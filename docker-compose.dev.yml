version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: bits-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: bits_platform_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - bits-dev-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: bits-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    networks:
      - bits-dev-network

  # Backend API (Development)
  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: bits-backend-dev
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
    environment:
      NODE_ENV: development
      PORT: 5000
      DATABASE_URL: ********************************************/bits_platform_dev
      REDIS_URL: redis://redis:6379
      JWT_SECRET: dev-jwt-secret
      SESSION_SECRET: dev-session-secret
      DEBUG: "true"
      SWAGGER_ENABLED: "true"
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    ports:
      - "5000:5000"
      - "9229:9229"  # Debug port
    networks:
      - bits-dev-network
    command: npm run dev

  # Adminer (Database management)
  adminer:
    image: adminer:latest
    container_name: bits-adminer
    restart: unless-stopped
    depends_on:
      - postgres
    ports:
      - "8080:8080"
    networks:
      - bits-dev-network

  # Redis Commander (Redis management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: bits-redis-commander
    restart: unless-stopped
    depends_on:
      - redis
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - bits-dev-network

# Volumes
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

# Networks
networks:
  bits-dev-network:
    driver: bridge