#!/usr/bin/env node

/**
 * Jupyter Integration Demo
 * 
 * This script demonstrates how to use the Jupyter integration API
 * to create kernels, execute code, and manage sessions.
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3001/api/jupyter';

// For demo purposes, we'll use the test endpoints that don't require authentication
// In production, you would use proper JWT tokens

class JupyterDemo {
  constructor() {
    this.api = axios.create({
      baseURL: BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  async testConnection() {
    console.log('🔍 Testing Jupyter connection...');
    try {
      const response = await this.api.get('/test-connection');
      console.log('✅ Connection successful:', response.data);
      return true;
    } catch (error) {
      console.error('❌ Connection failed:', error.response?.data || error.message);
      return false;
    }
  }

  async listKernelSpecs() {
    console.log('\n📋 Listing available kernel specifications...');
    try {
      // Note: This would require authentication in production
      const response = await axios.get('http://localhost:8888/api/kernelspecs', {
        headers: {
          'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33'
        }
      });
      console.log('✅ Available kernels:', Object.keys(response.data.kernelspecs));
      return response.data;
    } catch (error) {
      console.error('❌ Failed to list kernelspecs:', error.response?.data || error.message);
      return null;
    }
  }

  async createKernel() {
    console.log('\n🚀 Creating a new Python kernel...');
    try {
      // Direct API call to Jupyter server for demo
      const response = await axios.post('http://localhost:8888/api/kernels', 
        { name: 'python3' },
        {
          headers: {
            'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33',
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('✅ Kernel created:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to create kernel:', error.response?.data || error.message);
      return null;
    }
  }

  async listKernels() {
    console.log('\n📋 Listing running kernels...');
    try {
      const response = await axios.get('http://localhost:8888/api/kernels', {
        headers: {
          'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33'
        }
      });
      console.log('✅ Running kernels:', response.data.length);
      response.data.forEach(kernel => {
        console.log(`  - ${kernel.id} (${kernel.name}) - ${kernel.execution_state}`);
      });
      return response.data;
    } catch (error) {
      console.error('❌ Failed to list kernels:', error.response?.data || error.message);
      return [];
    }
  }

  async deleteKernel(kernelId) {
    console.log(`\n🗑️  Deleting kernel ${kernelId}...`);
    try {
      await axios.delete(`http://localhost:8888/api/kernels/${kernelId}`, {
        headers: {
          'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33'
        }
      });
      console.log('✅ Kernel deleted successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to delete kernel:', error.response?.data || error.message);
      return false;
    }
  }

  async createSession() {
    console.log('\n📝 Creating a new session...');
    try {
      const sessionData = {
        path: 'demo-notebook.ipynb',
        type: 'notebook',
        kernel: { name: 'python3' }
      };

      const response = await axios.post('http://localhost:8888/api/sessions', 
        sessionData,
        {
          headers: {
            'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33',
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('✅ Session created:', response.data.id);
      console.log(`   Notebook path: ${response.data.path}`);
      console.log(`   Kernel ID: ${response.data.kernel.id}`);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to create session:', error.response?.data || error.message);
      return null;
    }
  }

  async listSessions() {
    console.log('\n📋 Listing active sessions...');
    try {
      const response = await axios.get('http://localhost:8888/api/sessions', {
        headers: {
          'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33'
        }
      });
      console.log('✅ Active sessions:', response.data.length);
      response.data.forEach(session => {
        console.log(`  - ${session.id}: ${session.path} (kernel: ${session.kernel.id})`);
      });
      return response.data;
    } catch (error) {
      console.error('❌ Failed to list sessions:', error.response?.data || error.message);
      return [];
    }
  }

  async deleteSession(sessionId) {
    console.log(`\n🗑️  Deleting session ${sessionId}...`);
    try {
      await axios.delete(`http://localhost:8888/api/sessions/${sessionId}`, {
        headers: {
          'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33'
        }
      });
      console.log('✅ Session deleted successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to delete session:', error.response?.data || error.message);
      return false;
    }
  }

  async runDemo() {
    console.log('🎯 Starting Jupyter Integration Demo\n');

    // Test connection
    const connected = await this.testConnection();
    if (!connected) {
      console.log('❌ Cannot proceed without Jupyter connection');
      return;
    }

    // List available kernels
    await this.listKernelSpecs();

    // List current kernels
    await this.listKernels();

    // Create a new kernel
    const kernel = await this.createKernel();
    if (!kernel) return;

    // List kernels again to see the new one
    await this.listKernels();

    // Create a session
    const session = await this.createSession();
    if (!session) {
      await this.deleteKernel(kernel.id);
      return;
    }

    // List sessions
    await this.listSessions();

    // Clean up
    console.log('\n🧹 Cleaning up...');
    await this.deleteSession(session.id);
    await this.deleteKernel(kernel.id);

    console.log('\n✅ Demo completed successfully!');
    console.log('\n📚 Next steps:');
    console.log('1. Use proper JWT authentication for production');
    console.log('2. Implement WebSocket connections for real-time code execution');
    console.log('3. Create user-specific workspaces using the /workspace endpoint');
    console.log('4. Integrate with your frontend for a complete Jupyter experience');
  }
}

// Run the demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const demo = new JupyterDemo();
  demo.runDemo().catch(console.error);
}

export default JupyterDemo;
