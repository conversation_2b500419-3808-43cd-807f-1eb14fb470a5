<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jupyter Integration Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code-input {
            width: 100%;
            height: 100px;
            font-family: 'Courier New', monospace;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .workspace-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Jupyter Integration Demo</h1>
        
        <div class="section">
            <h2>🔍 Connection Status</h2>
            <button onclick="testConnection()">Test Jupyter Connection</button>
            <div id="connectionStatus"></div>
        </div>

        <div class="section">
            <h2>🎯 Workspace Management</h2>
            <div>
                <label for="projectId">Project ID:</label>
                <input type="text" id="projectId" value="demo-project-123" style="margin: 0 10px; padding: 5px;">
                <button onclick="createWorkspace()">Create Workspace</button>
            </div>
            <div id="workspaceInfo"></div>
        </div>

        <div class="section">
            <h2>⚡ Code Execution</h2>
            <div>
                <label for="kernelId">Kernel ID:</label>
                <input type="text" id="kernelId" placeholder="Will be filled after creating workspace" style="margin: 0 10px; padding: 5px; width: 300px;">
            </div>
            <textarea id="codeInput" class="code-input" placeholder="Enter Python code here...">
# Welcome to Jupyter Integration Demo!
import sys
print(f"Python version: {sys.version}")

# Try some data science
import pandas as pd
import numpy as np

# Create a simple dataset
data = {
    'name': ['Alice', 'Bob', 'Charlie'],
    'age': [25, 30, 35],
    'score': [85, 92, 78]
}

df = pd.DataFrame(data)
print("\nDataFrame:")
print(df)

print(f"\nMean age: {df['age'].mean()}")
print(f"Max score: {df['score'].max()}")
            </textarea>
            <div>
                <button onclick="executeCode()">Execute Code</button>
                <button onclick="clearOutput()">Clear Output</button>
            </div>
            <div id="executionOutput"></div>
        </div>

        <div class="section">
            <h2>📊 Kernel Management</h2>
            <button onclick="listKernels()">List Running Kernels</button>
            <button onclick="createKernel()">Create New Kernel</button>
            <div id="kernelList"></div>
        </div>

        <div class="section">
            <h2>📝 Session Management</h2>
            <button onclick="listSessions()">List Active Sessions</button>
            <div id="sessionList"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/jupyter';
        
        // For demo purposes, we'll use test endpoints that don't require authentication
        // In production, you would use proper JWT tokens
        
        let currentWorkspace = null;

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testConnection() {
            try {
                showStatus('connectionStatus', '🔄 Testing connection...', 'info');
                
                const response = await fetch(`${API_BASE}/test-connection`);
                const data = await response.json();
                
                if (response.ok) {
                    showStatus('connectionStatus', 
                        `✅ Connected to Jupyter ${data.jupyter_version} at ${data.server_url}`, 
                        'success'
                    );
                } else {
                    showStatus('connectionStatus', `❌ Connection failed: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('connectionStatus', `❌ Connection error: ${error.message}`, 'error');
            }
        }

        async function createWorkspace() {
            const projectId = document.getElementById('projectId').value;
            if (!projectId) {
                showStatus('workspaceInfo', '❌ Please enter a project ID', 'error');
                return;
            }

            try {
                showStatus('workspaceInfo', '🔄 Creating workspace...', 'info');
                
                // For demo, we'll create a session directly with Jupyter API
                // In production, use your authenticated backend endpoint
                const sessionData = {
                    path: `projects/${projectId}/workspace.ipynb`,
                    type: 'notebook',
                    kernel: { name: 'python3' }
                };

                const response = await fetch('http://localhost:8888/api/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33'
                    },
                    body: JSON.stringify(sessionData)
                });

                if (response.ok) {
                    const workspace = await response.json();
                    currentWorkspace = workspace;
                    
                    // Auto-fill kernel ID
                    document.getElementById('kernelId').value = workspace.kernel.id;
                    
                    showStatus('workspaceInfo', 
                        `✅ Workspace created successfully!
                        <div class="workspace-info">
                            <strong>Session ID:</strong> ${workspace.id}<br>
                            <strong>Notebook Path:</strong> ${workspace.path}<br>
                            <strong>Kernel ID:</strong> ${workspace.kernel.id}<br>
                            <strong>Kernel State:</strong> ${workspace.kernel.execution_state}
                        </div>`, 
                        'success'
                    );
                } else {
                    const error = await response.json();
                    showStatus('workspaceInfo', `❌ Failed to create workspace: ${error.message}`, 'error');
                }
            } catch (error) {
                showStatus('workspaceInfo', `❌ Workspace creation error: ${error.message}`, 'error');
            }
        }

        async function executeCode() {
            const kernelId = document.getElementById('kernelId').value;
            const code = document.getElementById('codeInput').value;
            
            if (!kernelId || !code) {
                showStatus('executionOutput', '❌ Please provide kernel ID and code', 'error');
                return;
            }

            try {
                showStatus('executionOutput', '🔄 Executing code...', 'info');
                
                // For demo, we'll show a simulated execution
                // In production, use your backend's execute endpoint with WebSocket support
                const output = `Executing code in kernel: ${kernelId}

${code}

--- Output ---
Python version: 3.8.10 (default, Nov 14 2022, 12:59:47) 
[GCC 9.4.0] on linux

DataFrame:
      name  age  score
0    Alice   25     85
1      Bob   30     92
2  Charlie   35     78

Mean age: 30.0
Max score: 92

✅ Code executed successfully!`;

                document.getElementById('executionOutput').innerHTML = 
                    `<div class="output">${output}</div>`;
                    
            } catch (error) {
                showStatus('executionOutput', `❌ Execution error: ${error.message}`, 'error');
            }
        }

        function clearOutput() {
            document.getElementById('executionOutput').innerHTML = '';
        }

        async function listKernels() {
            try {
                showStatus('kernelList', '🔄 Loading kernels...', 'info');
                
                const response = await fetch('http://localhost:8888/api/kernels', {
                    headers: {
                        'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33'
                    }
                });

                if (response.ok) {
                    const kernels = await response.json();
                    let html = `<h3>Running Kernels (${kernels.length}):</h3>`;
                    
                    if (kernels.length === 0) {
                        html += '<p>No running kernels</p>';
                    } else {
                        html += '<ul>';
                        kernels.forEach(kernel => {
                            html += `<li><strong>${kernel.id}</strong> (${kernel.name}) - ${kernel.execution_state}</li>`;
                        });
                        html += '</ul>';
                    }
                    
                    document.getElementById('kernelList').innerHTML = html;
                } else {
                    showStatus('kernelList', '❌ Failed to load kernels', 'error');
                }
            } catch (error) {
                showStatus('kernelList', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function createKernel() {
            try {
                const response = await fetch('http://localhost:8888/api/kernels', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33'
                    },
                    body: JSON.stringify({ name: 'python3' })
                });

                if (response.ok) {
                    const kernel = await response.json();
                    showStatus('kernelList', `✅ Created kernel: ${kernel.id}`, 'success');
                    listKernels(); // Refresh the list
                } else {
                    showStatus('kernelList', '❌ Failed to create kernel', 'error');
                }
            } catch (error) {
                showStatus('kernelList', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function listSessions() {
            try {
                showStatus('sessionList', '🔄 Loading sessions...', 'info');
                
                const response = await fetch('http://localhost:8888/api/sessions', {
                    headers: {
                        'Authorization': 'token 9ea1a0832ea66ab849f4782d5812ca242b6cdee948ba7a33'
                    }
                });

                if (response.ok) {
                    const sessions = await response.json();
                    let html = `<h3>Active Sessions (${sessions.length}):</h3>`;
                    
                    if (sessions.length === 0) {
                        html += '<p>No active sessions</p>';
                    } else {
                        html += '<ul>';
                        sessions.forEach(session => {
                            html += `<li><strong>${session.id}</strong>: ${session.path} (kernel: ${session.kernel.id})</li>`;
                        });
                        html += '</ul>';
                    }
                    
                    document.getElementById('sessionList').innerHTML = html;
                } else {
                    showStatus('sessionList', '❌ Failed to load sessions', 'error');
                }
            } catch (error) {
                showStatus('sessionList', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Initialize the demo
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
