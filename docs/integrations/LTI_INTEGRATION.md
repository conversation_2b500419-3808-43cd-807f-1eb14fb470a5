# LTI Integration Specification - BITS DataScience Platform

**Document Type:** Integration Specification  
**Target:** Frontend Integration (Figma Make, React, Vue, etc.)  
**Version:** 1.0  
**Last Updated:** August 18, 2025  

---

## 📋 **Integration Overview**

This document provides complete integration specifications for the LTI (Learning Tools Interoperability) functionality in the BITS DataScience Platform. It includes all API endpoints, request/response formats, authentication flows, and frontend integration examples.

---

## 🔐 **Authentication & Authorization**

### **LTI Session Management**

#### **Session Structure**
```javascript
// LTI Session Object
{
  user: {
    id: "uuid",
    email: "<EMAIL>",
    name: "<PERSON>",
    roles: ["instructor", "student"]
  },
  context: {
    id: "uuid",
    contextId: "course-123",
    title: "Data Science Fundamentals",
    label: "CS101"
  },
  resourceLink: {
    id: "uuid",
    resourceLinkId: "link-456",
    title: "Project Assignment"
  },
  launchData: {
    messageType: "LtiResourceLinkRequest",
    version: "1.3.0",
    deploymentId: "deployment-789"
  }
}
```

#### **Session Validation**
```javascript
// Check if user has LTI session
const hasLTISession = req.session.ltiLaunchData !== undefined;

// Get user role from LTI session
const userRole = req.session.user?.roles?.[0] || 'student';

// Check if user is instructor
const isInstructor = req.session.user?.roles?.includes('instructor');
```

---

## 🌐 **API Endpoints**

### **1. LTI Configuration**

#### **Get LTI Tool Configuration**
```http
GET /api/lti/config
```

**Response:**
```json
{
  "title": "BITS DataScience Projects Platform",
  "description": "Interactive data science projects and assignments platform for BITS Pilani",
  "target_link_uri": "https://your-domain.com/api/lti/oidc/init",
  "oidc_initiation_url": "https://your-domain.com/api/lti/oidc/init",
  "public_jwk_url": "https://your-domain.com/.well-known/jwks.json",
  "scopes": [
    "openid",
    "https://purl.imsglobal.org/spec/lti-ags/scope/lineitem",
    "https://purl.imsglobal.org/spec/lti-ags/scope/score",
    "https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly"
  ],
  "extensions": [
    {
      "domain": "your-domain.com",
      "tool_id": "bits-datascience-platform",
      "platform": "bits.edu",
      "settings": {
        "text": "BITS DataScience Platform",
        "icon_url": "https://your-domain.com/assets/bits-logo.png",
        "selection_width": 800,
        "selection_height": 600
      },
      "privacy_level": "public"
    }
  ],
  "custom_fields": {
    "project_id": "$ResourceLink.id",
    "context_id": "$Context.id",
    "user_id": "$User.id"
  },
  "claims": [
    "iss",
    "aud",
    "exp",
    "iat",
    "nonce",
    "https://purl.imsglobal.org/spec/lti/claim/deployment_id",
    "https://purl.imsglobal.org/spec/lti/claim/message_type",
    "https://purl.imsglobal.org/spec/lti/claim/version",
    "https://purl.imsglobal.org/spec/lti/claim/resource_link",
    "https://purl.imsglobal.org/spec/lti/claim/context",
    "https://purl.imsglobal.org/spec/lti/claim/tool_platform",
    "https://purl.imsglobal.org/spec/lti/claim/roles",
    "https://purl.imsglobal.org/spec/lti/claim/custom",
    "https://purl.imsglobal.org/spec/lti-ags/claim/assignmentsgradeservice",
    "https://purl.imsglobal.org/spec/lti-nrps/claim/namesroleservice",
    "https://purl.imsglobal.org/spec/lti-dl/claim/deep_linking_settings"
  ]
}
```

### **2. LTI Session Management**

#### **Get Current LTI Session**
```http
GET /api/lti/session
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "session": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "John Doe",
      "roles": ["instructor"]
    },
    "context": {
      "id": "uuid",
      "contextId": "course-123",
      "title": "Data Science Fundamentals",
      "label": "CS101"
    },
    "resourceLink": {
      "id": "uuid",
      "resourceLinkId": "link-456",
      "title": "Project Assignment"
    },
    "launchData": {
      "messageType": "LtiResourceLinkRequest",
      "version": "1.3.0",
      "deploymentId": "deployment-789"
    }
  }
}
```

**Error Response:**
```json
{
  "error": "No LTI Session",
  "message": "No active LTI session found"
}
```

### **3. Course Roster Management**

#### **Get Course Roster**
```http
GET /api/lti/roster/{contextId}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "roster": [
    {
      "user_id": "user-123",
      "name": "John Doe",
      "email": "<EMAIL>",
      "roles": ["Learner"],
      "status": "Active"
    },
    {
      "user_id": "user-456",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "roles": ["Instructor"],
      "status": "Active"
    }
  ]
}
```

### **4. Grade Management**

#### **Submit Grade**
```http
POST /api/lti/grades
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "resourceLinkId": "link-456",
  "userId": "uuid",
  "score": 85.5,
  "maxScore": 100
}
```

**Response:**
```json
{
  "success": true,
  "message": "Grade submitted successfully",
  "result": {
    "id": "score-789",
    "userId": "user-123",
    "scoreGiven": 85.5,
    "scoreMaximum": 100,
    "activityProgress": "Completed",
    "gradingProgress": "FullyGraded",
    "timestamp": "2025-08-18T10:30:00Z"
  }
}
```

**Error Response:**
```json
{
  "error": "Grade Submission Failed",
  "message": "Could not submit grade to platform"
}
```

### **5. Platform Management (Admin Only)**

#### **Register New Platform**
```http
POST /api/lti/platforms
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "platformName": "Brightspace D2L",
  "platformId": "https://bitspilani.brightspacedemo.com/",
  "clientId": "bits-datascience-platform",
  "authLoginUrl": "https://bitspilani.brightspacedemo.com/d2l/auth/api/token",
  "authTokenUrl": "https://bitspilani.brightspacedemo.com/d2l/auth/token",
  "keySetUrl": "https://bitspilani.brightspacedemo.com/.well-known/jwks.json",
  "deploymentId": "deployment-123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Platform registered successfully",
  "platform": {
    "id": "uuid",
    "platformName": "Brightspace D2L",
    "platformId": "https://bitspilani.brightspacedemo.com/",
    "clientId": "bits-datascience-platform",
    "isActive": true
  }
}
```

#### **Cleanup Expired Sessions**
```http
POST /api/lti/cleanup
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "success": true,
  "message": "Cleaned up 15 expired sessions",
  "deletedCount": 15
}
```

---

## 🎨 **Frontend Integration Examples**

### **React Component Examples**

#### **LTI Session Provider**
```jsx
import React, { createContext, useContext, useState, useEffect } from 'react';

const LTIContext = createContext();

export const LTIProvider = ({ children }) => {
  const [ltiSession, setLtiSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    checkLTISession();
  }, []);

  const checkLTISession = async () => {
    try {
      const response = await fetch('/api/lti/session', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setLtiSession(data.session);
      } else {
        setLtiSession(null);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const submitGrade = async (resourceLinkId, userId, score, maxScore = 100) => {
    try {
      const response = await fetch('/api/lti/grades', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          resourceLinkId,
          userId,
          score,
          maxScore
        })
      });

      if (!response.ok) {
        throw new Error('Grade submission failed');
      }

      return await response.json();
    } catch (err) {
      throw err;
    }
  };

  const getCourseRoster = async (contextId) => {
    try {
      const response = await fetch(`/api/lti/roster/${contextId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch roster');
      }

      return await response.json();
    } catch (err) {
      throw err;
    }
  };

  const value = {
    ltiSession,
    loading,
    error,
    submitGrade,
    getCourseRoster,
    refreshSession: checkLTISession
  };

  return (
    <LTIContext.Provider value={value}>
      {children}
    </LTIContext.Provider>
  );
};

export const useLTI = () => {
  const context = useContext(LTIContext);
  if (!context) {
    throw new Error('useLTI must be used within an LTIProvider');
  }
  return context;
};
```

#### **LTI Dashboard Component**
```jsx
import React from 'react';
import { useLTI } from './LTIProvider';

const LTIDashboard = () => {
  const { ltiSession, loading, error } = useLTI();

  if (loading) {
    return <div>Loading LTI session...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!ltiSession) {
    return <div>No LTI session found. Please launch from your LMS.</div>;
  }

  const { user, context, resourceLink } = ltiSession;
  const isInstructor = user.roles.includes('instructor');

  return (
    <div className="lti-dashboard">
      <header className="lti-header">
        <h1>BITS DataScience Platform</h1>
        <div className="user-info">
          <span>Welcome, {user.name}</span>
          <span>Role: {user.roles.join(', ')}</span>
        </div>
      </header>

      <div className="lti-context">
        <h2>Course: {context.title}</h2>
        <p>Context ID: {context.contextId}</p>
        {resourceLink && (
          <p>Assignment: {resourceLink.title}</p>
        )}
      </div>

      <div className="lti-content">
        {isInstructor ? (
          <InstructorView context={context} />
        ) : (
          <StudentView context={context} />
        )}
      </div>
    </div>
  );
};

const InstructorView = ({ context }) => {
  const { getCourseRoster } = useLTI();
  const [roster, setRoster] = useState([]);
  const [loading, setLoading] = useState(false);

  const loadRoster = async () => {
    setLoading(true);
    try {
      const response = await getCourseRoster(context.contextId);
      setRoster(response.roster);
    } catch (err) {
      console.error('Failed to load roster:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRoster();
  }, [context.contextId]);

  return (
    <div className="instructor-view">
      <h3>Course Roster</h3>
      {loading ? (
        <p>Loading roster...</p>
      ) : (
        <div className="roster-list">
          {roster.map((member) => (
            <div key={member.user_id} className="roster-member">
              <span>{member.name}</span>
              <span>{member.email}</span>
              <span>{member.roles.join(', ')}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const StudentView = ({ context }) => {
  return (
    <div className="student-view">
      <h3>Your Projects</h3>
      <p>Course: {context.title}</p>
      {/* Add student-specific content */}
    </div>
  );
};

export default LTIDashboard;
```

#### **Grade Submission Component**
```jsx
import React, { useState } from 'react';
import { useLTI } from './LTIProvider';

const GradeSubmission = ({ resourceLinkId, userId, maxScore = 100 }) => {
  const { submitGrade } = useLTI();
  const [score, setScore] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      const response = await submitGrade(resourceLinkId, userId, parseFloat(score), maxScore);
      setResult(response);
    } catch (err) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="grade-submission">
      <h3>Submit Grade</h3>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="score">Score:</label>
          <input
            type="number"
            id="score"
            value={score}
            onChange={(e) => setScore(e.target.value)}
            min="0"
            max={maxScore}
            step="0.1"
            required
          />
          <span>/ {maxScore}</span>
        </div>

        <button type="submit" disabled={submitting}>
          {submitting ? 'Submitting...' : 'Submit Grade'}
        </button>
      </form>

      {error && (
        <div className="error">
          Error: {error}
        </div>
      )}

      {result && (
        <div className="success">
          <h4>Grade Submitted Successfully</h4>
          <p>Score: {result.result.scoreGiven}/{result.result.scoreMaximum}</p>
          <p>Status: {result.result.gradingProgress}</p>
          <p>Timestamp: {new Date(result.result.timestamp).toLocaleString()}</p>
        </div>
      )}
    </div>
  );
};

export default GradeSubmission;
```

### **Vue.js Component Examples**

#### **LTI Session Store (Pinia)**
```javascript
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useLTIStore = defineStore('lti', () => {
  const session = ref(null);
  const loading = ref(false);
  const error = ref(null);

  const hasSession = computed(() => session.value !== null);
  const isInstructor = computed(() => 
    session.value?.user?.roles?.includes('instructor') || false
  );
  const isStudent = computed(() => 
    session.value?.user?.roles?.includes('student') || false
  );

  const checkSession = async () => {
    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/lti/session', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        session.value = data.session;
      } else {
        session.value = null;
      }
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  const submitGrade = async (resourceLinkId, userId, score, maxScore = 100) => {
    try {
      const response = await fetch('/api/lti/grades', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          resourceLinkId,
          userId,
          score,
          maxScore
        })
      });

      if (!response.ok) {
        throw new Error('Grade submission failed');
      }

      return await response.json();
    } catch (err) {
      throw err;
    }
  };

  const getCourseRoster = async (contextId) => {
    try {
      const response = await fetch(`/api/lti/roster/${contextId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch roster');
      }

      return await response.json();
    } catch (err) {
      throw err;
    }
  };

  return {
    session,
    loading,
    error,
    hasSession,
    isInstructor,
    isStudent,
    checkSession,
    submitGrade,
    getCourseRoster
  };
});
```

#### **LTI Dashboard Vue Component**
```vue
<template>
  <div class="lti-dashboard">
    <div v-if="loading" class="loading">
      Loading LTI session...
    </div>

    <div v-else-if="error" class="error">
      Error: {{ error }}
    </div>

    <div v-else-if="!hasSession" class="no-session">
      No LTI session found. Please launch from your LMS.
    </div>

    <div v-else class="dashboard-content">
      <header class="lti-header">
        <h1>BITS DataScience Platform</h1>
        <div class="user-info">
          <span>Welcome, {{ session.user.name }}</span>
          <span>Role: {{ session.user.roles.join(', ') }}</span>
        </div>
      </header>

      <div class="lti-context">
        <h2>Course: {{ session.context.title }}</h2>
        <p>Context ID: {{ session.context.contextId }}</p>
        <p v-if="session.resourceLink">
          Assignment: {{ session.resourceLink.title }}
        </p>
      </div>

      <div class="lti-content">
        <InstructorView 
          v-if="isInstructor" 
          :context="session.context" 
        />
        <StudentView 
          v-else 
          :context="session.context" 
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useLTIStore } from '@/stores/lti';
import InstructorView from './InstructorView.vue';
import StudentView from './StudentView.vue';

const ltiStore = useLTIStore();
const { session, loading, error, hasSession, isInstructor, checkSession } = ltiStore;

onMounted(() => {
  checkSession();
});
</script>

<style scoped>
.lti-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.lti-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.lti-context {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.loading, .error, .no-session {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

.error {
  color: #dc3545;
}

.no-session {
  color: #6c757d;
}
</style>
```

---

## 🔧 **Configuration & Environment Variables**

### **Required Environment Variables**
```env
# LTI Configuration
LTI_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"
LTI_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nYOUR_PUBLIC_KEY_HERE\n-----END PUBLIC KEY-----"
LTI_KEY_ID="bits-datascience-key-1"
LTI_TOOL_URL="https://your-domain.com"

# Session Configuration
SESSION_SECRET="your-session-secret"
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE="lax"

# CORS Configuration
CORS_ORIGIN="https://bitspilani.brightspacedemo.com"
```

### **Frontend Configuration**
```javascript
// config/lti.js
export const LTI_CONFIG = {
  API_BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:5001',
  ENDPOINTS: {
    SESSION: '/api/lti/session',
    GRADES: '/api/lti/grades',
    ROSTER: '/api/lti/roster',
    CONFIG: '/api/lti/config'
  },
  ROLES: {
    INSTRUCTOR: 'instructor',
    STUDENT: 'student',
    TEACHING_ASSISTANT: 'teaching_assistant',
    ADMIN: 'administrator'
  }
};
```

---

## 🎯 **Integration Checklist**

### **Frontend Integration Steps**

1. **✅ Set up LTI Session Management**
   - Implement session checking
   - Handle session expiration
   - Provide fallback for non-LTI users

2. **✅ Create Role-Based UI Components**
   - Instructor dashboard
   - Student dashboard
   - Teaching assistant view

3. **✅ Implement Grade Submission**
   - Grade input forms
   - Validation
   - Success/error handling

4. **✅ Add Course Roster Display**
   - Roster fetching
   - Member display
   - Role filtering

5. **✅ Handle LTI Launch Flow**
   - Session initialization
   - Context loading
   - User role mapping

6. **✅ Implement Error Handling**
   - Network errors
   - Authentication errors
   - Session errors

### **Testing Checklist**

1. **✅ Test LTI Session Creation**
2. **✅ Test Role-Based Access**
3. **✅ Test Grade Submission**
4. **✅ Test Roster Retrieval**
5. **✅ Test Error Scenarios**
6. **✅ Test Session Expiration**

---

## 📊 **Data Flow Diagrams**

### **LTI Launch Flow**
```
Brightspace → OIDC Init → Platform Auth → OIDC Callback → Session Creation → Dashboard
```

### **Grade Submission Flow**
```
Instructor → Grade Input → API Call → LTI Service → Brightspace AGS → Gradebook
```

### **Roster Sync Flow**
```
Dashboard → Roster Request → LTI Service → Brightspace NRPS → Roster Display
```

---

## 🔗 **Related Documentation**

- [LTI Brightspace Setup Guide](../LTI_BRIGHTSPACE_SETUP.md)
- [API Documentation](../API_DOCUMENTATION.md)
- [Authentication Guide](../AUTHENTICATION_INTEGRATION.md)
- [Project Management Integration](./PROJECT_MANAGEMENT_INTEGRATION.md)

---

**Document Version:** 1.0  
**Last Updated:** August 18, 2025  
**Compatibility:** LTI 1.3, Brightspace D2L 20.21+
