# Project Management Integration Guide

## Overview
This document provides complete integration specifications for the BITS DataScience Platform project management system, including course management, project creation, assignment, and submission handling.

## Base Configuration
- **Base URL**: `http://localhost:5001` (Development) / `https://api.bits-datascience.edu` (Production)
- **Content-Type**: `application/json`
- **Authentication**: JWT <PERSON> (required for all endpoints)

---

## 1. Course Management

### 1.1 Get All Courses

#### Endpoint
```
GET /api/courses?page=1&limit=10&search=datascience&instructorId=instructor-123
```

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `search` (optional): Search by course name or description
- `instructorId` (optional): Filter by instructor
- `status` (optional): Filter by status (active|inactive|draft)

#### Request Headers
```http
Authorization: Bearer <access_token>
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "courses": [
    {
      "id": "uuid",
      "name": "Introduction to Data Science",
      "description": "Learn the fundamentals of data science",
      "code": "DS101",
      "instructorId": "uuid",
      "instructor": {
        "id": "uuid",
        "name": "Dr. John Doe",
        "email": "<EMAIL>"
      },
      "status": "active",
      "enrollmentCount": 45,
      "maxEnrollment": 50,
      "startDate": "2025-01-15T00:00:00.000Z",
      "endDate": "2025-05-15T00:00:00.000Z",
      "createdAt": "2025-08-17T18:00:00.000Z",
      "updatedAt": "2025-08-17T18:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### 1.2 Create Course

#### Endpoint
```
POST /api/courses
```

#### Request Format
```json
{
  "name": "Advanced Machine Learning",
  "description": "Deep dive into machine learning algorithms",
  "code": "DS201",
  "instructorId": "uuid",
  "maxEnrollment": 30,
  "startDate": "2025-02-01T00:00:00.000Z",
  "endDate": "2025-06-01T00:00:00.000Z",
  "syllabus": "Course syllabus content...",
  "prerequisites": ["DS101"],
  "status": "active"
}
```

#### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

#### Response Format (Success - 201)
```json
{
  "success": true,
  "course": {
    "id": "uuid",
    "name": "Advanced Machine Learning",
    "description": "Deep dive into machine learning algorithms",
    "code": "DS201",
    "instructorId": "uuid",
    "instructor": {
      "id": "uuid",
      "name": "Dr. John Doe",
      "email": "<EMAIL>"
    },
    "status": "active",
    "enrollmentCount": 0,
    "maxEnrollment": 30,
    "startDate": "2025-02-01T00:00:00.000Z",
    "endDate": "2025-06-01T00:00:00.000Z",
    "syllabus": "Course syllabus content...",
    "prerequisites": ["DS101"],
    "createdAt": "2025-08-17T18:00:00.000Z",
    "updatedAt": "2025-08-17T18:00:00.000Z"
  },
  "message": "Course created successfully"
}
```

### 1.3 Get Course Details

#### Endpoint
```
GET /api/courses/:courseId
```

#### Request Headers
```http
Authorization: Bearer <access_token>
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "course": {
    "id": "uuid",
    "name": "Introduction to Data Science",
    "description": "Learn the fundamentals of data science",
    "code": "DS101",
    "instructorId": "uuid",
    "instructor": {
      "id": "uuid",
      "name": "Dr. John Doe",
      "email": "<EMAIL>"
    },
    "status": "active",
    "enrollmentCount": 45,
    "maxEnrollment": 50,
    "startDate": "2025-01-15T00:00:00.000Z",
    "endDate": "2025-05-15T00:00:00.000Z",
    "syllabus": "Course syllabus content...",
    "prerequisites": [],
    "projects": [
      {
        "id": "uuid",
        "name": "Data Analysis Project",
        "description": "Analyze a real-world dataset",
        "dueDate": "2025-03-15T23:59:59.000Z",
        "status": "active"
      }
    ],
    "enrolledStudents": [
      {
        "id": "uuid",
        "name": "Alice Johnson",
        "email": "<EMAIL>",
        "enrolledAt": "2025-01-10T00:00:00.000Z"
      }
    ],
    "createdAt": "2025-08-17T18:00:00.000Z",
    "updatedAt": "2025-08-17T18:00:00.000Z"
  }
}
```

### 1.4 Enroll in Course

#### Endpoint
```
POST /api/courses/:courseId/enroll
```

#### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "enrollment": {
    "id": "uuid",
    "courseId": "uuid",
    "studentId": "uuid",
    "enrolledAt": "2025-08-17T18:00:00.000Z",
    "status": "active"
  },
  "message": "Successfully enrolled in course"
}
```

---

## 2. Project Management

### 2.1 Get All Projects

#### Endpoint
```
GET /api/projects?courseId=course-123&status=active&page=1&limit=10
```

#### Query Parameters
- `courseId` (optional): Filter by course
- `status` (optional): Filter by status (active|inactive|draft)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `search` (optional): Search by project name or description

#### Request Headers
```http
Authorization: Bearer <access_token>
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "projects": [
    {
      "id": "uuid",
      "name": "Data Analysis Project",
      "description": "Analyze a real-world dataset using Python",
      "courseId": "uuid",
      "course": {
        "id": "uuid",
        "name": "Introduction to Data Science",
        "code": "DS101"
      },
      "instructorId": "uuid",
      "instructor": {
        "id": "uuid",
        "name": "Dr. John Doe",
        "email": "<EMAIL>"
      },
      "status": "active",
      "dueDate": "2025-03-15T23:59:59.000Z",
      "maxScore": 100,
      "submissionCount": 25,
      "requirements": [
        "Python notebook with analysis",
        "Written report",
        "Presentation slides"
      ],
      "allowedFileTypes": [".ipynb", ".pdf", ".pptx"],
      "maxFileSize": 10485760,
      "createdAt": "2025-08-17T18:00:00.000Z",
      "updatedAt": "2025-08-17T18:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 15,
    "pages": 2
  }
}
```

### 2.2 Create Project

#### Endpoint
```
POST /api/projects
```

#### Request Format
```json
{
  "name": "Machine Learning Classification",
  "description": "Build a classification model using scikit-learn",
  "courseId": "uuid",
  "dueDate": "2025-04-15T23:59:59.000Z",
  "maxScore": 100,
  "requirements": [
    "Jupyter notebook with model implementation",
    "Model evaluation report",
    "Code documentation"
  ],
  "allowedFileTypes": [".ipynb", ".pdf", ".py"],
  "maxFileSize": 10485760,
  "status": "active",
  "rubric": {
    "codeQuality": 25,
    "modelPerformance": 40,
    "documentation": 20,
    "presentation": 15
  }
}
```

#### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

#### Response Format (Success - 201)
```json
{
  "success": true,
  "project": {
    "id": "uuid",
    "name": "Machine Learning Classification",
    "description": "Build a classification model using scikit-learn",
    "courseId": "uuid",
    "course": {
      "id": "uuid",
      "name": "Advanced Machine Learning",
      "code": "DS201"
    },
    "instructorId": "uuid",
    "instructor": {
      "id": "uuid",
      "name": "Dr. John Doe",
      "email": "<EMAIL>"
    },
    "status": "active",
    "dueDate": "2025-04-15T23:59:59.000Z",
    "maxScore": 100,
    "submissionCount": 0,
    "requirements": [
      "Jupyter notebook with model implementation",
      "Model evaluation report",
      "Code documentation"
    ],
    "allowedFileTypes": [".ipynb", ".pdf", ".py"],
    "maxFileSize": 10485760,
    "rubric": {
      "codeQuality": 25,
      "modelPerformance": 40,
      "documentation": 20,
      "presentation": 15
    },
    "createdAt": "2025-08-17T18:00:00.000Z",
    "updatedAt": "2025-08-17T18:00:00.000Z"
  },
  "message": "Project created successfully"
}
```

### 2.3 Get Project Details

#### Endpoint
```
GET /api/projects/:projectId
```

#### Request Headers
```http
Authorization: Bearer <access_token>
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "project": {
    "id": "uuid",
    "name": "Data Analysis Project",
    "description": "Analyze a real-world dataset using Python",
    "courseId": "uuid",
    "course": {
      "id": "uuid",
      "name": "Introduction to Data Science",
      "code": "DS101"
    },
    "instructorId": "uuid",
    "instructor": {
      "id": "uuid",
      "name": "Dr. John Doe",
      "email": "<EMAIL>"
    },
    "status": "active",
    "dueDate": "2025-03-15T23:59:59.000Z",
    "maxScore": 100,
    "submissionCount": 25,
    "requirements": [
      "Python notebook with analysis",
      "Written report",
      "Presentation slides"
    ],
    "allowedFileTypes": [".ipynb", ".pdf", ".pptx"],
    "maxFileSize": 10485760,
    "rubric": {
      "codeQuality": 30,
      "analysis": 40,
      "documentation": 20,
      "presentation": 10
    },
    "submissions": [
      {
        "id": "uuid",
        "studentId": "uuid",
        "student": {
          "id": "uuid",
          "name": "Alice Johnson",
          "email": "<EMAIL>"
        },
        "status": "submitted",
        "submittedAt": "2025-03-14T15:30:00.000Z",
        "score": 85,
        "feedback": "Excellent analysis, good documentation"
      }
    ],
    "createdAt": "2025-08-17T18:00:00.000Z",
    "updatedAt": "2025-08-17T18:00:00.000Z"
  }
}
```

---

## 3. Submission Management

### 3.1 Get Submissions

#### Endpoint
```
GET /api/submissions?projectId=project-123&studentId=student-456&status=submitted
```

#### Query Parameters
- `projectId` (optional): Filter by project
- `studentId` (optional): Filter by student
- `status` (optional): Filter by status (draft|submitted|graded)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

#### Request Headers
```http
Authorization: Bearer <access_token>
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "submissions": [
    {
      "id": "uuid",
      "projectId": "uuid",
      "project": {
        "id": "uuid",
        "name": "Data Analysis Project",
        "dueDate": "2025-03-15T23:59:59.000Z"
      },
      "studentId": "uuid",
      "student": {
        "id": "uuid",
        "name": "Alice Johnson",
        "email": "<EMAIL>"
      },
      "status": "submitted",
      "submittedAt": "2025-03-14T15:30:00.000Z",
      "score": 85,
      "feedback": "Excellent analysis, good documentation",
      "files": [
        {
          "id": "uuid",
          "fileName": "analysis.ipynb",
          "fileSize": 2048576,
          "s3Key": "submissions/project-123/student-456/analysis.ipynb",
          "uploadedAt": "2025-03-14T15:30:00.000Z"
        }
      ],
      "rubricScores": {
        "codeQuality": 25,
        "analysis": 35,
        "documentation": 18,
        "presentation": 7
      },
      "createdAt": "2025-08-17T18:00:00.000Z",
      "updatedAt": "2025-08-17T18:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### 3.2 Create Submission

#### Endpoint
```
POST /api/submissions
```

#### Request Format
```json
{
  "projectId": "uuid",
  "files": [
    {
      "fileName": "analysis.ipynb",
      "fileSize": 2048576,
      "s3Key": "submissions/project-123/student-456/analysis.ipynb"
    },
    {
      "fileName": "report.pdf",
      "fileSize": 1048576,
      "s3Key": "submissions/project-123/student-456/report.pdf"
    }
  ],
  "comments": "Additional comments about the submission"
}
```

#### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

#### Response Format (Success - 201)
```json
{
  "success": true,
  "submission": {
    "id": "uuid",
    "projectId": "uuid",
    "project": {
      "id": "uuid",
      "name": "Data Analysis Project",
      "dueDate": "2025-03-15T23:59:59.000Z"
    },
    "studentId": "uuid",
    "student": {
      "id": "uuid",
      "name": "Alice Johnson",
      "email": "<EMAIL>"
    },
    "status": "draft",
    "submittedAt": null,
    "score": null,
    "feedback": null,
    "files": [
      {
        "id": "uuid",
        "fileName": "analysis.ipynb",
        "fileSize": 2048576,
        "s3Key": "submissions/project-123/student-456/analysis.ipynb",
        "uploadedAt": "2025-08-17T18:00:00.000Z"
      }
    ],
    "comments": "Additional comments about the submission",
    "createdAt": "2025-08-17T18:00:00.000Z",
    "updatedAt": "2025-08-17T18:00:00.000Z"
  },
  "message": "Submission created successfully"
}
```

### 3.3 Submit for Grading

#### Endpoint
```
POST /api/submissions/:submissionId/submit
```

#### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "submission": {
    "id": "uuid",
    "status": "submitted",
    "submittedAt": "2025-08-17T18:00:00.000Z"
  },
  "message": "Submission submitted for grading successfully"
}
```

---

## 4. Grading Management

### 4.1 Grade Submission

#### Endpoint
```
POST /api/grades
```

#### Request Format
```json
{
  "submissionId": "uuid",
  "score": 85,
  "feedback": "Excellent analysis, good documentation. Consider adding more visualizations.",
  "rubricScores": {
    "codeQuality": 25,
    "analysis": 35,
    "documentation": 18,
    "presentation": 7
  }
}
```

#### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

#### Response Format (Success - 201)
```json
{
  "success": true,
  "grade": {
    "id": "uuid",
    "submissionId": "uuid",
    "submission": {
      "id": "uuid",
      "projectId": "uuid",
      "studentId": "uuid",
      "status": "graded"
    },
    "score": 85,
    "feedback": "Excellent analysis, good documentation. Consider adding more visualizations.",
    "rubricScores": {
      "codeQuality": 25,
      "analysis": 35,
      "documentation": 18,
      "presentation": 7
    },
    "gradedBy": "uuid",
    "gradedAt": "2025-08-17T18:00:00.000Z",
    "createdAt": "2025-08-17T18:00:00.000Z",
    "updatedAt": "2025-08-17T18:00:00.000Z"
  },
  "message": "Grade submitted successfully"
}
```

### 4.2 Bulk Grade Submissions

#### Endpoint
```
POST /api/grades/bulk
```

#### Request Format
```json
{
  "grades": [
    {
      "submissionId": "uuid",
      "score": 85,
      "feedback": "Good work",
      "rubricScores": {
        "codeQuality": 25,
        "analysis": 35,
        "documentation": 18,
        "presentation": 7
      }
    },
    {
      "submissionId": "uuid",
      "score": 92,
      "feedback": "Excellent work",
      "rubricScores": {
        "codeQuality": 28,
        "analysis": 38,
        "documentation": 20,
        "presentation": 6
      }
    }
  ]
}
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "grades": [
    {
      "id": "uuid",
      "submissionId": "uuid",
      "score": 85,
      "feedback": "Good work",
      "gradedAt": "2025-08-17T18:00:00.000Z"
    }
  ],
  "message": "Bulk grading completed successfully"
}
```

---

## Frontend Integration Examples

### Course Management Component
```javascript
import React, { useState, useEffect } from 'react';

const CourseManager = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load courses
  const loadCourses = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        'http://localhost:5001/api/courses?page=1&limit=10',
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          }
        }
      );
      
      const data = await response.json();
      if (data.success) {
        setCourses(data.courses);
      }
    } catch (error) {
      setError('Failed to load courses');
    } finally {
      setLoading(false);
    }
  };

  // Create course
  const handleCreateCourse = async (courseData) => {
    try {
      const response = await fetch('http://localhost:5001/api/courses', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(courseData)
      });

      const data = await response.json();
      if (data.success) {
        await loadCourses(); // Refresh list
        return data.course;
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  useEffect(() => {
    loadCourses();
  }, []);

  return (
    <div className="course-manager">
      <h2>Course Management</h2>
      
      {loading && <div>Loading courses...</div>}
      {error && <div className="error">{error}</div>}

      <div className="course-list">
        {courses.map((course) => (
          <div key={course.id} className="course-item">
            <h3>{course.name}</h3>
            <p>{course.description}</p>
            <p>Code: {course.code}</p>
            <p>Instructor: {course.instructor.name}</p>
            <p>Enrollment: {course.enrollmentCount}/{course.maxEnrollment}</p>
            <p>Status: {course.status}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Project Management Component
```javascript
const ProjectManager = ({ courseId }) => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load projects for a course
  const loadProjects = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `http://localhost:5001/api/projects?courseId=${courseId}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          }
        }
      );
      
      const data = await response.json();
      if (data.success) {
        setProjects(data.projects);
      }
    } catch (error) {
      console.error('Failed to load projects:', error);
    } finally {
      setLoading(false);
    }
  };

  // Create project
  const handleCreateProject = async (projectData) => {
    try {
      const response = await fetch('http://localhost:5001/api/projects', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...projectData,
          courseId
        })
      });

      const data = await response.json();
      if (data.success) {
        await loadProjects(); // Refresh list
        return data.project;
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error;
    }
  };

  useEffect(() => {
    if (courseId) {
      loadProjects();
    }
  }, [courseId]);

  return (
    <div className="project-manager">
      <h2>Projects</h2>
      
      {loading && <div>Loading projects...</div>}

      <div className="project-list">
        {projects.map((project) => (
          <div key={project.id} className="project-item">
            <h3>{project.name}</h3>
            <p>{project.description}</p>
            <p>Due Date: {new Date(project.dueDate).toLocaleDateString()}</p>
            <p>Max Score: {project.maxScore}</p>
            <p>Submissions: {project.submissionCount}</p>
            <p>Status: {project.status}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Submission Component
```javascript
const SubmissionManager = ({ projectId, studentId }) => {
  const [submission, setSubmission] = useState(null);
  const [loading, setLoading] = useState(false);

  // Load submission
  const loadSubmission = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `http://localhost:5001/api/submissions?projectId=${projectId}&studentId=${studentId}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          }
        }
      );
      
      const data = await response.json();
      if (data.success && data.submissions.length > 0) {
        setSubmission(data.submissions[0]);
      }
    } catch (error) {
      console.error('Failed to load submission:', error);
    } finally {
      setLoading(false);
    }
  };

  // Submit for grading
  const handleSubmit = async () => {
    try {
      const response = await fetch(
        `http://localhost:5001/api/submissions/${submission.id}/submit`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();
      if (data.success) {
        await loadSubmission(); // Refresh submission
        alert('Submission submitted successfully!');
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Failed to submit:', error);
      alert('Failed to submit: ' + error.message);
    }
  };

  useEffect(() => {
    if (projectId && studentId) {
      loadSubmission();
    }
  }, [projectId, studentId]);

  return (
    <div className="submission-manager">
      <h2>My Submission</h2>
      
      {loading && <div>Loading submission...</div>}

      {submission && (
        <div className="submission-details">
          <h3>Submission Status: {submission.status}</h3>
          {submission.score && <p>Score: {submission.score}/{submission.project.maxScore}</p>}
          {submission.feedback && <p>Feedback: {submission.feedback}</p>}
          
          <div className="submission-files">
            <h4>Files:</h4>
            {submission.files.map((file) => (
              <div key={file.id} className="file-item">
                <span>{file.fileName}</span>
                <span>{(file.fileSize / 1024 / 1024).toFixed(2)} MB</span>
              </div>
            ))}
          </div>

          {submission.status === 'draft' && (
            <button onClick={handleSubmit}>Submit for Grading</button>
          )}
        </div>
      )}
    </div>
  );
};
```

---

## Error Handling

### Common Error Scenarios
1. **Permission Denied**: User lacks permissions for the operation
2. **Resource Not Found**: Course, project, or submission doesn't exist
3. **Validation Error**: Invalid data provided in request
4. **Conflict**: Resource already exists or is in invalid state

### Error Response Format
```json
{
  "success": false,
  "error": "Error message",
  "status": 400,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/projects",
  "method": "POST",
  "details": "Detailed error description"
}
```

---

## Testing Endpoints

### Test Project Management Flow
```bash
# 1. Create course
curl -X POST http://localhost:5001/api/courses \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Course",
    "description": "Test course description",
    "code": "TEST101",
    "instructorId": "instructor-uuid",
    "maxEnrollment": 30,
    "startDate": "2025-02-01T00:00:00.000Z",
    "endDate": "2025-06-01T00:00:00.000Z",
    "status": "active"
  }'

# 2. Create project
curl -X POST http://localhost:5001/api/projects \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Project",
    "description": "Test project description",
    "courseId": "course-uuid",
    "dueDate": "2025-04-15T23:59:59.000Z",
    "maxScore": 100,
    "status": "active"
  }'

# 3. Create submission
curl -X POST http://localhost:5001/api/submissions \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "projectId": "project-uuid",
    "files": [
      {
        "fileName": "test.ipynb",
        "fileSize": 1024,
        "s3Key": "submissions/test/test.ipynb"
      }
    ]
  }'

# 4. Submit for grading
curl -X POST http://localhost:5001/api/submissions/submission-uuid/submit \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json"

# 5. Grade submission
curl -X POST http://localhost:5001/api/grades \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "submissionId": "submission-uuid",
    "score": 85,
    "feedback": "Good work"
  }'
```

---

## Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Users can only access their own courses/projects/submissions
3. **Input Validation**: All inputs are validated on both client and server
4. **File Upload Security**: File types and sizes are validated
5. **Rate Limiting**: API endpoints are rate-limited to prevent abuse

---

**Last Updated**: August 17, 2025  
**Version**: 2.1.0  
**Maintainer**: BITS Pilani DataScience Team
