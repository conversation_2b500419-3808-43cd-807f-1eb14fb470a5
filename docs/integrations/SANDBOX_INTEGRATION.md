# Sandbox Environment Integration Guide

## Overview
This document provides complete integration specifications for the BITS DataScience Platform sandbox environment management system, including sandbox creation, management, and JupyterHub integration.

## Base Configuration
- **Base URL**: `http://localhost:5001` (Development) / `https://api.bits-datascience.edu` (Production)
- **Content-Type**: `application/json`
- **Authentication**: JWT <PERSON> (required for all endpoints)

---

## 1. Create Sandbox Environment

### Endpoint
```
POST /api/sandbox/create
```

### Request Format
```json
{
  "userId": "uuid",
  "projectId": "uuid",
  "resources": {
    "cpu": "1",
    "memory": "2Gi",
    "storage": "10Gi"
  },
  "environment": "python:3.9",
  "autoStartServer": true
}
```

### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "sandboxId": "sandbox-789",
  "workspace": {
    "workspaceId": "workspace-123",
    "s3Prefix": "workspaces/user-123/project-456"
  },
  "jupyterhubUser": "user-123",
  "jupyterhubUserCreated": true,
  "serverStarted": true,
  "message": "Sandbox created successfully for project 456"
}
```

### Response Format (Error - 400/500)
```json
{
  "success": false,
  "error": "Failed to create sandbox",
  "status": 500,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/sandbox/create",
  "method": "POST",
  "details": "Insufficient resources or JupyterHub unavailable"
}
```

### Frontend Integration Example
```javascript
const createSandbox = async (userId, projectId, resources) => {
  const response = await fetch('http://localhost:5001/api/sandbox/create', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      userId,
      projectId,
      resources: {
        cpu: resources.cpu || '1',
        memory: resources.memory || '2Gi',
        storage: resources.storage || '10Gi'
      },
      environment: 'python:3.9',
      autoStartServer: true
    })
  });

  const data = await response.json();
  
  if (data.success) {
    return data;
  } else {
    throw new Error(data.error);
  }
};
```

---

## 2. Get Sandbox Status

### Endpoint
```
GET /api/sandbox/:sandboxId/status
```

### Request Headers
```http
Authorization: Bearer <access_token>
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "status": "running",
  "resources": {
    "cpu": "1",
    "memory": "2Gi",
    "storage": "10Gi"
  },
  "uptime": "2h 30m",
  "url": "https://sandbox-789.example.com",
  "jupyterhubUrl": "https://jupyterhub.example.com/user/user-123",
  "workspace": {
    "workspaceId": "workspace-123",
    "s3Prefix": "workspaces/user-123/project-456"
  }
}
```

### Response Format (Error - 404)
```json
{
  "success": false,
  "error": "Sandbox not found",
  "status": 404,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/sandbox/sandbox-789/status",
  "method": "GET",
  "details": "The specified sandbox does not exist"
}
```

---

## 3. List User Sandboxes

### Endpoint
```
GET /api/sandbox/list?userId=user-123&projectId=project-456&status=running
```

### Query Parameters
- `userId` (optional): Filter by user ID
- `projectId` (optional): Filter by project ID
- `status` (optional): Filter by status (creating|running|stopped|error)

### Request Headers
```http
Authorization: Bearer <access_token>
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "userId": "user-123",
  "sandboxes": [
    {
      "sandboxId": "sandbox-789",
      "projectId": "project-456",
      "status": "running",
      "createdAt": "2025-08-17T12:00:00Z",
      "serverStatus": "active",
      "resources": {
        "cpu": "1",
        "memory": "2Gi",
        "storage": "10Gi"
      },
      "uptime": "2h 30m"
    }
  ],
  "count": 1,
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "pages": 1
  }
}
```

---

## 4. Update Sandbox Resources

### Endpoint
```
PUT /api/sandbox/:sandboxId/resources
```

### Request Format
```json
{
  "cpu": "2",
  "memory": "4Gi",
  "storage": "20Gi"
}
```

### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "sandboxId": "sandbox-789",
  "resources": {
    "cpu": "2",
    "memory": "4Gi",
    "storage": "20Gi"
  },
  "message": "Sandbox resources updated successfully"
}
```

---

## 5. Restart Sandbox

### Endpoint
```
POST /api/sandbox/:sandboxId/restart
```

### Request Format
```json
{
  "force": true
}
```

### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "sandboxId": "sandbox-789",
  "message": "Sandbox restarted successfully",
  "status": "restarting"
}
```

---

## 6. Scale Sandbox

### Endpoint
```
POST /api/sandbox/:sandboxId/scale
```

### Request Format
```json
{
  "replicas": 2,
  "autoScaling": true
}
```

### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "sandboxId": "sandbox-789",
  "scaling": {
    "replicas": 2,
    "autoScaling": true
  },
  "message": "Sandbox scaled successfully"
}
```

---

## 7. Get Sandbox Logs

### Endpoint
```
GET /api/sandbox/:sandboxId/logs?level=ERROR&limit=50&since=2025-08-17T10:00:00Z
```

### Query Parameters
- `level` (optional): Log level (INFO|WARN|ERROR)
- `limit` (optional): Number of log entries (default: 50)
- `since` (optional): ISO timestamp for filtering

### Request Headers
```http
Authorization: Bearer <access_token>
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "sandboxId": "sandbox-789",
  "logs": [
    {
      "timestamp": "2025-08-17T18:00:00.000Z",
      "level": "INFO",
      "message": "Sandbox created successfully",
      "source": "sandbox-orchestrator"
    },
    {
      "timestamp": "2025-08-17T18:01:00.000Z",
      "level": "INFO",
      "message": "JupyterHub server started",
      "source": "jupyterhub-admin"
    }
  ],
  "count": 2
}
```

---

## 8. Delete Sandbox

### Endpoint
```
DELETE /api/sandbox/:sandboxId
```

### Request Headers
```http
Authorization: Bearer <access_token>
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "sandboxId": "sandbox-789",
  "message": "Sandbox deleted successfully"
}
```

---

## 9. Sandbox Health Check

### Endpoint
```
GET /api/sandbox/health
```

### Request Headers
```http
Authorization: Bearer <access_token>
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "status": "healthy",
  "services": {
    "jupyterhub": "connected",
    "sandboxApi": "connected",
    "s3Workspace": "connected"
  },
  "timestamp": "2025-08-17T18:00:00.000Z"
}
```

---

## 10. JupyterHub User Management

### Create JupyterHub User
```
POST /api/jupyterhub/users
```

### Request Format
```json
{
  "name": "student-123",
  "admin": false,
  "groups": ["students"]
}
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "user": {
    "name": "student-123",
    "admin": false,
    "groups": ["students"],
    "created": true
  },
  "message": "JupyterHub user created successfully"
}
```

---

## 11. Start JupyterHub Server

### Endpoint
```
POST /api/jupyterhub/users/:username/servers/:serverName
```

### Request Format
```json
{
  "image": "python:3.9",
  "resources": {
    "cpu": "1",
    "memory": "2Gi"
  }
}
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "server": {
    "name": "default",
    "url": "https://jupyterhub.example.com/user/student-123",
    "status": "starting"
  },
  "message": "JupyterHub server started successfully"
}
```

---

## Frontend Integration Examples

### Sandbox Management Component
```javascript
import React, { useState, useEffect } from 'react';

const SandboxManager = ({ userId, projectId }) => {
  const [sandboxes, setSandboxes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Create sandbox
  const handleCreateSandbox = async () => {
    setLoading(true);
    try {
      const response = await createSandbox(userId, projectId, {
        cpu: '2',
        memory: '4Gi',
        storage: '20Gi'
      });
      
      // Refresh sandbox list
      await loadSandboxes();
      
      // Show success message
      alert('Sandbox created successfully!');
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Load sandboxes
  const loadSandboxes = async () => {
    try {
      const response = await fetch(
        `http://localhost:5001/api/sandbox/list?userId=${userId}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          }
        }
      );
      
      const data = await response.json();
      if (data.success) {
        setSandboxes(data.sandboxes);
      }
    } catch (error) {
      setError('Failed to load sandboxes');
    }
  };

  // Access sandbox
  const handleAccessSandbox = (sandboxId) => {
    // Open sandbox in new tab
    window.open(`https://sandbox-${sandboxId}.example.com`, '_blank');
  };

  // Monitor sandbox status
  const monitorSandbox = async (sandboxId) => {
    const response = await fetch(
      `http://localhost:5001/api/sandbox/${sandboxId}/status`,
      {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      }
    );
    
    const data = await response.json();
    return data;
  };

  useEffect(() => {
    loadSandboxes();
    
    // Poll for status updates
    const interval = setInterval(() => {
      sandboxes.forEach(async (sandbox) => {
        const status = await monitorSandbox(sandbox.sandboxId);
        // Update sandbox status in state
      });
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(interval);
  }, [userId]);

  return (
    <div className="sandbox-manager">
      <h2>Sandbox Environments</h2>
      
      <button 
        onClick={handleCreateSandbox} 
        disabled={loading}
      >
        {loading ? 'Creating...' : 'Create New Sandbox'}
      </button>

      {error && <div className="error">{error}</div>}

      <div className="sandbox-list">
        {sandboxes.map((sandbox) => (
          <div key={sandbox.sandboxId} className="sandbox-item">
            <h3>Sandbox {sandbox.sandboxId}</h3>
            <p>Status: {sandbox.status}</p>
            <p>Uptime: {sandbox.uptime}</p>
            <p>Resources: {sandbox.resources.cpu} CPU, {sandbox.resources.memory}</p>
            
            <div className="sandbox-actions">
              <button onClick={() => handleAccessSandbox(sandbox.sandboxId)}>
                Access
              </button>
              <button onClick={() => handleRestartSandbox(sandbox.sandboxId)}>
                Restart
              </button>
              <button onClick={() => handleDeleteSandbox(sandbox.sandboxId)}>
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Real-time Status Monitoring
```javascript
const useSandboxStatus = (sandboxId) => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStatus = async () => {
      try {
        const response = await fetch(
          `http://localhost:5001/api/sandbox/${sandboxId}/status`,
          {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
            }
          }
        );
        
        const data = await response.json();
        if (data.success) {
          setStatus(data);
        }
      } catch (error) {
        console.error('Failed to fetch sandbox status:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStatus();
    
    // Poll for status updates
    const interval = setInterval(fetchStatus, 10000); // Every 10 seconds
    
    return () => clearInterval(interval);
  }, [sandboxId]);

  return { status, loading };
};
```

---

## Error Handling

### Common Error Scenarios
1. **Resource Exhaustion**: When requested resources exceed available capacity
2. **JupyterHub Unavailable**: When JupyterHub service is down
3. **Network Issues**: When external sandbox API is unreachable
4. **Permission Denied**: When user lacks permissions for sandbox operations

### Error Response Format
```json
{
  "success": false,
  "error": "Error message",
  "status": 400,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/sandbox/create",
  "method": "POST",
  "details": "Detailed error description"
}
```

---

## Testing Endpoints

### Test Sandbox Flow
```bash
# 1. Create sandbox
curl -X POST http://localhost:5001/api/sandbox/create \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-123",
    "projectId": "project-456",
    "resources": {
      "cpu": "1",
      "memory": "2Gi",
      "storage": "10Gi"
    },
    "environment": "python:3.9",
    "autoStartServer": true
  }'

# 2. Check status
curl -X GET http://localhost:5001/api/sandbox/sandbox-789/status \
  -H "Authorization: Bearer <access_token>"

# 3. List sandboxes
curl -X GET "http://localhost:5001/api/sandbox/list?userId=user-123" \
  -H "Authorization: Bearer <access_token>"

# 4. Get logs
curl -X GET "http://localhost:5001/api/sandbox/sandbox-789/logs?level=INFO&limit=10" \
  -H "Authorization: Bearer <access_token>"

# 5. Update resources
curl -X PUT http://localhost:5001/api/sandbox/sandbox-789/resources \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "cpu": "2",
    "memory": "4Gi",
    "storage": "20Gi"
  }'

# 6. Restart sandbox
curl -X POST http://localhost:5001/api/sandbox/sandbox-789/restart \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{"force": true}'

# 7. Delete sandbox
curl -X DELETE http://localhost:5001/api/sandbox/sandbox-789 \
  -H "Authorization: Bearer <access_token>"
```

---

## Security Considerations

1. **Authentication**: All sandbox operations require valid JWT tokens
2. **Authorization**: Users can only access their own sandboxes
3. **Resource Limits**: Enforce resource quotas to prevent abuse
4. **Network Isolation**: Sandboxes are isolated from each other
5. **Audit Logging**: All sandbox operations are logged for security monitoring

---

**Last Updated**: August 17, 2025  
**Version**: 2.1.0  
**Maintainer**: BITS Pilani DataScience Team
