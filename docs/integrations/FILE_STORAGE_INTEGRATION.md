# File Storage Integration Guide

## Overview
This document provides complete integration specifications for the BITS DataScience Platform file storage system, including S3 integration, file upload/download, and workspace management.

## Base Configuration
- **Base URL**: `http://localhost:5001` (Development) / `https://api.bits-datascience.edu` (Production)
- **Content-Type**: `application/json`
- **Authentication**: JWT <PERSON> (required for all endpoints)

---

## 1. File Upload

### 1.1 Get Presigned Upload URL

#### Endpoint
```
POST /api/s3/upload
```

#### Request Format
```json
{
  "fileName": "analysis.ipynb",
  "fileType": "application/x-ipynb+json",
  "purpose": "submission",
  "metadata": {
    "projectId": "uuid",
    "submissionId": "uuid",
    "userId": "uuid"
  }
}
```

#### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "uploadUrl": "https://s3.amazonaws.com/bucket-name/presigned-url",
  "s3Key": "submissions/project-123/student-456/analysis.ipynb",
  "downloadUrl": "https://s3.amazonaws.com/bucket-name/submissions/project-123/student-456/analysis.ipynb",
  "expiresIn": 3600,
  "fileInfo": {
    "fileName": "analysis.ipynb",
    "fileType": "application/x-ipynb+json",
    "purpose": "submission",
    "metadata": {
      "projectId": "uuid",
      "submissionId": "uuid",
      "userId": "uuid"
    }
  }
}
```

#### Response Format (Error - 400)
```json
{
  "success": false,
  "error": "Invalid file type",
  "status": 400,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/s3/upload",
  "method": "POST",
  "details": "File type .exe is not allowed for security reasons"
}
```

### 1.2 Upload File to S3

#### Direct Upload to S3
```javascript
const uploadFile = async (file, purpose, metadata) => {
  // Step 1: Get presigned URL
  const response = await fetch('http://localhost:5001/api/s3/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      fileName: file.name,
      fileType: file.type,
      purpose,
      metadata
    })
  });

  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error);
  }

  // Step 2: Upload to S3 using presigned URL
  const uploadResponse = await fetch(data.uploadUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type
    }
  });

  if (!uploadResponse.ok) {
    throw new Error('Failed to upload file to S3');
  }

  return {
    s3Key: data.s3Key,
    downloadUrl: data.downloadUrl,
    fileName: file.name,
    fileSize: file.size
  };
};
```

---

## 2. File Download

### 2.1 Get Presigned Download URL

#### Endpoint
```
GET /api/s3/download/:s3Key
```

#### Request Headers
```http
Authorization: Bearer <access_token>
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "downloadUrl": "https://s3.amazonaws.com/bucket-name/presigned-download-url",
  "s3Key": "submissions/project-123/student-456/analysis.ipynb",
  "fileInfo": {
    "fileName": "analysis.ipynb",
    "fileSize": 2048576,
    "fileType": "application/x-ipynb+json",
    "lastModified": "2025-08-17T18:00:00.000Z"
  },
  "expiresIn": 3600
}
```

#### Response Format (Error - 404)
```json
{
  "success": false,
  "error": "File not found",
  "status": 404,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/s3/download/invalid-key",
  "method": "GET",
  "details": "The specified file does not exist"
}
```

### 2.2 Download File

#### Frontend Integration Example
```javascript
const downloadFile = async (s3Key, fileName) => {
  try {
    // Get presigned download URL
    const response = await fetch(`http://localhost:5001/api/s3/download/${s3Key}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
      }
    });

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error);
    }

    // Download file using presigned URL
    const downloadResponse = await fetch(data.downloadUrl);
    const blob = await downloadResponse.blob();

    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName || data.fileInfo.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return data.fileInfo;
  } catch (error) {
    console.error('Download failed:', error);
    throw error;
  }
};
```

---

## 3. File Management

### 3.1 List User Files

#### Endpoint
```
GET /api/s3/list?purpose=submission&projectId=project-123&page=1&limit=10
```

#### Query Parameters
- `purpose` (optional): Filter by purpose (submission|project|profile|workspace)
- `projectId` (optional): Filter by project ID
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `search` (optional): Search by filename

#### Request Headers
```http
Authorization: Bearer <access_token>
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "files": [
    {
      "id": "uuid",
      "fileName": "analysis.ipynb",
      "s3Key": "submissions/project-123/student-456/analysis.ipynb",
      "fileSize": 2048576,
      "fileType": "application/x-ipynb+json",
      "purpose": "submission",
      "metadata": {
        "projectId": "uuid",
        "submissionId": "uuid",
        "userId": "uuid"
      },
      "uploadedAt": "2025-08-17T18:00:00.000Z",
      "lastModified": "2025-08-17T18:00:00.000Z",
      "downloadUrl": "https://s3.amazonaws.com/bucket-name/presigned-download-url"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  },
  "summary": {
    "totalFiles": 25,
    "totalSize": 52428800,
    "purposes": {
      "submission": 15,
      "project": 8,
      "profile": 2
    }
  }
}
```

### 3.2 Delete File

#### Endpoint
```
DELETE /api/s3/:s3Key
```

#### Request Headers
```http
Authorization: Bearer <access_token>
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "s3Key": "submissions/project-123/student-456/analysis.ipynb",
  "message": "File deleted successfully"
}
```

#### Response Format (Error - 403)
```json
{
  "success": false,
  "error": "Permission denied",
  "status": 403,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/s3/submissions/project-123/student-456/analysis.ipynb",
  "method": "DELETE",
  "details": "You do not have permission to delete this file"
}
```

---

## 4. Workspace Management

### 4.1 Create Workspace

#### Endpoint
```
POST /api/s3/workspace
```

#### Request Format
```json
{
  "workspaceName": "project-123-workspace",
  "userId": "uuid",
  "projectId": "uuid",
  "description": "Workspace for Data Analysis Project"
}
```

#### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

#### Response Format (Success - 201)
```json
{
  "success": true,
  "workspace": {
    "id": "uuid",
    "workspaceName": "project-123-workspace",
    "s3Prefix": "workspaces/user-456/project-123",
    "userId": "uuid",
    "projectId": "uuid",
    "description": "Workspace for Data Analysis Project",
    "status": "active",
    "createdAt": "2025-08-17T18:00:00.000Z",
    "updatedAt": "2025-08-17T18:00:00.000Z"
  },
  "message": "Workspace created successfully"
}
```

### 4.2 List Workspace Files

#### Endpoint
```
GET /api/s3/workspace/:workspaceId/files?page=1&limit=10
```

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `search` (optional): Search by filename

#### Request Headers
```http
Authorization: Bearer <access_token>
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "workspace": {
    "id": "uuid",
    "workspaceName": "project-123-workspace",
    "s3Prefix": "workspaces/user-456/project-123"
  },
  "files": [
    {
      "id": "uuid",
      "fileName": "data.csv",
      "s3Key": "workspaces/user-456/project-123/data.csv",
      "fileSize": 1048576,
      "fileType": "text/csv",
      "uploadedAt": "2025-08-17T18:00:00.000Z",
      "lastModified": "2025-08-17T18:00:00.000Z",
      "downloadUrl": "https://s3.amazonaws.com/bucket-name/presigned-download-url"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 5,
    "pages": 1
  }
}
```

---

## 5. File Validation

### 5.1 Validate File

#### Endpoint
```
POST /api/s3/validate
```

#### Request Format
```json
{
  "fileName": "analysis.ipynb",
  "fileType": "application/x-ipynb+json",
  "fileSize": 2048576,
  "purpose": "submission",
  "allowedFileTypes": [".ipynb", ".pdf", ".py"],
  "maxFileSize": 10485760
}
```

#### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

#### Response Format (Success - 200)
```json
{
  "success": true,
  "valid": true,
  "validation": {
    "fileType": "valid",
    "fileSize": "valid",
    "fileName": "valid",
    "security": "passed"
  },
  "message": "File validation passed"
}
```

#### Response Format (Error - 400)
```json
{
  "success": true,
  "valid": false,
  "validation": {
    "fileType": "invalid",
    "fileSize": "valid",
    "fileName": "valid",
    "security": "passed"
  },
  "errors": [
    "File type .exe is not allowed for security reasons"
  ],
  "message": "File validation failed"
}
```

---

## Frontend Integration Examples

### File Upload Component
```javascript
import React, { useState } from 'react';

const FileUpload = ({ purpose, metadata, onUploadComplete }) => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);

  const handleFileUpload = async (file) => {
    setUploading(true);
    setError(null);
    setProgress(0);

    try {
      // Step 1: Get presigned URL
      const response = await fetch('http://localhost:5001/api/s3/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          purpose,
          metadata
        })
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error);
      }

      // Step 2: Upload to S3 with progress tracking
      const xhr = new XMLHttpRequest();
      
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentComplete = (event.loaded / event.total) * 100;
          setProgress(percentComplete);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          setProgress(100);
          onUploadComplete({
            s3Key: data.s3Key,
            downloadUrl: data.downloadUrl,
            fileName: file.name,
            fileSize: file.size
          });
        } else {
          throw new Error('Upload failed');
        }
      });

      xhr.addEventListener('error', () => {
        throw new Error('Upload failed');
      });

      xhr.open('PUT', data.uploadUrl);
      xhr.setRequestHeader('Content-Type', file.type);
      xhr.send(file);

    } catch (error) {
      setError(error.message);
    } finally {
      setUploading(false);
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  return (
    <div className="file-upload">
      <input
        type="file"
        onChange={handleFileSelect}
        disabled={uploading}
        accept=".ipynb,.pdf,.py,.csv,.json"
      />
      
      {uploading && (
        <div className="upload-progress">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <span>{progress.toFixed(1)}%</span>
        </div>
      )}
      
      {error && <div className="error">{error}</div>}
    </div>
  );
};
```

### File Manager Component
```javascript
const FileManager = ({ purpose, projectId }) => {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load files
  const loadFiles = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        purpose,
        ...(projectId && { projectId }),
        page: 1,
        limit: 50
      });

      const response = await fetch(
        `http://localhost:5001/api/s3/list?${params}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          }
        }
      );
      
      const data = await response.json();
      if (data.success) {
        setFiles(data.files);
      }
    } catch (error) {
      setError('Failed to load files');
    } finally {
      setLoading(false);
    }
  };

  // Download file
  const handleDownload = async (file) => {
    try {
      await downloadFile(file.s3Key, file.fileName);
    } catch (error) {
      setError('Download failed: ' + error.message);
    }
  };

  // Delete file
  const handleDelete = async (file) => {
    if (!confirm(`Are you sure you want to delete ${file.fileName}?`)) {
      return;
    }

    try {
      const response = await fetch(
        `http://localhost:5001/api/s3/${file.s3Key}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          }
        }
      );

      const data = await response.json();
      if (data.success) {
        await loadFiles(); // Refresh list
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      setError('Delete failed: ' + error.message);
    }
  };

  useEffect(() => {
    loadFiles();
  }, [purpose, projectId]);

  return (
    <div className="file-manager">
      <h2>Files</h2>
      
      {loading && <div>Loading files...</div>}
      {error && <div className="error">{error}</div>}

      <div className="file-list">
        {files.map((file) => (
          <div key={file.id} className="file-item">
            <div className="file-info">
              <span className="file-name">{file.fileName}</span>
              <span className="file-size">
                {(file.fileSize / 1024 / 1024).toFixed(2)} MB
              </span>
              <span className="file-date">
                {new Date(file.uploadedAt).toLocaleDateString()}
              </span>
            </div>
            
            <div className="file-actions">
              <button onClick={() => handleDownload(file)}>
                Download
              </button>
              <button onClick={() => handleDelete(file)}>
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Drag and Drop Upload
```javascript
const DragDropUpload = ({ purpose, metadata, onUploadComplete }) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      await handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file) => {
    setUploading(true);
    try {
      const result = await uploadFile(file, purpose, metadata);
      onUploadComplete(result);
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div
      className={`drag-drop-upload ${dragActive ? 'drag-active' : ''}`}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      {uploading ? (
        <div>Uploading...</div>
      ) : (
        <div>
          <p>Drag and drop files here</p>
          <p>or click to select files</p>
        </div>
      )}
    </div>
  );
};
```

---

## Error Handling

### Common Error Scenarios
1. **File Too Large**: When file size exceeds maximum allowed size
2. **Invalid File Type**: When file type is not allowed
3. **Permission Denied**: When user lacks permissions for file operations
4. **S3 Service Unavailable**: When AWS S3 service is down
5. **Presigned URL Expired**: When presigned URL has expired

### Error Response Format
```json
{
  "success": false,
  "error": "Error message",
  "status": 400,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/s3/upload",
  "method": "POST",
  "details": "Detailed error description"
}
```

---

## Testing Endpoints

### Test File Storage Flow
```bash
# 1. Get presigned upload URL
curl -X POST http://localhost:5001/api/s3/upload \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "fileName": "test.ipynb",
    "fileType": "application/x-ipynb+json",
    "purpose": "submission",
    "metadata": {
      "projectId": "project-uuid",
      "submissionId": "submission-uuid",
      "userId": "user-uuid"
    }
  }'

# 2. Upload file to S3 (using presigned URL from step 1)
curl -X PUT "https://s3.amazonaws.com/bucket-name/presigned-url" \
  -H "Content-Type: application/x-ipynb+json" \
  --upload-file test.ipynb

# 3. List files
curl -X GET "http://localhost:5001/api/s3/list?purpose=submission&projectId=project-uuid" \
  -H "Authorization: Bearer <access_token>"

# 4. Get presigned download URL
curl -X GET "http://localhost:5001/api/s3/download/submissions/project-uuid/user-uuid/test.ipynb" \
  -H "Authorization: Bearer <access_token>"

# 5. Download file (using presigned URL from step 4)
curl -X GET "https://s3.amazonaws.com/bucket-name/presigned-download-url" \
  --output downloaded-test.ipynb

# 6. Delete file
curl -X DELETE "http://localhost:5001/api/s3/submissions/project-uuid/user-uuid/test.ipynb" \
  -H "Authorization: Bearer <access_token>"
```

---

## Security Considerations

1. **Authentication**: All file operations require valid JWT tokens
2. **Authorization**: Users can only access their own files
3. **File Type Validation**: Only allowed file types are accepted
4. **File Size Limits**: Maximum file sizes are enforced
5. **Presigned URLs**: Temporary URLs with expiration for secure access
6. **Virus Scanning**: Files are scanned for malware before storage
7. **Encryption**: Files are encrypted at rest in S3

---

**Last Updated**: August 17, 2025  
**Version**: 2.1.0  
**Maintainer**: BITS Pilani DataScience Team
