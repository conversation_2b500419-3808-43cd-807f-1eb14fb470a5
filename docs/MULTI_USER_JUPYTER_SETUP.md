# Multi-User Jupyter Integration Setup Guide

This guide explains how to set up and test the multi-user Jupyter integration where each user gets their own isolated Jupyter instance.

## Overview

The new architecture provides:
- **User Isolation**: Each user gets their own Jupyter server instance
- **Project-based Workspaces**: Users can create multiple project workspaces
- **Sandboxed Environment**: Complete isolation between users
- **Automatic Authentication**: Seamless integration with your backend auth

## Architecture

```
Frontend → Backend API → JupyterHub → Individual Jupyter Instances
                                   ├── User 1 (user_1) → project_1/, project_2/
                                   ├── User 2 (user_2) → project_3/, project_4/
                                   └── User 3 (user_3) → project_5/, project_6/
```

## Local Development Setup

### Option 1: Docker Setup (Recommended)

1. **Start JupyterHub with Docker**:
   ```bash
   cd docker
   docker-compose up -d
   ```

2. **Verify JupyterHub is running**:
   ```bash
   curl http://localhost:8000/hub/api
   ```

3. **Update your `.env` file**:
   ```env
   JUPYTER_HUB_URL=http://localhost:8000
   JUPYTER_AUTH_TOKEN=your-jupyterhub-api-token
   ```

### Option 2: Manual JupyterHub Setup

1. **Install JupyterHub**:
   ```bash
   pip install jupyterhub jupyterhub-dummyauthenticator
   ```

2. **Create configuration file** (`jupyterhub_config.py`):
   ```python
   # Use the provided config from docker/jupyterhub/jupyterhub_config.py
   ```

3. **Start JupyterHub**:
   ```bash
   jupyterhub -f jupyterhub_config.py
   ```

## Production Setup

For production, update your environment variables:

```env
JUPYTER_HUB_URL=http://**************:8000
JUPYTER_AUTH_TOKEN=your-production-api-token
```

## API Usage

### 1. Authenticate User and Start Instance

```javascript
POST /api/jupyter/authenticate
{
  "username": "alice",
  "password": "dummy_password"  // For dummy authenticator
}

Response:
{
  "success": true,
  "serverURL": "http://localhost:8000/user/alice",
  "username": "alice",
  "userId": "1"
}
```

### 2. Create Project Workspace

```javascript
POST /api/jupyter/workspace
{
  "projectId": "project-123",
  "kernelName": "python3",
  "username": "alice",
  "password": "dummy_password"
}

Response:
{
  "success": true,
  "userId": "1",
  "projectId": "project-123",
  "projectDirectory": "project_project-123",
  "workspaceNotebook": "project_project-123/workspace.ipynb",
  "welcomeNotebook": "project_project-123/welcome.ipynb",
  "kernel_id": "kernel-id-here",
  "serverURL": "http://localhost:8000/user/alice",
  "directAccessURL": "http://localhost:8000/user/alice/notebooks/project_project-123/workspace.ipynb"
}
```

### 3. Execute Code

```javascript
POST /api/jupyter/kernels/{kernel_id}/execute
{
  "code": "print('Hello from isolated environment!')\nimport os\nprint(f'User directory: {os.getcwd()}')"
}

Response:
{
  "success": true,
  "kernel_id": "kernel-id",
  "execution_count": 1,
  "status": "ok",
  "outputs": [
    {
      "output_type": "stream",
      "name": "stdout",
      "text": "Hello from isolated environment!\nUser directory: /home/<USER>/notebooks/project_project-123\n"
    }
  ]
}
```

## Directory Structure

Each user gets their own isolated directory structure:

```
/home/<USER>/notebooks/
├── project_{project_id_1}/
│   ├── welcome.ipynb
│   ├── workspace.ipynb
│   └── [user files...]
├── project_{project_id_2}/
│   ├── welcome.ipynb
│   ├── workspace.ipynb
│   └── [user files...]
└── [other projects...]
```

## Testing

### Run Comprehensive Tests

```bash
# Test multi-user functionality
node scripts/test-multiuser-jupyter.js
```

This will test:
- User authentication and instance creation
- Project workspace creation
- Code execution in isolated environments
- User isolation (security test)

### Manual Testing

1. **Test User 1**:
   ```bash
   # Generate token for user 1
   node scripts/generate-test-token.js
   
   # Create workspace
   curl -X POST "http://localhost:3001/api/jupyter/workspace" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"projectId": "project-1", "username": "alice"}'
   ```

2. **Test User 2**:
   ```bash
   # Generate token for user 2
   # Create workspace for different user
   curl -X POST "http://localhost:3001/api/jupyter/workspace" \
     -H "Authorization: Bearer USER2_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"projectId": "project-2", "username": "bob"}'
   ```

## Security Features

### User Isolation
- Each user has their own Jupyter server instance
- Users cannot access other users' files or kernels
- Complete process isolation

### Authentication
- JWT-based authentication for API access
- JupyterHub handles user session management
- Configurable authenticator (dummy for testing, LDAP/OAuth for production)

### Project Isolation
- Each project gets its own directory
- Files are organized by project ID
- No cross-project contamination

## Monitoring

### Check User Instances
```bash
# List all running user servers
curl -H "Authorization: token your-api-token" \
  http://localhost:8000/hub/api/users
```

### Check User Status
```bash
# Check specific user server
curl -H "Authorization: token your-api-token" \
  http://localhost:8000/hub/api/users/alice/server
```

## Troubleshooting

### Common Issues

1. **User server won't start**:
   - Check JupyterHub logs: `docker logs jupyterhub-multiuser`
   - Verify user permissions
   - Check disk space

2. **Authentication fails**:
   - Verify API token in environment variables
   - Check JupyterHub configuration
   - Ensure dummy authenticator is enabled

3. **Cannot access user files**:
   - Check directory permissions
   - Verify user isolation is working
   - Check notebook directory configuration

### Debug Commands

```bash
# Check JupyterHub status
curl http://localhost:8000/hub/api

# List active users
curl -H "Authorization: token your-api-token" \
  http://localhost:8000/hub/api/users

# Check specific user
curl -H "Authorization: token your-api-token" \
  http://localhost:8000/hub/api/users/alice
```

## Production Considerations

1. **Authenticator**: Replace dummy authenticator with proper authentication (LDAP, OAuth, etc.)
2. **Spawner**: Consider using DockerSpawner for better isolation
3. **Storage**: Use persistent volumes for user data
4. **Scaling**: Configure resource limits per user
5. **Monitoring**: Set up proper logging and monitoring
6. **Backup**: Implement user data backup strategy

## Next Steps

1. Test the multi-user setup locally
2. Verify user isolation works correctly
3. Test project workspace creation
4. Integrate with your frontend
5. Deploy to production environment
6. Configure proper authentication for production
