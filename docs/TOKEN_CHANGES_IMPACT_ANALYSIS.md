# Token Changes Impact Analysis - BITS DataScience Platform

**Generated:** August 20, 2025  
**Platform:** BITS Pilani DataScience Projects Portal Backend  
**Analysis Focus:** Impact of Enhanced Token System on RBAC and Workflows (Updated with LTI Integration)  

---

## 🔍 **Current RBAC Architecture Analysis**

### **✅ Current Token-RBAC Integration (Multi-Authentication Support)**

#### **1. Token Payload Structure**
```javascript
// Current token payload (src/middlewares/auth.js)
const payload = {
  userId: user.id,      // Used to fetch user data
  email: user.email,    // Basic user info
  name: user.name       // Basic user info
};

// LTI Session Structure (src/controllers/ltiController.js)
const session = {
  user: { id, email, name, roles },
  ltiContext: { id, contextId, title, label },
  ltiResourceLink: { id, resourceLinkId, title },
  ltiLaunchData: { messageType, version, deploymentId }
};
```

#### **2. RBAC Data Flow**
```javascript
// Current flow in jwtMiddleware
1. Decode JWT token → Extract userId
2. Fetch user from database → Include roles & permissions
3. Extract permissions → req.userPermissions = [...]
4. Extract roles → req.userRoles = [...]
5. Attach to request → req.user, req.userPermissions, req.userRoles

// LTI flow in ltiAuth middleware
1. Check LTI session → Extract user from session
2. Map LTI roles to platform roles → req.userRoles = mappedRoles
3. Extract permissions → req.userPermissions = [...]
4. Attach to request → req.user, req.userPermissions, req.userRoles
```

#### **3. RBAC Middleware Dependencies**
```javascript
// RBAC middleware depends on:
- req.user (User object with roles/permissions)
- req.userPermissions (Array of permission keys)
- req.userRoles (Array of role names)
- req.primaryRole (First role in array)
```

---

## ⚠️ **Potential Impact Areas**

### **1. 🔴 High Impact - Token Payload Changes**

#### **Current vs Proposed Token Structure**
```javascript
// CURRENT (Single Token)
{
  "userId": "uuid",
  "email": "<EMAIL>", 
  "name": "User Name"
}

// PROPOSED (Access Token)
{
  "userId": "uuid",
  "email": "<EMAIL>",
  "name": "User Name",
  "type": "access",           // NEW
  "version": 1,              // NEW
  "exp": 1640998800          // Shorter expiry
}
```

#### **Impact Assessment:**
- ✅ **Low Risk**: `userId`, `email`, `name` remain unchanged
- ✅ **Low Risk**: JWT middleware will continue to work
- ⚠️ **Medium Risk**: New fields (`type`, `version`) need handling
- ⚠️ **Medium Risk**: Shorter token expiry affects user experience

### **2. 🔴 High Impact - Database Query Frequency**

#### **Current vs Proposed Database Load**
```javascript
// CURRENT: Database query on every API call
jwtMiddleware → User.findByPk(userId) → Include roles/permissions

// PROPOSED: Same database query pattern
jwtMiddleware → User.findByPk(userId) → Include roles/permissions
```

#### **Impact Assessment:**
- ✅ **No Change**: Database query pattern remains identical
- ✅ **No Change**: RBAC data fetching unchanged
- ✅ **No Change**: Permission/role extraction logic unchanged

### **3. 🟡 Medium Impact - Token Refresh Flow**

#### **Current Refresh Token Issues**
```javascript
// CURRENT: Refresh requires valid token (defeats purpose)
POST /api/auth/refresh
Headers: Authorization: Bearer <valid_token>  // ❌ Problem

// PROPOSED: Refresh uses refresh token
POST /api/auth/refresh  
Body: { "refreshToken": "..." }  // ✅ Solution
```

#### **Impact Assessment:**
- ✅ **Positive**: Fixes broken refresh flow
- ⚠️ **Medium Risk**: Frontend needs to handle new refresh mechanism
- ⚠️ **Medium Risk**: Token storage strategy changes

### **4. 🟡 Medium Impact - Logout Enhancement**

#### **Current vs Proposed Logout**
```javascript
// CURRENT: Only blacklists access token
logout() → blacklistToken(accessToken)

// PROPOSED: Blacklists both tokens
logout() → {
  blacklistToken(accessToken),
  blacklistToken(refreshToken),
  removeRefreshToken(userId, refreshToken)
}
```

#### **Impact Assessment:**
- ✅ **Positive**: More secure logout
- ⚠️ **Low Risk**: Additional database operations
- ✅ **No Change**: RBAC middleware unaffected

---

## 🔧 **Detailed Impact Analysis by Component**

### **1. Authentication Middleware (`src/middlewares/auth.js`)**

#### **Current Implementation**
```javascript
export const jwtMiddleware = async (req, res, next) => {
  // 1. Extract token from Authorization header
  const token = authHeader.substring(7);
  
  // 2. Check blacklist
  const isBlacklisted = await tokenBlacklistService.isTokenBlacklisted(token);
  
  // 3. Verify JWT
  const decoded = jwt.verify(token, process.env.JWT_SECRET);
  
  // 4. Fetch user with roles/permissions
  const user = await User.findByPk(decoded.userId, {
    include: [/* roles and permissions */]
  });
  
  // 5. Extract RBAC data
  req.user = user;
  req.userPermissions = userPermissions;
  req.userRoles = userRoles;
};
```

#### **Proposed Changes**
```javascript
export const jwtMiddleware = async (req, res, next) => {
  // 1. Extract token from Authorization header (UNCHANGED)
  const token = authHeader.substring(7);
  
  // 2. Check blacklist (UNCHANGED)
  const isBlacklisted = await tokenBlacklistService.isTokenBlacklisted(token);
  
  // 3. Verify JWT (UNCHANGED)
  const decoded = jwt.verify(token, process.env.JWT_SECRET);
  
  // 4. Validate token type (NEW)
  if (decoded.type !== 'access') {
    return res.status(401).json({ error: 'Invalid token type' });
  }
  
  // 5. Check token version (NEW)
  const user = await User.findByPk(decoded.userId);
  if (decoded.version < user.tokenVersion) {
    return res.status(401).json({ error: 'Token invalidated' });
  }
  
  // 6. Fetch user with roles/permissions (UNCHANGED)
  const user = await User.findByPk(decoded.userId, {
    include: [/* roles and permissions */]
  });
  
  // 7. Extract RBAC data (UNCHANGED)
  req.user = user;
  req.userPermissions = userPermissions;
  req.userRoles = userRoles;
};
```

#### **Impact Assessment:**
- ✅ **Minimal Changes**: Only 2 new validation steps
- ✅ **Backward Compatible**: Existing tokens still work
- ✅ **RBAC Unchanged**: Permission/role extraction identical

### **2. RBAC Middleware (`src/middlewares/rbac.js`)**

#### **Current Implementation**
```javascript
export const requirePermissions = (requiredPermissions, options = {}) => {
  return (req, res, next) => {
    // 1. Check authentication
    if (!req.user) return res.status(401).json({...});
    
    // 2. Get user permissions (set by jwtMiddleware)
    const userPermissions = req.userPermissions || [];
    
    // 3. Check permissions
    const hasAccess = permissions.every(permission => 
      userPermissions.includes(permission)
    );
    
    // 4. Allow/deny access
    if (!hasAccess) return res.status(403).json({...});
    next();
  };
};
```

#### **Proposed Changes**
```javascript
// NO CHANGES REQUIRED - RBAC middleware is token-agnostic
export const requirePermissions = (requiredPermissions, options = {}) => {
  return (req, res, next) => {
    // 1. Check authentication (UNCHANGED)
    if (!req.user) return res.status(401).json({...});
    
    // 2. Get user permissions (UNCHANGED)
    const userPermissions = req.userPermissions || [];
    
    // 3. Check permissions (UNCHANGED)
    const hasAccess = permissions.every(permission => 
      userPermissions.includes(permission)
    );
    
    // 4. Allow/deny access (UNCHANGED)
    if (!hasAccess) return res.status(403).json({...});
    next();
  };
};
```

#### **Impact Assessment:**
- ✅ **Zero Changes**: RBAC middleware is completely unaffected
- ✅ **Token Agnostic**: Works with any token type
- ✅ **Permission Based**: Relies on `req.userPermissions` array

### **3. Route Protection (All Route Files)**

#### **Current Route Protection**
```javascript
// Example from src/routes/projects.js
router.get('/:id', 
  jwtMiddleware,                    // Authentication
  requirePermissions(['view_projects']), // Authorization
  getProjectById
);
```

#### **Proposed Changes**
```javascript
// NO CHANGES REQUIRED - Route protection remains identical
router.get('/:id', 
  jwtMiddleware,                    // Authentication (unchanged)
  requirePermissions(['view_projects']), // Authorization (unchanged)
  getProjectById
);
```

#### **Impact Assessment:**
- ✅ **Zero Changes**: All route protection remains identical
- ✅ **Middleware Chain**: Authentication → Authorization flow unchanged
- ✅ **Permission Checks**: All permission-based routes unaffected

### **4. Controller Logic (All Controllers)**

#### **Current Controller Access**
```javascript
// Example from src/controllers/projectController.js
export const getProjectById = asyncHandler(async (req, res) => {
  // Access RBAC data (set by middleware)
  const user = req.user;                    // User object
  const userPermissions = req.userPermissions; // Permission array
  const userRoles = req.userRoles;          // Role array
  
  // Use RBAC data for business logic
  if (userRoles.includes('admin')) {
    // Admin logic
  } else if (userPermissions.includes('view_projects')) {
    // User logic
  }
});
```

#### **Proposed Changes**
```javascript
// NO CHANGES REQUIRED - Controller access remains identical
export const getProjectById = asyncHandler(async (req, res) => {
  // Access RBAC data (unchanged)
  const user = req.user;                    // User object (unchanged)
  const userPermissions = req.userPermissions; // Permission array (unchanged)
  const userRoles = req.userRoles;          // Role array (unchanged)
  
  // Use RBAC data for business logic (unchanged)
  if (userRoles.includes('admin')) {
    // Admin logic (unchanged)
  } else if (userPermissions.includes('view_projects')) {
    // User logic (unchanged)
  }
});
```

#### **Impact Assessment:**
- ✅ **Zero Changes**: All controller logic remains identical
- ✅ **RBAC Access**: `req.user`, `req.userPermissions`, `req.userRoles` unchanged
- ✅ **Business Logic**: All permission/role-based logic unaffected

---

## 📊 **Workflow Impact Analysis**

### **1. User Authentication Workflow**

#### **Current Flow**
```
1. User Login → Single JWT Token (24h)
2. API Calls → Bearer Token → jwtMiddleware → RBAC Check
3. Token Expires → Re-login Required
4. Logout → Token Blacklisted
```

#### **Proposed Flow**
```
1. User Login → Access Token (15m) + Refresh Token (7d)
2. API Calls → Access Token → jwtMiddleware → RBAC Check
3. Access Token Expires → Use Refresh Token → New Access Token
4. Logout → Both Tokens Blacklisted
```

#### **Impact Assessment:**
- ✅ **RBAC Unchanged**: Permission/role checking identical
- ✅ **Middleware Unchanged**: `jwtMiddleware` → `requirePermissions` flow
- ⚠️ **Frontend Changes**: Token management strategy changes
- ⚠️ **User Experience**: More frequent token refresh (but seamless)

### **2. Admin Workflow**

#### **Current Admin Operations**
```javascript
// Admin routes remain identical
router.get('/users', 
  jwtMiddleware,           // Authentication
  requireRoles(['admin']), // Authorization
  getUsers
);
```

#### **Proposed Admin Operations**
```javascript
// NO CHANGES - Admin routes remain identical
router.get('/users', 
  jwtMiddleware,           // Authentication (unchanged)
  requireRoles(['admin']), // Authorization (unchanged)
  getUsers
);
```

#### **Impact Assessment:**
- ✅ **Zero Changes**: All admin operations unaffected
- ✅ **Permission Checks**: Role-based access control unchanged
- ✅ **User Management**: Admin user management workflows identical

### **3. Student/Instructor Workflow**

#### **Current User Operations**
```javascript
// User routes remain identical
router.post('/submissions',
  jwtMiddleware,
  requirePermissions(['submit_assignments']),
  createSubmission
);
```

#### **Proposed User Operations**
```javascript
// NO CHANGES - User routes remain identical
router.post('/submissions',
  jwtMiddleware,
  requirePermissions(['submit_assignments']),
  createSubmission
);
```

#### **Impact Assessment:**
- ✅ **Zero Changes**: All user operations unaffected
- ✅ **Permission Checks**: Assignment submission, viewing, etc. unchanged
- ✅ **Course Access**: Course enrollment and access logic identical

---

## 🚨 **Potential Risks and Mitigation**

### **1. Token Type Validation Risk**

#### **Risk:**
```javascript
// New token type validation might break existing tokens
if (decoded.type !== 'access') {
  return res.status(401).json({ error: 'Invalid token type' });
}
```

#### **Mitigation:**
```javascript
// Backward compatibility
if (decoded.type && decoded.type !== 'access') {
  return res.status(401).json({ error: 'Invalid token type' });
}
// Allow tokens without 'type' field (existing tokens)
```

### **2. Token Version Validation Risk**

#### **Risk:**
```javascript
// Token version check might fail if user.tokenVersion is null
if (decoded.version < user.tokenVersion) {
  return res.status(401).json({ error: 'Token invalidated' });
}
```

#### **Mitigation:**
```javascript
// Safe version comparison
const userVersion = user.tokenVersion || 1;
if (decoded.version && decoded.version < userVersion) {
  return res.status(401).json({ error: 'Token invalidated' });
}
```

### **3. Database Migration Risk**

#### **Risk:**
- Adding `tokenVersion` field to User model
- Adding refresh token storage

#### **Mitigation:**
```javascript
// Safe migration
// 1. Add tokenVersion with default value
// 2. Add refresh token table with proper indexing
// 3. Implement gradual rollout
```

---

## 📋 **Implementation Strategy**

### **Phase 1: Backward Compatible Changes**
1. ✅ **Enhance Token Generation**: Add type and version fields
2. ✅ **Update JWT Middleware**: Add validation with fallbacks
3. ✅ **Implement Refresh Token**: New endpoint and storage
4. ✅ **Test Existing Workflows**: Ensure no regression

### **Phase 2: Frontend Integration**
1. ✅ **Update Token Storage**: Handle access + refresh tokens
2. ✅ **Implement Auto-Refresh**: Automatic token refresh logic
3. ✅ **Update Logout**: Handle both token types
4. ✅ **User Experience**: Seamless token refresh

### **Phase 3: Security Enhancement**
1. ✅ **Token Rotation**: Implement refresh token rotation
2. ✅ **Device Tracking**: Add device-specific tokens
3. ✅ **Session Management**: Enhanced session controls
4. ✅ **Monitoring**: Token usage analytics

---

## 🎯 **Conclusion**

### **Impact Summary**

| Component | Impact Level | Changes Required | Risk Level |
|-----------|-------------|------------------|------------|
| **RBAC Middleware** | None | 0 | None |
| **Route Protection** | None | 0 | None |
| **Controllers** | None | 0 | None |
| **JWT Middleware** | Low | 2 new validations | Low |
| **Authentication Flow** | Medium | New refresh mechanism | Medium |
| **Frontend Integration** | High | Token management changes | Medium |

### **Key Findings**

#### **✅ Positive Impacts**
1. **RBAC System Unchanged**: Zero modifications required
2. **Permission System Intact**: All permission checks remain identical
3. **Role-Based Access**: All role-based routes unaffected
4. **Security Enhancement**: Better token security and management
5. **User Experience**: Seamless token refresh (after implementation)

#### **⚠️ Areas Requiring Attention**
1. **JWT Middleware**: Minor updates for token type/version validation
2. **Frontend Integration**: Token storage and refresh logic changes
3. **Database Schema**: Adding token version and refresh token storage
4. **Testing**: Comprehensive testing of new token flows

### **Recommendation**

**Proceed with the enhanced token system implementation** as it provides:

1. **Minimal Risk**: RBAC and core workflows remain unchanged
2. **Security Benefits**: Proper access/refresh token separation
3. **User Experience**: Seamless authentication with automatic refresh
4. **Scalability**: Better session management for production use

The changes are **backward compatible** and can be implemented **gradually** without disrupting existing functionality.

---

**Analysis Generated by:** BITS DataScience Team  
**Last Updated:** August 18, 2025  
**Next Review:** After Phase 1 implementation
