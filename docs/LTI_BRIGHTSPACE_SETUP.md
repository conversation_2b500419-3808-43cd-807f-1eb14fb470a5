# LTI 1.3 Brightspace D2L Integration Guide

**Platform:** BITS DataScience Projects Portal  
**Target LMS:** Brightspace D2L  
**LTI Version:** 1.3  
**Last Updated:** August 18, 2025  

---

## 📋 **Overview**

This guide provides step-by-step instructions for integrating the BITS DataScience Platform with Brightspace D2L using LTI 1.3. The integration enables:

- **Single Sign-On (SSO)** from Brightspace to the DataScience Platform
- **Automatic user provisioning** with role mapping
- **Grade passback** from projects to Brightspace gradebook
- **Content selection** via deep linking
- **Course roster synchronization** via NRPS

---

## 🔧 **Prerequisites**

### **System Requirements**
- Brightspace D2L instance (demo or production)
- BITS DataScience Platform backend running
- Valid SSL certificates for both systems
- Network connectivity between Brightspace and the platform

### **Required Information**
- Brightspace D2L URL (e.g., `https://bitspilani.brightspacedemo.com/`)
- Platform administrator access
- API credentials (if using Brightspace APIs)

---

## 🚀 **Step 1: Platform Registration in Brightspace**

### **1.1 Access Brightspace Admin Tools**
1. Log into your Brightspace D2L instance as an administrator
2. Navigate to **Admin Tools** → **LTI Integration** → **Manage External Learning Tools**

### **1.2 Create New LTI Tool**
1. Click **"Create LTI Link"** or **"Add External Learning Tool"**
2. Select **"LTI 1.3"** as the version
3. Choose **"Configure Manually"** for tool setup

### **1.3 Configure Tool Settings**

#### **Basic Information**
```
Tool Name: BITS DataScience Platform
Tool Description: Interactive data science projects and assignments platform
Tool URL: https://your-platform-domain.com/api/lti/oidc/init
```

#### **Authentication Settings**
```
OpenID Connect Login URL: https://your-platform-domain.com/api/lti/oidc/init
Redirect URL: https://your-platform-domain.com/api/lti/oidc/callback
Target Link URI: https://your-platform-domain.com/api/lti/oidc/init
```

#### **Security Settings**
```
Public Key URL: https://your-platform-domain.com/.well-known/jwks.json
Client ID: bits-datascience-platform
```

#### **Scopes and Permissions**
```
Required Scopes:
- openid
- https://purl.imsglobal.org/spec/lti-ags/scope/lineitem
- https://purl.imsglobal.org/spec/lti-ags/scope/score
- https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly
```

#### **Custom Parameters**
```
project_id: $ResourceLink.id
context_id: $Context.id
user_id: $User.id
```

### **1.4 Save and Test Configuration**
1. Save the tool configuration
2. Test the connection using Brightspace's built-in testing tools
3. Note the **Client ID** and **Issuer URL** for the next step

---

## 🔧 **Step 2: Register Brightspace in the Platform**

### **2.1 Get Brightspace Configuration**
From your Brightspace admin panel, note down:
- **Issuer URL** (e.g., `https://bitspilani.brightspacedemo.com/`)
- **Client ID** (from the tool configuration)
- **Authorization Endpoint** (OIDC login URL)
- **Token Endpoint** (for token exchange)
- **JWKS URL** (for key verification)

### **2.2 Register Platform via API**
```bash
curl -X POST https://your-platform-domain.com/api/lti/platforms \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "platformName": "Brightspace D2L",
    "platformId": "https://bitspilani.brightspacedemo.com/",
    "clientId": "bits-datascience-platform",
    "authLoginUrl": "https://bitspilani.brightspacedemo.com/d2l/auth/api/token",
    "authTokenUrl": "https://bitspilani.brightspacedemo.com/d2l/auth/token",
    "keySetUrl": "https://bitspilani.brightspacedemo.com/.well-known/jwks.json",
    "deploymentId": "your-deployment-id"
  }'
```

### **2.3 Verify Registration**
```bash
curl -X GET https://your-platform-domain.com/api/lti/config
```

---

## 🔑 **Step 3: Generate and Configure Keys**

### **3.1 Generate RSA Key Pair**
```bash
# Generate private key
openssl genrsa -out lti_private_key.pem 2048

# Generate public key
openssl rsa -in lti_private_key.pem -pubout -out lti_public_key.pem

# Convert to base64 for environment variables
cat lti_private_key.pem | base64 -w 0
cat lti_public_key.pem | base64 -w 0
```

### **3.2 Configure Environment Variables**
Add to your platform's `.env` file:
```env
# LTI Configuration
LTI_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"
LTI_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nYOUR_PUBLIC_KEY_HERE\n-----END PUBLIC KEY-----"
LTI_KEY_ID="bits-datascience-key-1"
LTI_TOOL_URL="https://your-platform-domain.com"

# Brightspace D2L Configuration
BRIGHTSPACE_ISSUER="https://bitspilani.brightspacedemo.com/"
BRIGHTSPACE_CLIENT_ID="bits-datascience-platform"
```

### **3.3 Verify JWKS Endpoint**
```bash
curl https://your-platform-domain.com/.well-known/jwks.json
```

Expected response:
```json
{
  "keys": [
    {
      "kty": "RSA",
      "use": "sig",
      "kid": "bits-datascience-key-1",
      "alg": "RS256",
      "n": "YOUR_PUBLIC_KEY_MODULUS",
      "e": "AQAB"
    }
  ]
}
```

---

## 🧪 **Step 4: Test the Integration**

### **4.1 Test OIDC Flow**
1. Create a test course in Brightspace
2. Add the LTI tool to the course
3. Click on the tool link
4. Verify successful authentication and redirect

### **4.2 Test Deep Linking**
1. As an instructor, add the tool to a course
2. Select "Add Content" → "External Learning Tools"
3. Choose the BITS DataScience Platform
4. Verify the content selection interface appears

### **4.3 Test Grade Passback**
1. Create a project submission in the platform
2. Grade the submission
3. Verify the grade appears in Brightspace gradebook

---

## 📊 **Step 5: Role Mapping Configuration**

### **5.1 Default Role Mappings**
The platform automatically maps Brightspace roles to application roles:

| Brightspace Role | Platform Role | Permissions |
|------------------|---------------|-------------|
| Instructor | instructor | Create projects, grade submissions, manage course |
| TeachingAssistant | teaching_assistant | Grade submissions, view analytics |
| Learner | student | Submit projects, view grades |
| ContentDeveloper | content_developer | Create and edit content |
| Administrator | administrator | Full system access |

### **5.2 Custom Role Mapping**
To customize role mappings, modify the `mapLTIRoles` function in `src/services/ltiService.js`:

```javascript
const roleMappings = {
  'http://purl.imsglobal.org/vocab/lis/v2/membership#Instructor': 'instructor',
  'http://purl.imsglobal.org/vocab/lis/v2/membership#TeachingAssistant': 'teaching_assistant',
  'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner': 'student',
  // Add custom mappings here
  'http://purl.imsglobal.org/vocab/lis/v2/membership#CustomRole': 'custom_role'
};
```

---

## 🔄 **Step 6: Service Integration**

### **6.1 NRPS (Names and Roles Provisioning Service)**
The platform automatically syncs course rosters via NRPS:

```javascript
// Get course roster
const roster = await ltiService.getCourseRoster(context, platform);
console.log('Course members:', roster.members);
```

### **6.2 AGS (Assignments and Grades Service)**
Grades are automatically sent to Brightspace:

```javascript
// Submit grade
const result = await ltiService.submitGrade(
  resourceLink,
  user,
  score,
  platform
);
```

### **6.3 Deep Linking**
Content selection is handled automatically:

```javascript
// Create deep linking response
const deepLinkingJWT = ltiService.createDeepLinkingResponse(
  projects,
  deepLinkingSettings,
  platform
);
```

---

## 🔒 **Step 7: Security Configuration**

### **7.1 CORS Configuration**
Ensure your platform allows requests from Brightspace:

```javascript
// In your Express app
app.use(cors({
  origin: [
    'https://bitspilani.brightspacedemo.com',
    'https://your-brightspace-domain.com'
  ],
  credentials: true
}));
```

### **7.2 Session Security**
Configure secure session settings:

```javascript
app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'lax',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));
```

### **7.3 JWT Security**
Ensure proper JWT validation:

```javascript
// Validate JWT claims
const requiredClaims = [
  'iss', 'aud', 'exp', 'iat', 'nonce',
  'https://purl.imsglobal.org/spec/lti/claim/message_type',
  'https://purl.imsglobal.org/spec/lti/claim/version',
  'https://purl.imsglobal.org/spec/lti/claim/deployment_id'
];
```

---

## 🐛 **Step 8: Troubleshooting**

### **Common Issues**

#### **8.1 Authentication Failures**
**Symptoms:** Users can't log in via LTI
**Solutions:**
- Verify platform registration in both systems
- Check JWT signature validation
- Ensure proper key configuration
- Verify redirect URLs match exactly

#### **8.2 Grade Passback Issues**
**Symptoms:** Grades don't appear in Brightspace
**Solutions:**
- Check AGS scope permissions
- Verify line item creation
- Ensure proper score format
- Check platform access tokens

#### **8.3 Deep Linking Problems**
**Symptoms:** Content selection doesn't work
**Solutions:**
- Verify deep linking claims in JWT
- Check return URL configuration
- Ensure proper JWT signing
- Verify content item format

### **Debug Logging**
Enable debug logging for LTI operations:

```javascript
// In your environment
DEBUG=lti:*
NODE_ENV=development
```

### **Testing Tools**
Use these tools for testing:

1. **LTI 1.3 Test Tool**: https://lti-ri.imsglobal.org/
2. **JWT Debugger**: https://jwt.io/
3. **Brightspace API Explorer**: Available in your Brightspace instance

---

## 📈 **Step 9: Monitoring and Maintenance**

### **9.1 Health Checks**
Monitor these endpoints:

```bash
# JWKS endpoint
curl https://your-platform-domain.com/.well-known/jwks.json

# LTI configuration
curl https://your-platform-domain.com/api/lti/config

# Session status (requires authentication)
curl https://your-platform-domain.com/api/lti/session
```

### **9.2 Session Cleanup**
Run periodic cleanup of expired sessions:

```bash
curl -X POST https://your-platform-domain.com/api/lti/cleanup \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### **9.3 Key Rotation**
Rotate keys periodically:

1. Generate new key pair
2. Update environment variables
3. Update JWKS endpoint
4. Notify Brightspace administrators

---

## 📚 **Step 10: API Reference**

### **LTI Endpoints**

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/.well-known/jwks.json` | GET | Public key set |
| `/api/lti/oidc/init` | GET/POST | OIDC login initiation |
| `/api/lti/oidc/callback` | POST | OIDC callback |
| `/api/lti/deep-linking` | POST | Deep linking handler |
| `/api/lti/session` | GET | Session status |
| `/api/lti/roster/:contextId` | GET | Course roster |
| `/api/lti/grades` | POST | Grade submission |
| `/api/lti/platforms` | POST | Platform registration |
| `/api/lti/cleanup` | POST | Session cleanup |

### **Configuration Endpoints**

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/lti/config` | GET | LTI tool configuration |

---

## 🎯 **Success Criteria**

Your LTI integration is successful when:

✅ **Authentication works**: Users can log in via Brightspace  
✅ **Role mapping works**: Users have correct permissions  
✅ **Deep linking works**: Instructors can select content  
✅ **Grade passback works**: Grades appear in Brightspace  
✅ **Roster sync works**: Course members are synchronized  
✅ **Security is maintained**: All communications are secure  

---

## 📞 **Support**

For technical support:

- **Documentation**: Check this guide and API documentation
- **Logs**: Review application logs for error details
- **Testing**: Use the provided testing tools
- **Community**: Check LTI 1.3 community resources

---

## 🔗 **Additional Resources**

- [LTI 1.3 Specification](https://www.imsglobal.org/spec/lti/v1p3)
- [Brightspace D2L Documentation](https://docs.brightspace.com/)
- [LTI Advantage Overview](https://www.imsglobal.org/lti-advantage-overview)
- [JWT.io](https://jwt.io/) - JWT debugging tool
- [LTI 1.3 Test Tool](https://lti-ri.imsglobal.org/) - Testing platform

---

**Last Updated:** August 18, 2025  
**Version:** 1.0  
**Compatibility:** LTI 1.3, Brightspace D2L 20.21+
