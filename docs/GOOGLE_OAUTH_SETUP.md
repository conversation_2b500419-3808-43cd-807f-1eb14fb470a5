# Google OAuth Setup Guide

## Overview
This guide will help you set up Google OAuth authentication for the BITS DataScience Platform.

## Prerequisites
- Google Cloud Console access
- Domain ownership (for production)
- Basic understanding of OAuth 2.0

---

## Step 1: Google Cloud Console Setup

### 1.1 Create/Select Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. **Project Name:** `BITS DataScience Platform`
4. **Project ID:** `bits-datascience-platform` (or similar)

### 1.2 Enable Required APIs
1. Navigate to **APIs & Services** > **Library**
2. Search and enable these APIs:
   - **Google+ API** (for user profile data)
   - **Google OAuth2 API**
   - **Google Identity and Access Management (IAM) API**

### 1.3 Configure OAuth Consent Screen
1. Go to **APIs & Services** > **OAuth consent screen**
2. Choose **External** user type
3. Fill in the required information:

```
App name: BITS DataScience Platform
User support email: <EMAIL>
Developer contact information: <EMAIL>
```

4. **Scopes to add:**
   - `.../auth/userinfo.email`
   - `.../auth/userinfo.profile`

5. **Test users:** Add your email for testing

---

## Step 2: Create OAuth 2.0 Credentials

### 2.1 Create Web Application Client
1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Choose **Web application**

### 2.2 Configure Client Settings

#### **Development Configuration:**
```
Name: BITS DataScience Platform - Development
Authorized JavaScript origins:
- http://localhost:3000
- http://localhost:5001

Authorized redirect URIs:
- http://localhost:5001/api/auth/google/callback
```

#### **Production Configuration:**
```
Name: BITS DataScience Platform - Production
Authorized JavaScript origins:
- https://your-domain.com
- https://api.your-domain.com

Authorized redirect URIs:
- https://api.your-domain.com/api/auth/google/callback
```

### 2.3 Get Your Credentials
After creating the client, you'll receive:
- **Client ID:** `123456789-abcdefghijklmnop.apps.googleusercontent.com`
- **Client Secret:** `GOCSPX-abcdefghijklmnopqrstuvwxyz`

**⚠️ Important:** Keep your Client Secret secure and never commit it to version control!

---

## Step 3: Environment Configuration

### 3.1 Create Environment File
Create a `.env` file in your project root:

```bash
# Copy the example file
cp .env.example .env
```

### 3.2 Configure Google OAuth Variables
Add these to your `.env` file:

```bash
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
GOOGLE_CALLBACK_URL=http://localhost:5001/api/auth/google/callback

# Frontend URL (for redirects)
FRONTEND_URL=http://localhost:3000
```

### 3.3 Complete Environment Setup
Here's a complete `.env` template:

```bash
# =============================================================================
# BITS DataScience Platform - Environment Configuration
# =============================================================================

# Server Configuration
NODE_ENV=development
PORT=5001
HOST=localhost

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/bits_platform
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bits_platform
DB_USER=postgres
DB_PASSWORD=password

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT & Session Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
GOOGLE_CALLBACK_URL=http://localhost:5001/api/auth/google/callback

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:3000

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=bits-datascience-platform

# LTI Configuration
LTI_TOOL_URL=http://localhost:5001
LTI_PRIVATE_KEY=your-lti-private-key
LTI_PUBLIC_KEY=your-lti-public-key

# JupyterHub Configuration
JUPYTERHUB_URL=http://localhost:8000
JUPYTERHUB_TOKEN=your-jupyterhub-admin-token

# Sandbox Configuration
SANDBOX_API_BASE_URL=https://sandbox-api.example.com
SANDBOX_API_TOKEN=your-sandbox-api-token
```

---

## Step 4: Frontend Configuration

### 4.1 React Frontend Setup
If you're using React, add these environment variables to your frontend:

```bash
# .env (in your React app)
REACT_APP_API_BASE_URL=http://localhost:5001
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id-here
```

### 4.2 Google OAuth Library
Install the Google OAuth library:

```bash
npm install @react-oauth/google
```

### 4.3 Initialize Google OAuth
In your React app:

```javascript
import { GoogleOAuthProvider } from '@react-oauth/google';

function App() {
  return (
    <GoogleOAuthProvider clientId={process.env.REACT_APP_GOOGLE_CLIENT_ID}>
      {/* Your app components */}
    </GoogleOAuthProvider>
  );
}
```

---

## Step 5: Testing the Setup

### 5.1 Start the Backend
```bash
npm run dev
```

### 5.2 Test OAuth Flow
1. **Direct API Test:**
   ```bash
   curl -X GET http://localhost:5001/api/auth/google
   ```

2. **Frontend Integration Test:**
   - Open your frontend app
   - Click "Login with Google"
   - Complete the OAuth flow
   - Verify you receive a JWT token

### 5.3 Verify Configuration
Check these endpoints work:
- `GET /api/auth/google` - Initiates OAuth flow
- `GET /api/auth/google/callback` - OAuth callback
- `GET /health` - Health check

---

## Step 6: Production Deployment

### 6.1 Update OAuth Settings
1. Go back to Google Cloud Console
2. Update your OAuth client with production URLs:
   ```
   Authorized JavaScript origins:
   - https://your-domain.com
   
   Authorized redirect URIs:
   - https://api.your-domain.com/api/auth/google/callback
   ```

### 6.2 Environment Variables
Update your production `.env`:

```bash
NODE_ENV=production
GOOGLE_CLIENT_ID=your-production-client-id
GOOGLE_CLIENT_SECRET=your-production-client-secret
GOOGLE_CALLBACK_URL=https://api.your-domain.com/api/auth/google/callback
FRONTEND_URL=https://your-domain.com
```

### 6.3 Security Considerations
- Use strong JWT secrets
- Enable HTTPS in production
- Set up proper CORS configuration
- Use environment-specific OAuth clients

---

## Troubleshooting

### Common Issues

#### 1. "Invalid redirect_uri" Error
**Solution:** Ensure the redirect URI in Google Console matches exactly:
- Check for trailing slashes
- Verify protocol (http vs https)
- Confirm port numbers

#### 2. "Client ID not found" Error
**Solution:** 
- Verify `GOOGLE_CLIENT_ID` in your `.env` file
- Check that the OAuth client is properly configured
- Ensure the project has the required APIs enabled

#### 3. CORS Issues
**Solution:**
- Update `CORS_ORIGIN` in your `.env`
- Ensure frontend URL is included in CORS configuration

#### 4. "Access blocked" Error
**Solution:**
- Add your email to test users in OAuth consent screen
- Verify the app is not in restricted mode
- Check if your domain is authorized

### Debug Mode
Enable debug logging:

```bash
DEBUG=passport:* npm run dev
```

---

## Security Best Practices

### 1. Environment Variables
- Never commit `.env` files to version control
- Use different OAuth clients for development and production
- Rotate secrets regularly

### 2. OAuth Configuration
- Use HTTPS in production
- Implement proper state parameter validation
- Set up proper error handling

### 3. Token Management
- Implement token refresh logic
- Set appropriate token expiration times
- Store tokens securely

### 4. User Data
- Only request necessary scopes
- Implement proper user data validation
- Follow GDPR/privacy regulations

---

## Support

If you encounter issues:

1. **Check the logs:** Look for detailed error messages
2. **Verify configuration:** Double-check all environment variables
3. **Test step by step:** Use the testing endpoints
4. **Google Console:** Verify OAuth client configuration

For additional help, refer to:
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Passport.js Google Strategy](http://www.passportjs.org/packages/passport-google-oauth20/)
- [BITS DataScience Platform Documentation](./README.md)

---

**Last Updated:** August 17, 2025  
**Version:** 2.1.0  
**Maintainer:** BITS Pilani DataScience Team
