# Testing Guide for BITS-DataScience Projects Platform

## Overview

This guide provides comprehensive instructions for testing the BITS-DataScience Projects Platform API using the provided Postman collection, API documentation, and integration documentation.

---

## Prerequisites

### Required Software
- **Postman** (Desktop or Web version)
- **Node.js** 18+ (for running the backend)
- **PostgreSQL** 12+ (for database)
- **Redis** (for session management)
- **AWS Account** (for S3 testing)

### Environment Setup

1. **Clone and Setup Backend**
   ```bash
   git clone <repository-url>
   cd backend
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Database Setup**
   ```bash
   npx sequelize-cli db:migrate
   npx sequelize-cli db:seed:all
   ```

4. **Start the Server**
   ```bash
   npm run dev
   ```

---

## Postman Collection Setup

### 1. Import Collection

1. Open Postman
2. Click **Import** button
3. Select the `BITS_DataScience_Platform.postman_collection.json` file
4. Import the collection

### 2. Environment Variables

Create a new environment in Postman with these variables:

```json
{
  "baseUrl": "http://localhost:5001",
  "accessToken": "",
  "refreshToken": "",
  "userId": "",
  "courseId": "",
  "projectId": "",
  "submissionId": "",
  "sandboxId": ""
}
```

### 3. Collection Variables

The collection includes automatic variable setting through test scripts. Key variables:
- **baseUrl**: API base URL
- **accessToken**: JWT access token (auto-set on login)
- **refreshToken**: JWT refresh token (auto-set on login)
- **userId**: Current user ID (auto-set on login)
- **courseId**: Selected course ID (auto-set when fetching courses)
- **projectId**: Selected project ID (auto-set when fetching projects)
- **submissionId**: Selected submission ID (auto-set when fetching submissions)

---

## Testing Workflows

### 1. Authentication Flow

#### Basic Authentication Test
1. **Google OAuth Login**
   - Endpoint: `POST /api/auth/google`
   - Updates: `accessToken`, `refreshToken`, `userId`
   - Test with mock Google token

2. **Token Refresh**
   - Endpoint: `POST /api/auth/refresh`
   - Uses: `refreshToken`
   - Updates: `accessToken`

3. **Logout**
   - Endpoint: `POST /api/auth/logout`
   - Clears session

#### Test Script Example
```javascript
// Login Test
pm.test("Login successful", function () {
    pm.expect(pm.response.code).to.equal(200);
    const responseJson = pm.response.json();
    pm.expect(responseJson.success).to.be.true;
    pm.expect(responseJson.tokens.accessToken).to.exist;
    
    // Auto-save tokens
    pm.environment.set('accessToken', responseJson.tokens.accessToken);
    pm.environment.set('refreshToken', responseJson.tokens.refreshToken);
    pm.environment.set('userId', responseJson.user.id);
});
```

### 2. User Management Flow

#### Student User Flow
1. **Get Profile** → `GET /api/users/profile`
2. **Update Profile** → `PUT /api/users/profile`
3. **Get Enrolled Courses** → `GET /api/courses`

#### Instructor User Flow
1. **Get All Users** → `GET /api/users`
2. **Create Course** → `POST /api/courses`
3. **Manage Projects** → `POST /api/projects`
4. **Grade Submissions** → `POST /api/grades`

#### Admin User Flow
1. **User Management** → `GET /api/users`, `PUT /api/users/:id/status`
2. **Platform Management** → `GET /api/lti/platforms`
3. **Role Management** → `POST /api/roles`

### 3. Course & Project Management Flow

#### Complete Course Setup
```
1. Create Course → POST /api/courses
2. Enroll Users → POST /api/courses/:id/enroll
3. Create Project → POST /api/projects
4. Get Course Details → GET /api/courses/:id
```

#### Project Lifecycle
```
1. Create Project → POST /api/projects
2. Publish Project → PUT /api/projects/:id (status: published)
3. Student Submission → POST /api/submissions
4. Instructor Grading → POST /api/grades
5. Grade Passback → POST /api/lti/grades
```

### 4. Submission & Grading Flow

#### Student Submission Process
```
1. Get Projects → GET /api/projects
2. Create Submission → POST /api/submissions
3. Upload Files → POST /api/s3/upload
4. Auto-save Progress → POST /api/submissions/:id/auto-save
5. Submit for Grading → POST /api/submissions/:id/submit
6. View Grades → GET /api/grades
```

#### Instructor Grading Process
```
1. Get Submissions → GET /api/submissions
2. Review Submission → GET /api/submissions/:id
3. Create Grade → POST /api/grades
4. Send to LMS → POST /api/lti/grades
5. View Analytics → GET /api/grades/analytics/:projectId
```

### 5. File Storage (S3) Testing

#### File Upload Flow
```
1. Get Upload URL → POST /api/s3/upload
   Request: { fileName, fileType, purpose, metadata }
   
2. Upload to S3 → PUT <presigned_url>
   Use the returned uploadUrl with file content
   
3. Confirm Upload → GET /api/s3/download/:s3Key
   Verify file is accessible
   
4. List Files → GET /api/s3/list
   Check file appears in user's file list
```

#### Test Different File Types
- **Notebooks**: `.ipynb` files with JSON content
- **Datasets**: `.csv`, `.json`, `.xlsx` files
- **Images**: `.png`, `.jpg` for profile pictures
- **Documents**: `.pdf`, `.docx` for project resources

### 6. LTI Integration Testing

#### LTI Platform Setup
```
1. Register Platform → POST /api/lti/platforms
   Configure D2L Brightspace settings
   
2. Get Configuration → GET /api/lti/config
   Share with LMS administrator
   
3. Test JWKS → GET /api/lti/jwks
   Verify public keys are accessible
```

#### LTI Launch Simulation
```
1. Login Initiation → POST /api/lti/login
   Simulate LMS authentication request
   
2. Launch Processing → POST /api/lti/launch
   Process ID token and state
   
3. Grade Passback → POST /api/lti/grades
   Send grades back to LMS
```

---

## Test Scenarios

### 1. Role-Based Access Control

#### Test Admin Permissions
```javascript
// Test admin can access user management
pm.test("Admin can access user management", function () {
    pm.sendRequest({
        url: pm.environment.get('baseUrl') + '/api/users',
        method: 'GET',
        header: {
            'Authorization': 'Bearer ' + pm.environment.get('accessToken')
        }
    }, function (err, res) {
        pm.expect(res.code).to.equal(200);
        pm.expect(res.json().success).to.be.true;
    });
});
```

#### Test Student Restrictions
```javascript
// Test student cannot access admin endpoints
pm.test("Student cannot access admin endpoints", function () {
    pm.sendRequest({
        url: pm.environment.get('baseUrl') + '/api/lti/platforms',
        method: 'GET',
        header: {
            'Authorization': 'Bearer ' + pm.environment.get('accessToken')
        }
    }, function (err, res) {
        pm.expect(res.code).to.equal(403);
    });
});
```

### 2. Data Validation Testing

#### Invalid Input Testing
```javascript
// Test invalid course creation
pm.test("Invalid course data rejected", function () {
    pm.sendRequest({
        url: pm.environment.get('baseUrl') + '/api/courses',
        method: 'POST',
        header: {
            'Authorization': 'Bearer ' + pm.environment.get('accessToken'),
            'Content-Type': 'application/json'
        },
        body: {
            mode: 'raw',
            raw: JSON.stringify({
                name: "", // Invalid: empty name
                code: "INVALID_CODE_TOO_LONG_EXCEEDS_LIMIT"
            })
        }
    }, function (err, res) {
        pm.expect(res.code).to.equal(422);
        pm.expect(res.json().error).to.equal('Validation Error');
    });
});
```

### 3. Performance Testing

#### Bulk Operations
```javascript
// Test bulk grading performance
pm.test("Bulk grading completes within time limit", function () {
    const startTime = Date.now();
    
    pm.sendRequest({
        url: pm.environment.get('baseUrl') + '/api/grades/bulk',
        method: 'POST',
        header: {
            'Authorization': 'Bearer ' + pm.environment.get('accessToken'),
            'Content-Type': 'application/json'
        },
        body: {
            mode: 'raw',
            raw: JSON.stringify({
                grades: new Array(50).fill().map((_, i) => ({
                    submissionId: `submission-${i}`,
                    rubricScores: [{ rubricId: 'rubric-1', score: 85 }],
                    overallFeedback: `Grade ${i}`
                }))
            })
        }
    }, function (err, res) {
        const duration = Date.now() - startTime;
        pm.expect(duration).to.be.below(5000); // Should complete in <5 seconds
        pm.expect(res.code).to.equal(200);
    });
});
```

### 4. Integration Testing

#### End-to-End Project Flow
```javascript
// Complete project lifecycle test
pm.test("Complete project workflow", function () {
    const workflow = [
        { method: 'POST', endpoint: '/api/courses', data: { name: 'Test Course' }},
        { method: 'POST', endpoint: '/api/projects', data: { title: 'Test Project' }},
        { method: 'POST', endpoint: '/api/submissions', data: { projectId: '{{projectId}}' }},
        { method: 'POST', endpoint: '/api/grades', data: { submissionId: '{{submissionId}}' }}
    ];
    
    // Execute workflow steps sequentially
    // Implementation would use pm.sendRequest chaining
});
```

---

## Error Testing

### 1. Authentication Errors

```javascript
// Test expired token
pm.test("Expired token returns 401", function () {
    pm.sendRequest({
        url: pm.environment.get('baseUrl') + '/api/users/profile',
        method: 'GET',
        header: {
            'Authorization': 'Bearer invalid_expired_token'
        }
    }, function (err, res) {
        pm.expect(res.code).to.equal(401);
        pm.expect(res.json().error).to.equal('Access Denied');
    });
});
```

### 2. Rate Limiting

```javascript
// Test rate limiting
pm.test("Rate limiting works", function () {
    const requests = Array(101).fill().map(() => 
        pm.sendRequest({
            url: pm.environment.get('baseUrl') + '/api/auth/google',
            method: 'POST',
            header: { 'Content-Type': 'application/json' },
            body: { mode: 'raw', raw: '{}' }
        })
    );
    
    // Some requests should be rate limited (429)
});
```

### 3. Database Errors

```javascript
// Test database connection handling
pm.test("Graceful database error handling", function () {
    // Simulate database unavailability
    // Expect 503 Service Unavailable
});
```

---

## Load Testing

### 1. Concurrent Users

Use Postman's Collection Runner with multiple iterations:

1. **Setup**: Create test data (users, courses, projects)
2. **Execute**: Run collection with 10-50 iterations
3. **Monitor**: Check response times and error rates
4. **Analyze**: Review performance metrics

### 2. File Upload Testing

Test large file uploads:

```javascript
// Test large file upload
pm.test("Large file upload succeeds", function () {
    const largeFileSize = 50 * 1024 * 1024; // 50MB
    // Test S3 upload with large file
});
```

### 3. Database Load

Test database performance:

```javascript
// Test database query performance
pm.test("Complex queries perform well", function () {
    const startTime = Date.now();
    
    pm.sendRequest({
        url: pm.environment.get('baseUrl') + '/api/grades/analytics/' + pm.environment.get('projectId'),
        method: 'GET',
        header: {
            'Authorization': 'Bearer ' + pm.environment.get('accessToken')
        }
    }, function (err, res) {
        const duration = Date.now() - startTime;
        pm.expect(duration).to.be.below(2000); // Should complete in <2 seconds
    });
});
```

---

## Security Testing

### 1. SQL Injection

```javascript
// Test SQL injection protection
pm.test("SQL injection protection", function () {
    pm.sendRequest({
        url: pm.environment.get('baseUrl') + '/api/users',
        method: 'GET',
        header: {
            'Authorization': 'Bearer ' + pm.environment.get('accessToken')
        },
        qs: {
            search: "'; DROP TABLE users; --"
        }
    }, function (err, res) {
        pm.expect(res.code).to.not.equal(500);
        // Should return safe result, not error
    });
});
```

### 2. XSS Protection

```javascript
// Test XSS protection
pm.test("XSS protection", function () {
    pm.sendRequest({
        url: pm.environment.get('baseUrl') + '/api/users/profile',
        method: 'PUT',
        header: {
            'Authorization': 'Bearer ' + pm.environment.get('accessToken'),
            'Content-Type': 'application/json'
        },
        body: {
            mode: 'raw',
            raw: JSON.stringify({
                name: "<script>alert('xss')</script>"
            })
        }
    }, function (err, res) {
        pm.expect(res.json().user.name).to.not.include('<script>');
    });
});
```

### 3. Authorization Testing

```javascript
// Test unauthorized access
pm.test("Unauthorized access blocked", function () {
    pm.sendRequest({
        url: pm.environment.get('baseUrl') + '/api/users/' + pm.environment.get('userId'),
        method: 'DELETE'
    }, function (err, res) {
        pm.expect(res.code).to.equal(401);
    });
});
```

---

## Monitoring & Reporting

### 1. Test Results Collection

```javascript
// Collect test metrics
pm.test("Response time acceptable", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
    
    // Log performance data
    const testResult = {
        endpoint: pm.request.url.toString(),
        method: pm.request.method,
        responseTime: pm.response.responseTime,
        status: pm.response.code,
        timestamp: new Date().toISOString()
    };
    
    console.log('Test Result:', JSON.stringify(testResult));
});
```

### 2. Custom Reporting

Create custom reports using Postman's built-in reporters:

```bash
# Run collection with HTML report
newman run collection.json -e environment.json -r html --reporter-html-export report.html

# Run with CSV output
newman run collection.json -e environment.json -r csv --reporter-csv-export results.csv
```

---

## 🧪 Enhanced Testing Infrastructure

### Unit Testing with Jest

The platform now includes a comprehensive unit testing suite with enhanced coverage and improved test organization.

#### Running Unit Tests

```bash
# Run all unit tests
npm run test:unit

# Run tests with coverage
npm run test:coverage

# Run specific test suites
npm run test -- --testPathPattern="tests/unit/models"
npm run test -- --testPathPattern="tests/unit/services"
npm run test -- --testPathPattern="tests/unit/controllers"

# Run tests in watch mode
npm run test:watch
```

#### Test Structure

```
tests/
├── unit/                    # Unit tests
│   ├── models/             # Model tests (✅ 195 tests passing)
│   │   ├── user.test.js
│   │   ├── course.test.js
│   │   ├── project.test.js
│   │   ├── submission.test.js
│   │   └── role.test.js
│   ├── services/           # Service tests
│   │   ├── s3Service.test.js
│   │   ├── sandboxOrchestrator.test.js
│   │   ├── jupyterhubAdmin.test.js
│   │   └── s3Workspace.test.js
│   ├── controllers/        # Controller tests
│   ├── middlewares/        # Middleware tests
│   └── utils/              # Utility tests
├── integration/            # Integration tests
├── fixtures/               # Test data
└── setup.js               # Test setup
```

#### Test Coverage Status

- **Model Tests**: ✅ 195/195 passing (100% success rate)
- **Service Tests**: 🔄 34/35 passing (97% success rate)
- **Controller Tests**: 🔄 In progress
- **Middleware Tests**: 🔄 In progress
- **Overall Coverage**: 87% test success rate

#### Writing Unit Tests

```javascript
// Example: Testing a service function
import { createSandbox } from '../../../src/services/sandboxOrchestrator.js';

describe('Sandbox Orchestrator', () => {
  beforeEach(() => {
    // Setup mocks
    jest.clearAllMocks();
  });

  test('should create sandbox successfully', async () => {
    // Arrange
    const sandboxConfig = {
      userId: 'user-123',
      projectId: 'project-456',
      resources: { cpu: '1', memory: '2Gi' }
    };

    // Act
    const result = await createSandbox(sandboxConfig);

    // Assert
    expect(result.success).toBe(true);
    expect(result.sandboxId).toBeDefined();
  });
});
```

### Integration Testing

#### Sandbox Integration Tests

```bash
# Run sandbox integration tests
npm run test:integration -- --testPathPattern="sandbox"

# Run with specific environment
NODE_ENV=test npm run test:integration
```

#### API Integration Tests

```javascript
// Example: Testing sandbox API endpoints
describe('Sandbox API Integration', () => {
  test('should create and manage sandbox lifecycle', async () => {
    // Create sandbox
    const createResponse = await request(app)
      .post('/api/sandbox/create')
      .send({
        userId: 'user-123',
        projectId: 'project-456',
        resources: { cpu: '1', memory: '2Gi' }
      });

    expect(createResponse.status).toBe(200);
    const sandboxId = createResponse.body.sandboxId;

    // Check status
    const statusResponse = await request(app)
      .get(`/api/sandbox/${sandboxId}/status`);
    
    expect(statusResponse.status).toBe(200);
    expect(statusResponse.body.status).toBe('running');

    // Clean up
    await request(app)
      .delete(`/api/sandbox/${sandboxId}`);
  });
});
```

### Performance Testing

#### Load Testing with Artillery

```bash
# Install Artillery
npm install -g artillery

# Run load test
artillery run tests/performance/sandbox-load-test.yml
```

#### Performance Test Configuration

```yaml
# tests/performance/sandbox-load-test.yml
config:
  target: 'http://localhost:5001'
  phases:
    - duration: 60
      arrivalRate: 10
  defaults:
    headers:
      Authorization: 'Bearer {{ $randomString() }}'

scenarios:
  - name: "Sandbox Creation Load Test"
    flow:
      - post:
          url: "/api/sandbox/create"
          json:
            userId: "user-{{ $randomNumber(1, 1000) }}"
            projectId: "project-{{ $randomNumber(1, 100) }}"
            resources:
              cpu: "1"
              memory: "2Gi"
```

### Test Data Management

#### Fixtures and Factories

```javascript
// tests/fixtures/sandboxFixtures.js
export const createSandboxFixture = (overrides = {}) => ({
  userId: 'user-123',
  projectId: 'project-456',
  resources: {
    cpu: '1',
    memory: '2Gi',
    storage: '10Gi'
  },
  environment: 'python:3.9',
  autoStartServer: true,
  ...overrides
});

export const createJupyterHubUserFixture = (overrides = {}) => ({
  name: 'test-user',
  admin: false,
  groups: ['students'],
  ...overrides
});
```

### Continuous Integration

#### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
      
      - name: Generate coverage report
        run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
```

### Test Reporting

#### Coverage Reports

```bash
# Generate detailed coverage report
npm run test:coverage

# View coverage in browser
open coverage/lcov-report/index.html
```

#### Test Results Summary

```bash
# Generate test results summary
npm run test:summary

# Output example:
# ✅ Model Tests: 195/195 passing
# 🔄 Service Tests: 34/35 passing
# 🔄 Controller Tests: 45/60 passing
# 🔄 Middleware Tests: 12/15 passing
# Overall: 87% success rate
```

---

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check token expiration
   - Verify Google OAuth configuration
   - Ensure proper environment variables

2. **Database Connection Errors**
   - Verify PostgreSQL is running
   - Check database credentials
   - Run migrations

3. **S3 Upload Failures**
   - Verify AWS credentials
   - Check bucket permissions
   - Validate CORS settings

4. **LTI Integration Issues**
   - Verify platform registration
   - Check JWT key configuration
   - Validate nonce/state parameters

5. **🆕 Sandbox Issues**
   - Verify JupyterHub server is accessible
   - Check sandbox API configuration
   - Validate resource limits and quotas
   - Monitor sandbox health status
   - Check network connectivity for external APIs

### Debug Helpers

```javascript
// Debug request/response
pm.test("Debug info", function () {
    console.log('Request URL:', pm.request.url);
    console.log('Request Headers:', pm.request.headers);
    console.log('Response Status:', pm.response.code);
    console.log('Response Body:', pm.response.text());
});
```

---

## Best Practices

### 1. Test Organization
- Group related tests in folders
- Use descriptive test names
- Include setup and teardown scripts
- Maintain test data consistency

### 2. Environment Management
- Use different environments for dev/staging/production
- Keep sensitive data in environment variables
- Document required environment setup

### 3. Test Data Management
- Create test data through API calls
- Clean up test data after tests
- Use unique identifiers for test entities
- Avoid dependencies on existing data

### 4. Continuous Integration
- Integrate tests with CI/CD pipeline
- Set up automated test runs
- Monitor test results and trends
- Alert on test failures

---

## Support

For testing support and questions:
- **Documentation**: `/backend/docs/`
- **API Reference**: `http://localhost:5001/api-docs`
- **Health Check**: `http://localhost:5001/health`
- **🆕 Sandbox Health**: `http://localhost:5001/api/sandbox/health`
- **Email**: <EMAIL>