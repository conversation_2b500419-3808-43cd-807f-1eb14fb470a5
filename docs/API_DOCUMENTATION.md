# BITS-DataScience Projects Platform API Documentation

## Overview

The BITS-DataScience Projects Platform provides a comprehensive REST API for managing role-based learning, project assignments, submissions, and grading. The platform integrates with D2L-Brightspace LMS via LTI 1.3 OIDC, supports Google OAuth authentication, and uses AWS S3 for file storage.

## Base URL

```
Production: https://api.bits-datascience.edu
Development: http://localhost:5001
```

## Authentication

The API supports multiple authentication methods:

### Authentication Methods

1. **JWT Authentication**: Traditional username/password login
2. **Google OAuth**: Social login integration
3. **LTI 1.3 OIDC**: Learning Management System integration (Brightspace D2L)

### Authentication Flow

#### JWT/Google OAuth Flow
1. **Login**: `POST /api/auth/login` or `POST /api/auth/google`
2. **Token Generation**: Receive JWT access token
3. **API Access**: Include JWT in Authorization header

#### LTI 1.3 OIDC Flow
1. **OIDC Initiation**: `GET/POST /api/lti/oidc/init`
2. **Platform Authentication**: Redirect to Brightspace D2L
3. **OIDC Callback**: `POST /api/lti/oidc/callback`
4. **Session Creation**: LTI session established
5. **API Access**: Use LTI session for protected endpoints

### Headers

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## API Endpoints

### Authentication & Session Management

#### POST /api/auth/google
Google OAuth authentication and LMS synchronization.

**Request Body:**
```json
{
  "googleToken": "string",
  "lmsData": {
    "userId": "string",
    "email": "string",
    "name": "string",
    "roles": ["string"]
  }
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "name": "string",
    "email": "string",
    "role": "student|instructor|admin",
    "status": "active|inactive|pending"
  },
  "tokens": {
    "accessToken": "string",
    "refreshToken": "string",
    "expiresIn": 3600
  }
}
```

#### POST /api/auth/refresh
Refresh JWT access token.

**Request Body:**
```json
{
  "refreshToken": "string"
}
```

#### POST /api/auth/logout
Logout and invalidate tokens.

#### POST /api/auth/logout-all
Logout from all devices.

#### GET /api/auth/logout/stats
Get logout blacklist statistics (Admin only).

---

### LTI 1.3 Integration

#### GET /api/lti/config
Get LTI tool configuration for platform registration.

**Response:**
```json
{
  "title": "BITS-DataScience Projects Platform",
  "description": "Interactive data science projects and assignments platform for BITS Pilani",
  "target_link_uri": "http://localhost:5001/lti/launch",
  "oidc_initiation_url": "http://localhost:5001/api/lti/oidc/init",
  "public_jwk_url": "http://localhost:5001/.well-known/jwks.json",
  "scopes": [
    "openid",
    "https://purl.imsglobal.org/spec/lti-ags/scope/lineitem",
    "https://purl.imsglobal.org/spec/lti-ags/scope/score",
    "https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly"
  ]
}
```

#### GET/POST /api/lti/oidc/init
OIDC login initiation endpoint.

**Query Parameters:**
- `iss`: Platform issuer identifier
- `login_hint`: User identifier hint
- `target_link_uri`: Target URI for launch
- `client_id`: OAuth client ID
- `lti_message_hint`: LTI message hint

#### POST /api/lti/oidc/callback
OIDC callback endpoint.

**Request Body:**
```json
{
  "code": "authorization-code",
  "state": "state-parameter"
}
```

#### GET /api/lti/session
Get current LTI session data.

**Response:**
```json
{
  "success": true,
  "session": {
    "user": {
      "id": "uuid",
      "email": "string",
      "name": "string",
      "roles": ["string"]
    },
    "context": {
      "id": "uuid",
      "contextId": "string",
      "title": "string",
      "label": "string"
    },
    "resourceLink": {
      "id": "uuid",
      "resourceLinkId": "string",
      "title": "string"
    },
    "launchData": {
      "messageType": "string",
      "version": "string",
      "deploymentId": "string"
    }
  }
}
```

#### GET /api/lti/roster/:contextId
Get course roster (LTI NRPS).

#### POST /api/lti/grades
Submit grade (LTI AGS).

**Request Body:**
```json
{
  "lineItemId": "string",
  "userId": "string",
  "score": 85,
  "maxScore": 100,
  "comment": "string"
}
```

#### POST /api/lti/cleanup
Clean up expired LTI sessions (Admin only).

---

### User Management

#### GET /api/users
Get paginated list of users (Admin/Instructor only).

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search by name or email
- `role`: Filter by role
- `status`: Filter by status

**Response:**
```json
{
  "success": true,
  "users": [
    {
      "id": "uuid",
      "name": "string",
      "email": "string",
      "role": "string",
      "status": "string",
      "profileData": {},
      "createdAt": "datetime",
      "lastLoginAt": "datetime"
    }
  ],
  "pagination": {
    "current": 1,
    "total": 10,
    "pages": 5,
    "limit": 10
  }
}
```

#### GET /api/users/profile
Get current user profile.

#### PUT /api/users/profile
Update current user profile.

**Request Body:**
```json
{
  "name": "string",
  "profileData": {
    "firstName": "string",
    "lastName": "string",
    "bio": "string",
    "preferences": {}
  }
}
```

#### GET /api/users/:id
Get user by ID (Admin/Instructor only).

#### PUT /api/users/:id/status
Update user status (Admin only).

**Request Body:**
```json
{
  "status": "active|inactive|suspended"
}
```

#### DELETE /api/users/:id
Soft delete user (Admin only).

---

### Course Management

#### GET /api/courses
Get courses for current user.

**Query Parameters:**
- `status`: Filter by course status
- `semester`: Filter by semester
- `search`: Search course name or code

**Response:**
```json
{
  "success": true,
  "courses": [
    {
      "id": "uuid",
      "name": "string",
      "code": "string",
      "description": "string",
      "semester": "string",
      "status": "active|archived",
      "instructor": {
        "id": "uuid",
        "name": "string"
      },
      "enrollmentCount": 0,
      "projectCount": 0
    }
  ]
}
```

#### POST /api/courses
Create new course (Instructor/Admin only).

**Request Body:**
```json
{
  "name": "string",
  "code": "string",
  "description": "string",
  "semester": "string",
  "settings": {
    "allowLateSubmissions": true,
    "autoGrading": false,
    "maxAttempts": 3
  }
}
```

#### GET /api/courses/:id
Get course details.

#### PUT /api/courses/:id
Update course (Instructor/Admin only).

#### DELETE /api/courses/:id
Delete course (Admin only).

#### GET /api/courses/:id/enrollments
Get course enrollments.

#### POST /api/courses/:id/enroll
Enroll user in course.

**Request Body:**
```json
{
  "userId": "uuid",
  "role": "student|ta"
}
```

#### DELETE /api/courses/:id/enrollments/:userId
Remove user from course.

---

### Project Management

#### GET /api/projects
Get projects based on user role and course access.

**Query Parameters:**
- `courseId`: Filter by course
- `status`: Filter by status
- `difficulty`: Filter by difficulty level
- `search`: Search title or description

**Response:**
```json
{
  "success": true,
  "projects": [
    {
      "id": "uuid",
      "title": "string",
      "description": "string",
      "courseId": "uuid",
      "difficultyLevel": "beginner|intermediate|advanced",
      "status": "draft|published|archived",
      "dueDate": "datetime",
      "maxAttempts": 3,
      "isTemplate": false,
      "rubrics": [
        {
          "id": "uuid",
          "criteria": "string",
          "maxScore": 100,
          "weight": 0.3
        }
      ],
      "course": {
        "name": "string",
        "code": "string"
      }
    }
  ]
}
```

#### POST /api/projects
Create new project (Instructor/Admin only).

**Request Body:**
```json
{
  "title": "string",
  "description": "string",
  "courseId": "uuid",
  "difficultyLevel": "beginner|intermediate|advanced",
  "dueDate": "datetime",
  "maxAttempts": 3,
  "instructions": "string",
  "requirements": ["string"],
  "resources": [
    {
      "type": "dataset|notebook|documentation",
      "name": "string",
      "url": "string",
      "description": "string"
    }
  ],
  "rubrics": [
    {
      "criteria": "string",
      "description": "string",
      "maxScore": 100,
      "weight": 0.3
    }
  ],
  "isTemplate": false
}
```

#### GET /api/projects/:id
Get project details.

#### PUT /api/projects/:id
Update project (Creator/Admin only).

#### DELETE /api/projects/:id
Delete project (Creator/Admin only).

#### POST /api/projects/:id/duplicate
Duplicate project as template.

#### GET /api/projects/templates
Get project templates.

---

### Submission Management

#### GET /api/submissions
Get submissions based on user role.

**Query Parameters:**
- `projectId`: Filter by project
- `userId`: Filter by user (Instructor/Admin only)
- `status`: Filter by status
- `graded`: Filter by grading status

**Response:**
```json
{
  "success": true,
  "submissions": [
    {
      "id": "uuid",
      "projectId": "uuid",
      "userId": "uuid",
      "status": "draft|submitted|graded",
      "submittedAt": "datetime",
      "attempt": 1,
      "notebooks": [
        {
          "name": "string",
          "s3Key": "string",
          "size": 1024,
          "lastModified": "datetime"
        }
      ],
      "files": [
        {
          "name": "string",
          "s3Key": "string",
          "type": "string",
          "size": 1024
        }
      ],
      "project": {
        "title": "string",
        "dueDate": "datetime"
      },
      "user": {
        "name": "string",
        "email": "string"
      },
      "grade": {
        "totalScore": 85,
        "maxScore": 100,
        "feedback": "string"
      }
    }
  ]
}
```

#### POST /api/submissions
Create new submission.

**Request Body:**
```json
{
  "projectId": "uuid",
  "notebooks": [
    {
      "name": "string",
      "content": "string",
      "cellOutputs": []
    }
  ],
  "files": [
    {
      "name": "string",
      "s3Key": "string",
      "type": "string"
    }
  ],
  "metadata": {
    "environment": "jupyter",
    "packages": ["pandas", "numpy"],
    "executionTime": 1200
  }
}
```

#### GET /api/submissions/:id
Get submission details.

#### PUT /api/submissions/:id
Update submission (Owner only, before submission).

#### POST /api/submissions/:id/submit
Submit for grading.

#### POST /api/submissions/:id/auto-save
Auto-save submission progress.

**Request Body:**
```json
{
  "notebooks": [
    {
      "name": "string",
      "content": "string",
      "cellOutputs": []
    }
  ]
}
```

#### GET /api/submissions/:id/files/:filename
Download submission file.

---

### Grade Management

#### GET /api/grades
Get grades based on user role.

**Query Parameters:**
- `submissionId`: Filter by submission
- `projectId`: Filter by project
- `userId`: Filter by user
- `evaluatorId`: Filter by evaluator

**Response:**
```json
{
  "success": true,
  "grades": [
    {
      "id": "uuid",
      "submissionId": "uuid",
      "evaluatorId": "uuid",
      "totalScore": 85,
      "maxScore": 100,
      "feedback": "string",
      "rubricScores": [
        {
          "rubricId": "uuid",
          "score": 25,
          "maxScore": 30,
          "feedback": "string"
        }
      ],
      "gradedAt": "datetime",
      "submission": {
        "project": {
          "title": "string"
        },
        "user": {
          "name": "string"
        }
      }
    }
  ]
}
```

#### POST /api/grades
Create grade for submission (Instructor/Admin only).

**Request Body:**
```json
{
  "submissionId": "uuid",
  "rubricScores": [
    {
      "rubricId": "uuid",
      "score": 25,
      "feedback": "string"
    }
  ],
  "overallFeedback": "string",
  "sendNotification": true
}
```

#### PUT /api/grades/:id
Update grade (Evaluator/Admin only).

#### DELETE /api/grades/:id
Delete grade (Admin only).

#### POST /api/grades/bulk
Bulk grade submissions.

**Request Body:**
```json
{
  "grades": [
    {
      "submissionId": "uuid",
      "rubricScores": [],
      "overallFeedback": "string"
    }
  ]
}
```

#### GET /api/grades/analytics/:projectId
Get grading analytics for project.

---

### Role & Permission Management

#### GET /api/roles
Get all roles (Admin only).

#### POST /api/roles
Create new role (Admin only).

**Request Body:**
```json
{
  "name": "string",
  "description": "string",
  "permissions": ["uuid"]
}
```

#### GET /api/roles/permissions
Get all permissions.

#### PUT /api/roles/:id/permissions
Update role permissions (Admin only).

#### POST /api/users/:userId/roles
Assign role to user (Admin only).

#### DELETE /api/users/:userId/roles/:roleId
Remove role from user (Admin only).

---

### File Storage (S3 Integration)

#### POST /api/s3/upload
Get presigned URL for file upload.

**Request Body:**
```json
{
  "fileName": "string",
  "fileType": "string",
  "purpose": "submission|project|profile",
  "metadata": {
    "projectId": "uuid",
    "submissionId": "uuid"
  }
}
```

**Response:**
```json
{
  "success": true,
  "uploadUrl": "string",
  "s3Key": "string",
  "downloadUrl": "string"
}
```

#### GET /api/s3/download/:s3Key
Get presigned URL for file download.

#### DELETE /api/s3/:s3Key
Delete file from S3 (Owner/Admin only).

#### GET /api/s3/list
List user's files.

**Query Parameters:**
- `purpose`: Filter by purpose
- `projectId`: Filter by project

---

### Sandbox Environment Management

#### POST /api/sandbox/create
Create a new sandbox environment for data science projects.

**Request Body:**
```json
{
  "userId": "uuid",
  "projectId": "uuid",
  "resources": {
    "cpu": "string",
    "memory": "string",
    "storage": "string"
  },
  "environment": "string",
  "autoStartServer": "boolean"
}
```

**Response:**
```json
{
  "success": true,
  "sandboxId": "string",
  "workspace": {
    "workspaceId": "string",
    "s3Prefix": "string"
  },
  "jupyterhubUser": "string",
  "jupyterhubUserCreated": true,
  "serverStarted": true,
  "message": "string"
}
```

#### DELETE /api/sandbox/:sandboxId
Delete a sandbox environment.

**Response:**
```json
{
  "success": true,
  "message": "Sandbox deleted successfully"
}
```

#### GET /api/sandbox/:sandboxId/status
Get sandbox environment status.

**Response:**
```json
{
  "success": true,
  "status": "creating|running|stopped|error",
  "resources": {
    "cpu": "string",
    "memory": "string",
    "storage": "string"
  },
  "uptime": "string",
  "url": "string"
}
```

#### PUT /api/sandbox/:sandboxId/resources
Update sandbox resource allocation.

**Request Body:**
```json
{
  "cpu": "string",
  "memory": "string",
  "storage": "string"
}
```

#### GET /api/sandbox/list
List user's sandbox environments.

**Query Parameters:**
- `userId`: Filter by user ID
- `projectId`: Filter by project ID
- `status`: Filter by status

**Response:**
```json
{
  "success": true,
  "userId": "string",
  "sandboxes": [
    {
      "sandboxId": "string",
      "projectId": "string",
      "status": "string",
      "createdAt": "string",
      "serverStatus": "string"
    }
  ],
  "count": "number"
}
```

#### GET /api/sandbox/:sandboxId/logs
Get sandbox operation logs.

**Query Parameters:**
- `level`: Log level (INFO|WARN|ERROR)
- `limit`: Number of log entries (default: 50)
- `since`: ISO timestamp for filtering

#### POST /api/sandbox/:sandboxId/restart
Restart a sandbox environment.

**Request Body:**
```json
{
  "force": "boolean"
}
```

#### POST /api/sandbox/:sandboxId/scale
Scale sandbox resources.

**Request Body:**
```json
{
  "replicas": "number",
  "autoScaling": "boolean"
}
```

#### GET /api/sandbox/health
Get sandbox service health status.

**Response:**
```json
{
  "success": true,
  "status": "healthy|degraded|unhealthy",
  "services": {
    "jupyterhub": "string",
    "sandboxApi": "string",
    "s3Workspace": "string"
  },
  "timestamp": "string"
}
```

---

### JupyterHub Management

#### POST /api/jupyterhub/users
Create a new JupyterHub user.

**Request Body:**
```json
{
  "name": "string",
  "admin": "boolean",
  "groups": ["string"]
}
```

#### DELETE /api/jupyterhub/users/:username
Delete a JupyterHub user.

#### GET /api/jupyterhub/users/:username
Get JupyterHub user information.

#### GET /api/jupyterhub/users
List all JupyterHub users.

#### POST /api/jupyterhub/users/:username/servers/:serverName
Start a JupyterHub server for a user.

**Request Body:**
```json
{
  "image": "string",
  "resources": {
    "cpu": "string",
    "memory": "string"
  }
}
```

#### DELETE /api/jupyterhub/users/:username/servers/:serverName
Stop a JupyterHub server.

#### GET /api/jupyterhub/users/:username/servers/:serverName
Get server status.

#### GET /api/jupyterhub/users/:username/servers
List user's servers.

#### POST /api/jupyterhub/groups
Create a new JupyterHub group.

**Request Body:**
```json
{
  "name": "string",
  "users": ["string"]
}
```

#### DELETE /api/jupyterhub/groups/:groupName
Delete a JupyterHub group.

#### GET /api/jupyterhub/groups
List all JupyterHub groups.

#### POST /api/jupyterhub/groups/:groupName/users
Add users to a group.

#### DELETE /api/jupyterhub/groups/:groupName/users/:username
Remove user from a group.

---

### LMS Integration

#### POST /api/lms/sync/users
Sync users from LMS (Admin only).

#### POST /api/lms/sync/courses
Sync courses from LMS (Admin only).

#### GET /api/lms/status
Get LMS sync status.

---

### LTI Integration

#### GET /api/lti/config
Get LTI tool configuration for platform registration.

#### POST /api/lti/login
LTI login initiation endpoint.

#### POST /api/lti/launch
LTI launch endpoint for resource link requests.

#### GET /api/lti/jwks
Get tool's public key set for JWT verification.

#### POST /api/lti/deep-linking
Handle LTI deep linking for content selection.

#### GET /api/lti/platforms
Get registered LTI platforms (Admin only).

#### POST /api/lti/platforms
Register new LTI platform (Admin only).

**Request Body:**
```json
{
  "platformId": "string",
  "platformName": "string",
  "clientId": "string",
  "authLoginUrl": "string",
  "authTokenUrl": "string",
  "keySetUrl": "string",
  "settings": {}
}
```

#### POST /api/lti/grades
Send grade to LMS via Assignment and Grade Services.

**Request Body:**
```json
{
  "submissionId": "uuid",
  "gradeId": "uuid"
}
```

---

## Error Handling

### Standard Error Response

```json
{
  "success": false,
  "error": "Error Type",
  "message": "Human-readable error message",
  "details": {},
  "timestamp": "datetime",
  "path": "/api/endpoint"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `500` - Internal Server Error

### Common Validation Errors

```json
{
  "success": false,
  "error": "Validation Error",
  "message": "Request validation failed",
  "details": {
    "field": "email",
    "message": "Valid email is required"
  }
}
```

---

## Data Models

### User Model
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  lmsUserId?: string;
  status: 'active' | 'inactive' | 'suspended';
  profileData: {
    firstName?: string;
    lastName?: string;
    bio?: string;
    avatar?: string;
    preferences: Record<string, any>;
  };
  roles: Role[];
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}
```

### Course Model
```typescript
interface Course {
  id: string;
  name: string;
  code: string;
  description?: string;
  semester: string;
  instructorId: string;
  status: 'active' | 'archived';
  settings: {
    allowLateSubmissions: boolean;
    autoGrading: boolean;
    maxAttempts: number;
  };
  instructor: User;
  enrollments: CourseEnrollment[];
  projects: Project[];
}
```

### Project Model
```typescript
interface Project {
  id: string;
  title: string;
  description: string;
  courseId: string;
  creatorId: string;
  difficultyLevel: 'beginner' | 'intermediate' | 'advanced';
  status: 'draft' | 'published' | 'archived';
  dueDate?: Date;
  maxAttempts: number;
  instructions?: string;
  requirements: string[];
  resources: ProjectResource[];
  rubrics: Rubric[];
  isTemplate: boolean;
  course: Course;
  submissions: Submission[];
}
```

### Submission Model
```typescript
interface Submission {
  id: string;
  projectId: string;
  userId: string;
  status: 'draft' | 'submitted' | 'graded';
  submittedAt?: Date;
  attempt: number;
  notebooks: NotebookFile[];
  files: SubmissionFile[];
  metadata: Record<string, any>;
  project: Project;
  user: User;
  grade?: Grade;
}
```

### Grade Model
```typescript
interface Grade {
  id: string;
  submissionId: string;
  evaluatorId: string;
  totalScore: number;
  maxScore: number;
  feedback?: string;
  rubricScores: RubricScore[];
  gradedAt: Date;
  submission: Submission;
  evaluator: User;
}
```

---

## Rate Limiting

- **Authentication**: 5 requests per minute
- **File Upload**: 10 requests per minute
- **General API**: 100 requests per minute
- **LTI Endpoints**: 20 requests per minute

---

## Webhooks

### Grade Update Webhook
Triggered when a grade is created or updated.

**Payload:**
```json
{
  "event": "grade.updated",
  "data": {
    "gradeId": "uuid",
    "submissionId": "uuid",
    "projectId": "uuid",
    "userId": "uuid",
    "totalScore": 85,
    "maxScore": 100
  },
  "timestamp": "datetime"
}
```

### Submission Update Webhook
Triggered when a submission is submitted.

**Payload:**
```json
{
  "event": "submission.submitted",
  "data": {
    "submissionId": "uuid",
    "projectId": "uuid",
    "userId": "uuid",
    "attempt": 1
  },
  "timestamp": "datetime"
}
```

---

## SDK Examples

### JavaScript/TypeScript

```typescript
import axios from 'axios';

class BitsDataScienceAPI {
  private baseURL = 'https://api.bits-datascience.edu';
  private token: string;

  constructor(token: string) {
    this.token = token;
  }

  private get headers() {
    return {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json'
    };
  }

  async getProjects(courseId?: string) {
    const params = courseId ? { courseId } : {};
    const response = await axios.get(`${this.baseURL}/api/projects`, {
      headers: this.headers,
      params
    });
    return response.data;
  }

  async submitProject(projectId: string, notebooks: any[], files: any[]) {
    const response = await axios.post(`${this.baseURL}/api/submissions`, {
      projectId,
      notebooks,
      files
    }, {
      headers: this.headers
    });
    return response.data;
  }
}
```

### Python

```python
import requests
from typing import Optional, List, Dict, Any

class BitsDataScienceAPI:
    def __init__(self, token: str, base_url: str = "https://api.bits-datascience.edu"):
        self.base_url = base_url
        self.token = token
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
    
    def get_projects(self, course_id: Optional[str] = None) -> Dict[str, Any]:
        params = {"courseId": course_id} if course_id else {}
        response = requests.get(
            f"{self.base_url}/api/projects",
            headers=self.headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def submit_project(self, project_id: str, notebooks: List[Dict], files: List[Dict]) -> Dict[str, Any]:
        data = {
            "projectId": project_id,
            "notebooks": notebooks,
            "files": files
        }
        response = requests.post(
            f"{self.base_url}/api/submissions",
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()
```

---

## Testing

### Health Check
```bash
curl -X GET https://api.bits-datascience.edu/health
```

### Authentication Test
```bash
curl -X POST https://api.bits-datascience.edu/api/auth/google \
  -H "Content-Type: application/json" \
  -d '{"googleToken": "your-google-token"}'
```

---

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.bits-datascience.edu
- Status Page: https://status.bits-datascience.edu

---

**Last Updated**: August 20, 2025  
**Version**: 2.2.0  
**Maintainer**: BITS Pilani DataScience Team