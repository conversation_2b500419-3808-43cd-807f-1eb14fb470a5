# Integration Test Setup Guide

This document explains how to set up and run integration tests for the BITS DataScience Platform Backend.

## Overview

Integration tests verify that different components of the system work together correctly, including:
- Database operations and relationships
- API endpoints with real HTTP requests
- Authentication and authorization flows
- External service integrations

## Test Database Setup

### Prerequisites

1. **PostgreSQL** must be installed and running
2. **Node.js** and **npm** must be installed
3. **Database user** with sufficient privileges

### Database Configuration

The integration tests use a separate test database to avoid affecting development data.

#### Environment Variables

Set these environment variables in your `.env` file or test environment:

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME_TEST=bits_datascience_test
DB_USERNAME=postgres
DB_PASSWORD=your_password

# Test Environment
NODE_ENV=test
PORT=5002
JWT_SECRET=test-jwt-secret-key-for-testing-only
SESSION_SECRET=test-session-secret-key-for-testing-only
```

#### Database Setup Steps

1. **Create PostgreSQL User** (if not exists):
   ```sql
   CREATE USER postgres WITH PASSWORD 'your_password';
   ALTER USER postgres WITH SUPERUSER;
   ```

2. **Create Test Database**:
   ```sql
   CREATE DATABASE bits_datascience_test;
   GRANT ALL PRIVILEGES ON DATABASE bits_datascience_test TO postgres;
   ```

3. **Verify Connection**:
   ```bash
   psql -h localhost -U postgres -d bits_datascience_test
   ```

### Alternative: Docker Setup

If you prefer using Docker for the test database:

```bash
# Start PostgreSQL container
docker run --name bits-test-db \
  -e POSTGRES_DB=bits_datascience_test \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 \
  -d postgres:13

# Update environment variables
DB_HOST=localhost
DB_PORT=5432
DB_NAME_TEST=bits_datascience_test
DB_USERNAME=postgres
DB_PASSWORD=postgres
```

## Running Integration Tests

### Prerequisites Check

Before running integration tests, ensure:
1. PostgreSQL is running
2. Test database exists
3. Environment variables are set correctly

### Test Commands

```bash
# Run all integration tests
npm run test:integration

# Run specific integration test file
npm run test:integration -- --testPathPattern="database.integration.test.js"

# Run integration tests with verbose output
npm run test:integration -- --verbose

# Run integration tests in watch mode
npm run test:integration -- --watch
```

### Test Modes

The integration tests support two modes:

#### 1. Real Database Mode
- Connects to actual PostgreSQL database
- Performs real database operations
- Tests actual data persistence and relationships
- Requires proper database setup

#### 2. Mock Mode (Fallback)
- Uses Jest mocks when database is unavailable
- Tests the test structure and logic
- No database connection required
- Useful for CI/CD environments without database

### Test Structure

```
tests/integration/
├── setup.js                    # Global test setup/teardown
├── database.integration.test.js # Database operations tests
├── auth.integration.test.js    # Authentication API tests
└── api.integration.test.js     # General API tests
```

## Test Database Management

### Automatic Setup

The test framework automatically:
1. Creates test database if it doesn't exist
2. Syncs all Sequelize models
3. Creates test data (users, roles, permissions, etc.)
4. Cleans up data between tests
5. Closes connections after tests

### Manual Database Reset

If you need to reset the test database:

```bash
# Drop and recreate test database
psql -h localhost -U postgres -c "DROP DATABASE IF EXISTS bits_datascience_test;"
psql -h localhost -U postgres -c "CREATE DATABASE bits_datascience_test;"

# Or use the npm script (if available)
npm run db:reset:test
```

## Test Data

### Default Test Data

The integration tests create the following test data:

#### Users
- `<EMAIL>` - Administrator user
- `<EMAIL>` - Instructor user  
- `<EMAIL>` - Student user

#### Roles
- `admin` - Administrator role
- `instructor` - Instructor role
- `student` - Student role

#### Permissions
- User management permissions
- Course management permissions
- Project management permissions

#### Sample Data
- Test course with instructor
- Test project associated with course

### Custom Test Data

To add custom test data, modify `tests/config/test-database.js`:

```javascript
export const createTestData = async (models) => {
  // Add your custom test data here
  const customUser = await models.User.create({
    email: '<EMAIL>',
    name: 'Custom User',
    // ... other fields
  });
  
  return {
    // ... existing data
    customUser
  };
};
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Errors

**Error**: `role "postgres" does not exist`

**Solution**:
```bash
# Create postgres user
sudo -u postgres createuser --superuser postgres
sudo -u postgres psql -c "ALTER USER postgres WITH PASSWORD 'your_password';"
```

#### 2. Permission Denied

**Error**: `permission denied for database`

**Solution**:
```sql
GRANT ALL PRIVILEGES ON DATABASE bits_datascience_test TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
```

#### 3. Port Already in Use

**Error**: `EADDRINUSE: address already in use :::5002`

**Solution**:
```bash
# Change test port in environment
export PORT=5003

# Or kill existing process
lsof -ti:5002 | xargs kill -9
```

#### 4. Test Timeout

**Error**: `Timeout - Async callback was not invoked`

**Solution**:
- Increase test timeout in `jest.integration.config.js`
- Check database connection
- Verify all async operations complete

### Debug Mode

Enable debug logging for integration tests:

```bash
# Set debug environment variable
export DEBUG=integration:*

# Run tests with debug output
npm run test:integration -- --verbose
```

## CI/CD Integration

### GitHub Actions Example

```yaml
name: Integration Tests
on: [push, pull_request]

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_DB: bits_datascience_test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      
      - run: npm ci
      - run: npm run test:integration
```

### Docker Compose for Testing

```yaml
# docker-compose.test.yml
version: '3.8'
services:
  test-db:
    image: postgres:13
    environment:
      POSTGRES_DB: bits_datascience_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  test-runner:
    build: .
    environment:
      DB_HOST: test-db
      DB_PORT: 5432
      DB_NAME_TEST: bits_datascience_test
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      NODE_ENV: test
    depends_on:
      test-db:
        condition: service_healthy
    command: npm run test:integration
```

## Best Practices

1. **Isolation**: Each test should be independent and not rely on other tests
2. **Cleanup**: Always clean up test data after each test
3. **Realistic Data**: Use realistic test data that matches production scenarios
4. **Error Handling**: Test both success and failure scenarios
5. **Performance**: Keep tests fast by using efficient database operations
6. **Documentation**: Document complex test scenarios and data requirements

## Monitoring and Maintenance

### Test Coverage

Monitor integration test coverage:

```bash
npm run test:integration -- --coverage
```

### Performance Monitoring

Track test execution time:

```bash
npm run test:integration -- --verbose --detectOpenHandles
```

### Regular Maintenance

1. Update test data to match schema changes
2. Review and update test scenarios
3. Monitor test execution time
4. Update documentation as needed
