# LTI 1.3 and AWS S3 Integration Guide

## Overview

This document provides comprehensive technical details for integrating the BITS-DataScience Projects Platform with D2L-Brightspace LMS using LTI 1.3 standards and AWS S3 for file storage and management. This integration has been fully implemented and is production-ready.

---

## LTI 1.3 Integration

### Architecture Overview

The platform implements LTI 1.3 (Learning Tools Interoperability) to enable seamless integration with D2L-Brightspace. This allows:

- **Single Sign-On (SSO)** through the LMS
- **Deep Linking** for content selection
- **Grade Passback** using Assignment and Grade Services (AGS)
- **Context Synchronization** between LMS courses and platform projects

### LTI 1.3 Flow Diagram

```
LMS (D2L Brightspace) → Platform Flow:

1. OIDC Login Initiation
   LMS → GET/POST /api/lti/oidc/init
   
2. OIDC Authentication
   Platform → Redirect to LMS Auth (Brightspace D2L)
   
3. OIDC Callback
   LMS → POST /api/lti/oidc/callback (with auth code)
   
4. Token Exchange & Launch
   Platform → Exchange code for ID token and process launch
   
5. User/Course Sync
   Platform → Sync user and course data with role mapping
   
6. Project Access
   Platform → Redirect to appropriate dashboard based on role
```

### Database Schema

#### LTI Platform Registration
```sql
CREATE TABLE lti_platforms (
    id UUID PRIMARY KEY,
    platform_id VARCHAR NOT NULL UNIQUE,  -- LMS issuer URL
    platform_name VARCHAR NOT NULL,
    client_id VARCHAR NOT NULL,
    auth_login_url TEXT NOT NULL,
    auth_token_url TEXT NOT NULL,
    key_set_url TEXT NOT NULL,
    private_key TEXT,  -- Tool's private key
    public_key TEXT,   -- Tool's public key
    key_id VARCHAR,    -- Key identifier
    is_active BOOLEAN DEFAULT TRUE,
    settings JSONB DEFAULT '{}'
);
```

#### LTI Deployments
```sql
CREATE TABLE lti_deployments (
    id UUID PRIMARY KEY,
    platform_id UUID REFERENCES lti_platforms(id),
    deployment_id VARCHAR NOT NULL,
    deployment_name VARCHAR,
    is_active BOOLEAN DEFAULT TRUE,
    settings JSONB DEFAULT '{}'
);
```

#### LTI Contexts (Course Mapping)
```sql
CREATE TABLE lti_contexts (
    id UUID PRIMARY KEY,
    platform_id UUID REFERENCES lti_platforms(id),
    deployment_id UUID REFERENCES lti_deployments(id),
    course_id UUID REFERENCES courses(id),
    context_id VARCHAR NOT NULL,  -- LMS course ID
    context_title VARCHAR,
    context_label VARCHAR,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### LTI Resource Links (Project Mapping)
```sql
CREATE TABLE lti_resource_links (
    id UUID PRIMARY KEY,
    platform_id UUID REFERENCES lti_platforms(id),
    context_id UUID REFERENCES lti_contexts(id),
    project_id UUID REFERENCES projects(id),
    resource_link_id VARCHAR NOT NULL,
    resource_link_title VARCHAR,
    custom_parameters JSONB DEFAULT '{}'
);
```

### LTI Service Implementation

#### Key Management

```javascript
class LTIKeyManager {
  generateKeyPair() {
    const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: { type: 'spki', format: 'pem' },
      privateKeyEncoding: { type: 'pkcs8', format: 'pem' }
    });
    return { privateKey, publicKey };
  }

  getJWKS() {
    return {
      keys: [{
        kty: 'RSA',
        use: 'sig',
        kid: this.keyId,
        alg: 'RS256',
        n: this.extractModulus(),
        e: this.extractExponent()
      }]
    };
  }
}
```

#### JWT Verification

```javascript
class LTITokenVerifier {
  async verifyIDToken(token, platform) {
    const platformKeys = await this.getPlatformKeys(platform.keySetUrl);
    
    const decoded = jwt.verify(token, platformKeys, {
      algorithms: ['RS256'],
      issuer: platform.platformId,
      audience: platform.clientId
    });

    return this.validateClaims(decoded);
  }

  validateClaims(payload) {
    const requiredClaims = [
      'iss', 'aud', 'exp', 'iat', 'nonce',
      'https://purl.imsglobal.org/spec/lti/claim/message_type',
      'https://purl.imsglobal.org/spec/lti/claim/version'
    ];

    for (const claim of requiredClaims) {
      if (!payload[claim]) {
        throw new Error(`Missing required claim: ${claim}`);
      }
    }

    return payload;
  }
}
```

### LTI Launch Handling

#### Authentication Flow

1. **Login Initiation**
   ```http
   POST /api/lti/login
   Content-Type: application/x-www-form-urlencoded
   
   iss=https://brightspace.bits.edu
   &login_hint=user123
   &target_link_uri=https://platform.bits.edu/lti/launch
   &client_id=bits-datascience-platform
   ```

2. **Authentication Request**
   ```javascript
   const authParams = new URLSearchParams({
     response_type: 'id_token',
     response_mode: 'form_post',
     scope: 'openid',
     client_id: platform.clientId,
     redirect_uri: `${toolUrl}/lti/launch`,
     login_hint: loginHint,
     state: generateState(),
     nonce: generateNonce()
   });
   ```

3. **Launch Processing**
   ```javascript
   async processLaunch(idToken, state) {
     const session = await this.validateSession(state);
     const launchData = await this.verifyJWT(idToken, session.platform);
     
     const user = await this.syncUser(launchData);
     const context = await this.syncContext(launchData);
     const resourceLink = await this.syncResourceLink(launchData);
     
     return { user, context, resourceLink };
   }
   ```

### Deep Linking Implementation

#### Content Selection

```javascript
async handleDeepLinking(idToken, state) {
  const launchResult = await this.processLaunch(idToken, state);
  const deepLinkingClaim = launchResult.launchData[
    'https://purl.imsglobal.org/spec/lti-dl/claim/deep_linking_settings'
  ];

  const projects = await this.getAvailableProjects(launchResult.context);
  const contentItems = projects.map(project => ({
    type: 'ltiResourceLink',
    title: project.title,
    text: project.description,
    url: `${this.toolUrl}/lti/launch?project=${project.id}`,
    custom: {
      project_id: project.id,
      difficulty: project.difficultyLevel
    }
  }));

  return this.createDeepLinkingResponse(contentItems, deepLinkingClaim);
}
```

#### Deep Linking Response

```javascript
createDeepLinkingResponse(contentItems, deepLinkingClaim) {
  const payload = {
    iss: this.clientId,
    aud: this.platformId,
    exp: Math.floor(Date.now() / 1000) + 600,
    iat: Math.floor(Date.now() / 1000),
    nonce: this.generateNonce(),
    'https://purl.imsglobal.org/spec/lti-dl/claim/content_items': contentItems,
    'https://purl.imsglobal.org/spec/lti-dl/claim/data': deepLinkingClaim.data
  };

  return jwt.sign(payload, this.privateKey, {
    algorithm: 'RS256',
    keyid: this.keyId
  });
}
```

### Assignment and Grade Services (AGS)

#### Grade Passback Implementation

```javascript
class AGSService {
  async sendGrade(userId, lineItem, score, platform) {
    const accessToken = await this.getAccessToken(platform);
    
    const scoreData = {
      userId: userId,
      scoreGiven: score,
      scoreMaximum: lineItem.scoreMaximum,
      activityProgress: 'Completed',
      gradingProgress: 'FullyGraded',
      timestamp: new Date().toISOString()
    };

    await axios.post(lineItem.scoresUrl, scoreData, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/vnd.ims.lis.v1.score+json'
      }
    });
  }

  async getAccessToken(platform) {
    const clientAssertion = this.createClientAssertion(platform);
    
    const response = await axios.post(platform.authTokenUrl, {
      grant_type: 'client_credentials',
      client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
      client_assertion: clientAssertion,
      scope: 'https://purl.imsglobal.org/spec/lti-ags/scope/score'
    });

    return response.data.access_token;
  }
}
```

### LTI Configuration

#### Tool Registration JSON

```json
{
  "title": "BITS-DataScience Projects Platform",
  "description": "Interactive data science projects and assignments platform",
  "target_link_uri": "https://platform.bits.edu/lti/launch",
  "oidc_initiation_url": "https://platform.bits.edu/api/lti/login",
  "public_jwk_url": "https://platform.bits.edu/api/lti/jwks",
  "scopes": [
    "openid",
    "https://purl.imsglobal.org/spec/lti-ags/scope/lineitem",
    "https://purl.imsglobal.org/spec/lti-ags/scope/score",
    "https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly"
  ],
  "extensions": [{
    "domain": "platform.bits.edu",
    "tool_id": "bits-datascience-platform",
    "platform": "bits.edu",
    "settings": {
      "text": "BITS DataScience Platform",
      "privacy_level": "public"
    }
  }]
}
```

---

## AWS S3 Integration

### Architecture Overview

AWS S3 provides secure, scalable file storage for:
- **Student Submissions** (Jupyter notebooks, datasets, code files)
- **Project Resources** (starter notebooks, datasets, documentation)
- **User Profiles** (avatars, preferences)
- **System Backups** (database backups, logs)

### S3 Bucket Structure

```
bits-datascience-platform/
├── submissions/
│   ├── {userId}/
│   │   ├── {projectId}/
│   │   │   ├── {submissionId}/
│   │   │   │   ├── notebooks/
│   │   │   │   ├── datasets/
│   │   │   │   └── outputs/
├── projects/
│   ├── {projectId}/
│   │   ├── resources/
│   │   ├── templates/
│   │   └── rubrics/
├── users/
│   ├── {userId}/
│   │   ├── profile/
│   │   └── preferences/
└── system/
    ├── backups/
    └── logs/
```

### S3 Service Implementation

#### Core S3 Service

```javascript
import AWS from 'aws-sdk';

class S3Service {
  constructor() {
    this.s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1'
    });
    this.bucketName = process.env.S3_BUCKET_NAME;
  }

  generateKey(purpose, userId, projectId, submissionId, filename) {
    const timestamp = Date.now();
    
    switch (purpose) {
      case 'submission':
        return `submissions/${userId}/${projectId}/${submissionId}/${filename}`;
      case 'project':
        return `projects/${projectId}/resources/${filename}`;
      case 'profile':
        return `users/${userId}/profile/${filename}`;
      default:
        return `misc/${timestamp}-${filename}`;
    }
  }

  async getPresignedUploadUrl(key, contentType, expiresIn = 3600) {
    const params = {
      Bucket: this.bucketName,
      Key: key,
      ContentType: contentType,
      Expires: expiresIn,
      ServerSideEncryption: 'AES256'
    };

    return this.s3.getSignedUrlPromise('putObject', params);
  }

  async getPresignedDownloadUrl(key, expiresIn = 3600) {
    const params = {
      Bucket: this.bucketName,
      Key: key,
      Expires: expiresIn
    };

    return this.s3.getSignedUrlPromise('getObject', params);
  }
}
```

#### File Upload Handler

```javascript
class FileUploadService {
  async initiateUpload(userId, purpose, metadata) {
    const { fileName, fileType, projectId, submissionId } = metadata;
    
    // Generate unique S3 key
    const s3Key = this.s3Service.generateKey(
      purpose, userId, projectId, submissionId, fileName
    );

    // Get presigned upload URL
    const uploadUrl = await this.s3Service.getPresignedUploadUrl(
      s3Key, fileType
    );

    // Store file metadata
    await this.storeFileMetadata({
      s3Key,
      fileName,
      fileType,
      purpose,
      userId,
      projectId,
      submissionId,
      status: 'uploading'
    });

    return {
      uploadUrl,
      s3Key,
      downloadUrl: await this.s3Service.getPresignedDownloadUrl(s3Key)
    };
  }

  async confirmUpload(s3Key, userId) {
    // Verify file exists in S3
    const exists = await this.s3Service.objectExists(s3Key);
    if (!exists) {
      throw new Error('File upload failed');
    }

    // Update file status
    await this.updateFileStatus(s3Key, 'uploaded');

    // Process file if needed (virus scan, validation, etc.)
    await this.processUploadedFile(s3Key);

    return { success: true };
  }
}
```

### Submission File Management

#### Notebook Storage

```javascript
class NotebookStorageService {
  async saveNotebook(userId, projectId, submissionId, notebook) {
    const s3Key = `submissions/${userId}/${projectId}/${submissionId}/notebooks/${notebook.name}`;
    
    // Convert notebook to JSON
    const notebookData = {
      cells: notebook.cells,
      metadata: notebook.metadata,
      nbformat: 4,
      nbformat_minor: 4
    };

    // Upload to S3
    await this.s3Service.uploadObject(s3Key, JSON.stringify(notebookData, null, 2), {
      ContentType: 'application/json',
      Metadata: {
        userId,
        projectId,
        submissionId,
        notebookName: notebook.name
      }
    });

    return s3Key;
  }

  async loadNotebook(s3Key) {
    const object = await this.s3Service.getObject(s3Key);
    return JSON.parse(object.Body.toString());
  }

  async autoSaveNotebook(userId, projectId, notebook) {
    const timestamp = new Date().toISOString();
    const s3Key = `submissions/${userId}/${projectId}/autosave/${timestamp}-${notebook.name}`;
    
    await this.saveNotebook(userId, projectId, 'autosave', notebook);
    
    // Clean up old autosaves (keep last 10)
    await this.cleanupAutosaves(userId, projectId);
  }
}
```

#### Dataset Management

```javascript
class DatasetStorageService {
  async uploadDataset(userId, projectId, file) {
    const s3Key = `submissions/${userId}/${projectId}/datasets/${file.name}`;
    
    // Validate file type and size
    this.validateDatasetFile(file);
    
    // Upload with metadata
    await this.s3Service.uploadObject(s3Key, file.buffer, {
      ContentType: file.mimetype,
      Metadata: {
        originalName: file.originalname,
        size: file.size.toString(),
        uploadedBy: userId
      }
    });

    return {
      s3Key,
      downloadUrl: await this.s3Service.getPresignedDownloadUrl(s3Key)
    };
  }

  validateDatasetFile(file) {
    const allowedTypes = [
      'text/csv',
      'application/json',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    if (!allowedTypes.includes(file.mimetype)) {
      throw new Error('Unsupported file type');
    }

    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      throw new Error('File too large');
    }
  }
}
```

### S3 Security Configuration

#### Bucket Policy

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowApplicationAccess",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::ACCOUNT:user/bits-platform-service"
      },
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::bits-datascience-platform",
        "arn:aws:s3:::bits-datascience-platform/*"
      ]
    },
    {
      "Sid": "DenyDirectPublicAccess",
      "Effect": "Deny",
      "Principal": "*",
      "Action": "s3:*",
      "Resource": [
        "arn:aws:s3:::bits-datascience-platform",
        "arn:aws:s3:::bits-datascience-platform/*"
      ],
      "Condition": {
        "StringNotEquals": {
          "aws:SourceIp": ["IP_RANGE_OF_PLATFORM"]
        }
      }
    }
  ]
}
```

#### CORS Configuration

```json
{
  "CORSRules": [
    {
      "AllowedOrigins": [
        "https://platform.bits.edu",
        "https://dev.platform.bits.edu"
      ],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
      "AllowedHeaders": ["*"],
      "MaxAgeSeconds": 3000
    }
  ]
}
```

### Performance Optimization

#### CloudFront Integration

```javascript
class CDNService {
  constructor() {
    this.cloudfront = new AWS.CloudFront({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: 'us-east-1'
    });
  }

  async getCachedUrl(s3Key) {
    const distributionDomain = process.env.CLOUDFRONT_DOMAIN;
    return `https://${distributionDomain}/${s3Key}`;
  }

  async invalidateCache(s3Keys) {
    const params = {
      DistributionId: process.env.CLOUDFRONT_DISTRIBUTION_ID,
      InvalidationBatch: {
        Paths: {
          Quantity: s3Keys.length,
          Items: s3Keys.map(key => `/${key}`)
        },
        CallerReference: Date.now().toString()
      }
    };

    return this.cloudfront.createInvalidation(params).promise();
  }
}
```

#### S3 Transfer Acceleration

```javascript
const s3Accelerated = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
  useAccelerateEndpoint: true
});
```

### Backup and Disaster Recovery

#### Automated Backups

```javascript
class BackupService {
  async createSubmissionBackup(submissionId) {
    const submission = await Submission.findByPk(submissionId, {
      include: ['notebooks', 'files']
    });

    const backupData = {
      submission: submission.toJSON(),
      timestamp: new Date().toISOString(),
      version: '1.0'
    };

    const backupKey = `backups/submissions/${submissionId}/${Date.now()}.json`;
    
    await this.s3Service.uploadObject(
      backupKey, 
      JSON.stringify(backupData, null, 2),
      { StorageClass: 'GLACIER' }
    );

    return backupKey;
  }

  async scheduleRegularBackups() {
    // Backup critical data daily
    const cron = require('node-cron');
    
    cron.schedule('0 2 * * *', async () => {
      await this.backupDatabase();
      await this.backupUserSubmissions();
      await this.cleanupOldBackups();
    });
  }
}
```

### Monitoring and Logging

#### S3 Access Logging

```javascript
class S3MonitoringService {
  async logFileAccess(s3Key, userId, action) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      s3Key,
      userId,
      action,
      ip: this.getClientIP(),
      userAgent: this.getUserAgent()
    };

    // Store in CloudWatch
    await this.cloudWatch.putLogEvents({
      logGroupName: 'bits-platform-s3-access',
      logStreamName: new Date().toISOString().split('T')[0],
      logEvents: [{
        timestamp: Date.now(),
        message: JSON.stringify(logEntry)
      }]
    }).promise();
  }

  async generateUsageReport() {
    const bucketSize = await this.s3Service.getBucketSize();
    const objectCount = await this.s3Service.getObjectCount();
    const monthlyCost = this.calculateMonthlyCost(bucketSize);

    return {
      bucketSize,
      objectCount,
      monthlyCost,
      topUsers: await this.getTopStorageUsers(),
      fileTypeDistribution: await this.getFileTypeDistribution()
    };
  }
}
```

---

## Integration Deployment

### Environment Configuration

```bash
# LTI Configuration
LTI_TOOL_URL=https://platform.bits.edu
LTI_KEY_ID=bits-lti-key-1
LTI_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----...
LTI_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----...

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=bits-datascience-platform
CLOUDFRONT_DOMAIN=cdn.bits-datascience.edu
CLOUDFRONT_DISTRIBUTION_ID=E1234567890123

# Database Configuration
DATABASE_URL=************************************/bits_platform
REDIS_URL=redis://localhost:6379

# Session Configuration
SESSION_SECRET=your-session-secret
```

### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/

# Set environment
ENV NODE_ENV=production

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

EXPOSE 5000

CMD ["npm", "start"]
```

### Production Checklist

- [ ] LTI Platform registered in D2L-Brightspace
- [ ] S3 bucket created with proper policies
- [ ] CloudFront distribution configured
- [ ] SSL certificates installed
- [ ] Environment variables set
- [ ] Database migrations run
- [ ] Health checks configured
- [ ] Monitoring and logging set up
- [ ] Backup procedures tested

---

## Troubleshooting

### Common LTI Issues

1. **JWT Verification Failures**
   - Check platform key URLs
   - Verify nonce and state parameters
   - Ensure clock synchronization

2. **Deep Linking Errors**
   - Validate content item format
   - Check return URL configuration
   - Verify deep linking claims

3. **Grade Passback Failures**
   - Confirm AGS scope permissions
   - Check line item configuration
   - Verify access token validity

### Common S3 Issues

1. **Upload Failures**
   - Check presigned URL expiration
   - Verify bucket permissions
   - Monitor CORS configuration

2. **Access Denied Errors**
   - Review IAM policies
   - Check bucket policies
   - Verify SSL requirements

3. **Performance Issues**
   - Enable Transfer Acceleration
   - Implement CloudFront caching
   - Optimize file sizes

---

## Support and Maintenance

### Monitoring

- **LTI Launch Success Rate**: Monitor successful vs failed launches
- **S3 Upload Success Rate**: Track file upload completion
- **Grade Passback Success**: Monitor AGS grade submissions
- **Storage Usage**: Track S3 storage costs and usage patterns

### Alerts

```javascript
// CloudWatch Alarms
const alarms = [
  {
    name: 'LTI-Launch-Failures',
    metric: 'failed_lti_launches',
    threshold: 5,
    period: 300
  },
  {
    name: 'S3-Upload-Failures',
    metric: 'failed_s3_uploads',
    threshold: 10,
    period: 300
  },
  {
    name: 'High-Storage-Costs',
    metric: 's3_monthly_cost',
    threshold: 1000,
    period: 86400
  }
];
```

### Maintenance Tasks

```javascript
// Daily maintenance
cron.schedule('0 1 * * *', async () => {
  await cleanupExpiredSessions();
  await archiveOldSubmissions();
  await generateUsageReports();
});

// Weekly maintenance
cron.schedule('0 2 * * 0', async () => {
  await optimizeS3Storage();
  await rotateLTIKeys();
  await validateLTIPlatforms();
});
```

For additional support, contact: <EMAIL>