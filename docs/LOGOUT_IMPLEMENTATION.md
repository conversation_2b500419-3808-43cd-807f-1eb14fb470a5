# Logout Implementation Guide

## Overview
This document explains how the logout functionality is implemented in the BITS DataScience Platform, including the enhanced token blacklisting system for proper JWT invalidation and multi-authentication method support.

---

## 🔐 **Authentication Architecture**

### **Multi-Method Authentication**
The platform supports multiple authentication methods:

#### **JWT-Based Authentication**
The platform uses **JSON Web Tokens (JWT)** for stateless authentication:

```javascript
// Token Structure
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "userId": "uuid",
    "email": "<EMAIL>",
    "iat": 1640995200,
    "exp": 1640998800
  },
  "signature": "HMACSHA256(...)"
}
```

#### **Google OAuth Authentication**
- **OAuth 2.0 Flow**: Secure social login integration
- **JWT Token Generation**: Generates JWT token after successful OAuth callback
- **Session Management**: Uses JWT tokens for subsequent API access

#### **LTI 1.3 OIDC Authentication**
- **Learning Management System Integration**: Brightspace D2L integration
- **Session-Based**: Uses server-side sessions for LTI launches
- **Automatic Cleanup**: Expired LTI sessions are automatically cleaned up

### **The JWT Invalidation Challenge**
JWT tokens are **stateless** by design, which means:
- ✅ **Fast**: No database lookup required for validation
- ✅ **Scalable**: Works across multiple server instances
- ❌ **Cannot be revoked**: Once issued, tokens remain valid until expiration

---

## 🚀 **Enhanced Logout Implementation**

### **1. Token Blacklisting Service**

We've implemented a **Redis-based token blacklist** to solve the JWT invalidation problem:

```javascript
// src/services/tokenBlacklistService.js
class TokenBlacklistService {
  constructor() {
    this.redis = new redis(process.env.REDIS_URL);
    this.blacklistPrefix = 'blacklisted_token:';
    this.blacklistExpiry = 24 * 60 * 60; // 24 hours
  }

  async blacklistToken(token, expiresIn = this.blacklistExpiry) {
    const key = `${this.blacklistPrefix}${token}`;
    await this.redis.setex(key, expiresIn, '1');
  }

  async isTokenBlacklisted(token) {
    const key = `${this.blacklistPrefix}${token}`;
    const result = await this.redis.get(key);
    return result !== null;
  }
}
```

### **2. Enhanced JWT Middleware**

The JWT middleware now checks for blacklisted tokens:

```javascript
// src/middlewares/auth.js
export const jwtMiddleware = async (req, res, next) => {
  const token = req.headers.authorization?.substring(7);
  
  // Check if token is blacklisted
  const isBlacklisted = await tokenBlacklistService.isTokenBlacklisted(token);
  if (isBlacklisted) {
    return res.status(401).json({
      error: 'Token invalidated',
      message: 'This token has been revoked. Please login again.'
    });
  }

  // Continue with normal JWT verification...
};
```

### **3. Enhanced Logout Controller**

```javascript
// src/controllers/authController.js
export const logout = asyncHandler(async (req, res) => {
  try {
    const token = req.headers.authorization?.substring(7);
    
    if (token) {
      // Add token to blacklist
      await tokenBlacklistService.blacklistToken(token);
      logger.info(`Token blacklisted for user: ${req.user.email}`);
    }
    
    logger.info(`User logged out: ${req.user.email}`);
    
    res.json({
      success: true,
      message: 'Logged out successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Logout error:', error);
    // Graceful degradation - still logout even if blacklisting fails
    res.json({
      success: true,
      message: 'Logged out successfully',
      timestamp: new Date().toISOString()
    });
  }
});

// LTI Session Cleanup
export const cleanupLTISessions = asyncHandler(async (req, res) => {
  try {
    const deletedCount = await ltiService.cleanupExpiredSessions();
    
    res.json({
      success: true,
      message: `Cleaned up ${deletedCount} expired sessions`,
      deletedCount
    });
  } catch (error) {
    logger.error('LTI session cleanup failed:', error);
    res.status(500).json({
      error: 'Cleanup Failed',
      message: 'Could not clean up expired sessions'
    });
  }
});
```

---

## 📋 **API Endpoints**

### **1. Standard Logout**
```http
POST /api/auth/logout
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully",
  "timestamp": "2025-08-17T18:00:00.000Z"
}
```

### **2. Logout from All Devices**
```http
POST /api/auth/logout-all
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Response:**
```json
{
  "success": true,
  "message": "Logged out from all devices successfully",
  "timestamp": "2025-08-17T18:00:00.000Z"
}
```

### **3. Logout Statistics (Admin Only)**
```http
GET /api/auth/logout/stats
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "stats": {
    "totalBlacklistedTokens": 150,
    "memoryUsage": 1024000
  },
  "timestamp": "2025-08-17T18:00:00.000Z"
}
```

### **4. LTI Session Cleanup (Admin Only)**
```http
POST /api/lti/cleanup
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Cleaned up 5 expired sessions",
  "deletedCount": 5
}
```

---

## 🔄 **Logout Flow**

### **Step-by-Step Process:**

1. **Client Request**
   ```javascript
   const logout = async () => {
     const token = localStorage.getItem('accessToken');
     
     await fetch('/api/auth/logout', {
       method: 'POST',
       headers: {
         'Authorization': `Bearer ${token}`,
         'Content-Type': 'application/json'
       }
     });
   };
   ```

2. **Server Processing**
   - Extract JWT token from Authorization header
   - Add token to Redis blacklist with TTL
   - Log the logout action
   - Return success response

3. **Client Cleanup**
   ```javascript
   // Clear local storage
   localStorage.clear();
   sessionStorage.clear();
   
   // Redirect to login
   window.location.href = '/login';
   ```

4. **Token Invalidation**
   - Subsequent requests with the same token will be rejected
   - Middleware checks blacklist before processing requests

---

## 🛡️ **Security Features**

### **1. Token Blacklisting**
- **Redis Storage**: Fast, in-memory storage for blacklisted tokens
- **TTL Management**: Automatic cleanup of expired blacklist entries
- **Fail-Open Design**: If Redis is down, tokens are still allowed (graceful degradation)

### **2. Graceful Degradation**
```javascript
// If blacklisting fails, user can still logout
try {
  await tokenBlacklistService.blacklistToken(token);
} catch (error) {
  logger.error('Blacklisting failed:', error);
  // Continue with logout anyway
}
```

### **3. Admin Monitoring**
- **Statistics Endpoint**: Monitor blacklist size and memory usage
- **Cleanup Jobs**: Automatic removal of expired entries
- **Audit Logging**: Track all logout activities

---

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=1h

# Blacklist Configuration
BLACKLIST_EXPIRY=86400  # 24 hours in seconds
```

### **Redis Setup**
```bash
# Start Redis server
docker run -d --name redis -p 6379:6379 redis:7-alpine

# Or using docker-compose
docker-compose up redis
```

---

## 🧪 **Testing**

### **1. Test Logout Flow**
```bash
# 1. Login to get a token
curl -X POST http://localhost:5001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 2. Use the token to access protected endpoint
curl -X GET http://localhost:5001/api/auth/me \
  -H "Authorization: Bearer <token>"

# 3. Logout
curl -X POST http://localhost:5001/api/auth/logout \
  -H "Authorization: Bearer <token>"

# 4. Try to use the same token (should fail)
curl -X GET http://localhost:5001/api/auth/me \
  -H "Authorization: Bearer <token>"
```

### **2. Test Blacklist Statistics**
```bash
# Get blacklist stats (requires admin role)
curl -X GET http://localhost:5001/api/auth/logout/stats \
  -H "Authorization: Bearer <admin_token>"
```

---

## 📊 **Performance Considerations**

### **1. Redis Performance**
- **Memory Usage**: Each blacklisted token uses ~100 bytes
- **Lookup Time**: O(1) constant time for token checks
- **Storage**: Tokens automatically expire after 24 hours

### **2. Scalability**
- **Horizontal Scaling**: Multiple server instances can share the same Redis
- **Load Balancing**: Stateless design works with any load balancer
- **Caching**: Redis provides fast in-memory access

### **3. Monitoring**
```javascript
// Monitor blacklist size
const stats = await tokenBlacklistService.getBlacklistStats();
console.log(`Blacklisted tokens: ${stats.totalBlacklistedTokens}`);

// Cleanup expired entries
await tokenBlacklistService.cleanupExpiredEntries();
```

---

## 🔄 **Alternative Approaches**

### **1. Token Versioning**
```javascript
// Store token version in user record
await user.update({ tokenVersion: user.tokenVersion + 1 });

// Include version in JWT payload
const token = jwt.sign({
  userId: user.id,
  version: user.tokenVersion
}, JWT_SECRET);

// Check version in middleware
if (decoded.version < user.tokenVersion) {
  return res.status(401).json({ error: 'Token invalidated' });
}
```

### **2. Short-Lived Tokens**
```javascript
// Use very short token expiration (5-15 minutes)
JWT_EXPIRES_IN=900  // 15 minutes

// Implement automatic refresh
const refreshToken = async () => {
  const response = await fetch('/api/auth/refresh', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};
```

### **3. Session-Based Authentication**
```javascript
// Store sessions in Redis
const session = await redis.get(`session:${sessionId}`);
if (!session) {
  return res.status(401).json({ error: 'Session expired' });
}
```

---

## 🚨 **Security Best Practices**

### **1. Token Storage**
- **Frontend**: Store in `localStorage` or `sessionStorage`
- **Mobile**: Use secure storage (Keychain, Keystore)
- **Never**: Store in cookies without proper flags

### **2. Token Transmission**
- **HTTPS Only**: Always use HTTPS in production
- **Authorization Header**: Use Bearer token format
- **CORS**: Configure proper CORS policies

### **3. Token Management**
- **Short Expiration**: Use short-lived access tokens
- **Refresh Tokens**: Implement secure refresh mechanism
- **Blacklisting**: Invalidate tokens on logout
- **Monitoring**: Track suspicious activity

---

## 🔍 **Troubleshooting**

### **Common Issues**

#### **1. Redis Connection Errors**
```javascript
// Check Redis connection
const redis = new Redis(process.env.REDIS_URL);
redis.on('error', (err) => {
  logger.error('Redis connection error:', err);
});
```

#### **2. Token Not Blacklisted**
```javascript
// Verify blacklist entry
const isBlacklisted = await tokenBlacklistService.isTokenBlacklisted(token);
console.log('Token blacklisted:', isBlacklisted);
```

#### **3. Memory Usage High**
```javascript
// Monitor memory usage
const stats = await tokenBlacklistService.getBlacklistStats();
if (stats.memoryUsage > *********) { // 100MB
  await tokenBlacklistService.cleanupExpiredEntries();
}
```

---

## 📚 **Additional Resources**

- [JWT.io](https://jwt.io/) - JWT Debugger and Documentation
- [Redis Documentation](https://redis.io/documentation) - Redis Commands and Best Practices
- [Passport.js](http://www.passportjs.org/) - Authentication Middleware
- [Express.js Security](https://expressjs.com/en/advanced/best-practices-security.html) - Security Best Practices

---

**Last Updated:** August 20, 2025  
**Version:** 2.2.0  
**Maintainer:** BITS Pilani DataScience Team
