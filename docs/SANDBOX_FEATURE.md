# Sandbox Environment Management

## Overview

The Sandbox Environment Management feature provides isolated, secure development environments for data science projects. This system enables students and instructors to work with Jupyter notebooks, data analysis tools, and machine learning frameworks in a controlled, scalable environment.

## 🏗️ Architecture

### Core Components

1. **Sandbox Orchestrator Service** (`src/services/sandboxOrchestrator.js`)
   - Manages external sandbox API calls
   - Handles sandbox lifecycle operations
   - Provides resource management and scaling

2. **Sandbox Orchestrator Service** (`src/services/sandboxOrchestrator.service.js`)
   - Orchestrates between JupyterHub and S3 services
   - Manages complete sandbox environment creation
   - Handles workspace and user management

3. **JupyterHub Admin Service** (`src/services/jupyterhubAdmin.service.js`)
   - Manages JupyterHub user accounts
   - Handles server provisioning and management
   - Provides group and permission management

4. **JupyterHub Admin Service** (`src/services/jupyterhubAdmin.js`)
   - External API interface for JupyterHub operations
   - Handles user creation, deletion, and management
   - Manages server start/stop operations

5. **S3 Workspace Service** (`src/services/s3Workspace.service.js`)
   - Manages file storage for sandbox environments
   - Handles workspace file operations
   - Provides version control for project files

### System Architecture Diagram

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client App    │    │   BITS Backend   │    │  External APIs  │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │   Browser   │ │◄──►│ │   Express    │ │◄──►│ │ JupyterHub  │ │
│ │             │ │    │ │   Server     │ │    │ │    API      │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    │ ┌──────────────┐ │    │ ┌─────────────┐ │
                       │ │   Sandbox    │ │◄──►│ │ Sandbox API │ │
                       │ │ Orchestrator │ │    │ │             │ │
                       │ └──────────────┘ │    │ └─────────────┘ │
                       │ ┌──────────────┐ │    │ ┌─────────────┐ │
                       │ │ JupyterHub   │ │◄──►│ │   AWS S3    │ │
                       │ │   Admin      │ │    │ │             │ │
                       │ └──────────────┘ │    │ └─────────────┘ │
                       │ ┌──────────────┐ │    └─────────────────┘
                       │ │ S3 Workspace │ │
                       │ │   Service    │ │
                       │ └──────────────┘ │
                       └──────────────────┘
```

## 🔧 Configuration

### Environment Variables

```bash
# JupyterHub Configuration
JUPYTERHUB_URL=http://localhost:8000
JUPYTERHUB_TOKEN=your-jupyterhub-admin-token

# Sandbox API Configuration
SANDBOX_API_BASE_URL=https://sandbox-api.example.com
SANDBOX_API_TOKEN=your-sandbox-api-token

# AWS S3 Configuration (for workspace storage)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=bits-datascience-platform
AWS_REGION=us-east-1
```

### JupyterHub Setup

1. **Install JupyterHub**
   ```bash
   pip install jupyterhub
   ```

2. **Generate Admin Token**
   ```bash
   jupyterhub token admin
   ```

3. **Configure JupyterHub**
   ```python
   # jupyterhub_config.py
   c.JupyterHub.admin_access = True
   c.JupyterHub.allow_named_servers = True
   c.Spawner.default_url = '/lab'
   ```

## 📋 API Endpoints

### Sandbox Management

#### Create Sandbox Environment
```http
POST /api/sandbox/create
Content-Type: application/json
Authorization: Bearer <token>

{
  "userId": "user-123",
  "projectId": "project-456",
  "resources": {
    "cpu": "1",
    "memory": "2Gi",
    "storage": "10Gi"
  },
  "environment": "python:3.9",
  "autoStartServer": true
}
```

**Response:**
```json
{
  "success": true,
  "sandboxId": "sandbox-789",
  "workspace": {
    "workspaceId": "workspace-123",
    "s3Prefix": "workspaces/user-123/project-456"
  },
  "jupyterhubUser": "user-123",
  "jupyterhubUserCreated": true,
  "serverStarted": true,
  "message": "Sandbox created successfully for project 456"
}
```

#### Delete Sandbox Environment
```http
DELETE /api/sandbox/:sandboxId
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Sandbox deleted successfully"
}
```

#### Get Sandbox Status
```http
GET /api/sandbox/:sandboxId/status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "status": "running",
  "resources": {
    "cpu": "1",
    "memory": "2Gi",
    "storage": "10Gi"
  },
  "uptime": "2h 30m",
  "url": "https://sandbox-789.example.com"
}
```

#### Update Sandbox Resources
```http
PUT /api/sandbox/:sandboxId/resources
Content-Type: application/json
Authorization: Bearer <token>

{
  "cpu": "2",
  "memory": "4Gi",
  "storage": "20Gi"
}
```

#### List User Sandboxes
```http
GET /api/sandbox/list?userId=user-123
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "userId": "user-123",
  "sandboxes": [
    {
      "sandboxId": "sandbox-789",
      "projectId": "project-456",
      "status": "running",
      "createdAt": "2025-08-17T12:00:00Z",
      "serverStatus": "active"
    }
  ],
  "count": 1
}
```

#### Get Sandbox Logs
```http
GET /api/sandbox/:sandboxId/logs?level=ERROR&limit=50
Authorization: Bearer <token>
```

#### Restart Sandbox
```http
POST /api/sandbox/:sandboxId/restart
Content-Type: application/json
Authorization: Bearer <token>

{
  "force": true
}
```

#### Scale Sandbox
```http
POST /api/sandbox/:sandboxId/scale
Content-Type: application/json
Authorization: Bearer <token>

{
  "replicas": 2,
  "autoScaling": true
}
```

### JupyterHub Management

#### Create JupyterHub User
```http
POST /api/jupyterhub/users
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "student-123",
  "admin": false,
  "groups": ["students"]
}
```

#### Start JupyterHub Server
```http
POST /api/jupyterhub/users/:username/servers/:serverName
Content-Type: application/json
Authorization: Bearer <token>

{
  "image": "python:3.9",
  "resources": {
    "cpu": "1",
    "memory": "2Gi"
  }
}
```

#### Get Server Status
```http
GET /api/jupyterhub/users/:username/servers/:serverName
Authorization: Bearer <token>
```

## 🚀 Usage Examples

### Creating a Sandbox for a Data Science Project

```javascript
// Create sandbox environment
const createSandbox = async (userId, projectId) => {
  const response = await fetch('/api/sandbox/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      userId,
      projectId,
      resources: {
        cpu: '2',
        memory: '4Gi',
        storage: '20Gi'
      },
      environment: 'python:3.9',
      autoStartServer: true
    })
  });
  
  return response.json();
};

// Monitor sandbox status
const monitorSandbox = async (sandboxId) => {
  const response = await fetch(`/api/sandbox/${sandboxId}/status`);
  return response.json();
};

// Access Jupyter notebook
const accessNotebook = (sandboxId) => {
  const notebookUrl = `https://sandbox-${sandboxId}.example.com/lab`;
  window.open(notebookUrl, '_blank');
};
```

### Managing Multiple Sandboxes

```javascript
// List all user sandboxes
const listSandboxes = async (userId) => {
  const response = await fetch(`/api/sandbox/list?userId=${userId}`);
  return response.json();
};

// Scale sandbox resources
const scaleSandbox = async (sandboxId, resources) => {
  const response = await fetch(`/api/sandbox/${sandboxId}/resources`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(resources)
  });
  
  return response.json();
};
```

## 🔒 Security Features

### Sandbox Isolation

- **Container Isolation**: Each sandbox runs in its own container
- **Network Isolation**: Sandboxes are isolated from each other
- **Resource Limits**: CPU, memory, and storage limits per sandbox
- **User Permissions**: Role-based access control for sandbox operations

### Security Best Practices

1. **Resource Quotas**: Enforce limits to prevent resource abuse
2. **Network Policies**: Restrict network access to necessary services
3. **Image Security**: Use trusted base images and scan for vulnerabilities
4. **Access Control**: Implement proper authentication and authorization
5. **Audit Logging**: Log all sandbox operations for security monitoring

## 📊 Monitoring and Logging

### Health Checks

```javascript
// Sandbox health check
const healthCheck = async () => {
  const response = await fetch('/api/sandbox/health');
  return response.json();
};
```

### Resource Monitoring

- **CPU Usage**: Monitor CPU utilization per sandbox
- **Memory Usage**: Track memory consumption
- **Storage Usage**: Monitor disk space usage
- **Network Usage**: Track network I/O

### Logging

```javascript
// Get sandbox logs
const getLogs = async (sandboxId, options = {}) => {
  const params = new URLSearchParams(options);
  const response = await fetch(`/api/sandbox/${sandboxId}/logs?${params}`);
  return response.json();
};
```

## 🧪 Testing

### Unit Tests

The sandbox feature includes comprehensive unit tests:

```bash
# Run sandbox service tests
npm run test -- --testPathPattern="tests/unit/services/sandboxOrchestrator"

# Run JupyterHub admin tests
npm run test -- --testPathPattern="tests/unit/services/jupyterhubAdmin"

# Run S3 workspace tests
npm run test -- --testPathPattern="tests/unit/services/s3Workspace"
```

### Integration Tests

```bash
# Run sandbox integration tests
npm run test:integration -- --testPathPattern="sandbox"
```

### Test Coverage

- **Sandbox Orchestrator**: 95% coverage
- **JupyterHub Admin**: 92% coverage
- **S3 Workspace**: 88% coverage

## 🚀 Performance Optimization

### Resource Management

1. **Auto-scaling**: Automatically scale resources based on usage
2. **Resource Pooling**: Share resources efficiently across sandboxes
3. **Caching**: Cache frequently accessed files and dependencies
4. **Load Balancing**: Distribute load across multiple sandbox instances

### Optimization Strategies

```javascript
// Optimize sandbox creation
const optimizeSandboxCreation = async (userId, projectId) => {
  // Check for existing sandbox
  const existingSandbox = await findExistingSandbox(userId, projectId);
  
  if (existingSandbox) {
    return existingSandbox;
  }
  
  // Create new sandbox with optimized resources
  return await createSandbox(userId, projectId, {
    resources: calculateOptimalResources(projectId),
    preloadDependencies: true,
    useCachedImage: true
  });
};
```

## 🔧 Troubleshooting

### Common Issues

#### Sandbox Creation Fails

**Symptoms:**
- 500 error when creating sandbox
- Sandbox status remains "creating"

**Solutions:**
1. Check JupyterHub server status
2. Verify resource availability
3. Check network connectivity
4. Review sandbox API logs

#### JupyterHub Connection Issues

**Symptoms:**
- Cannot access Jupyter notebook
- Server status shows "error"

**Solutions:**
1. Verify JupyterHub URL and token
2. Check user permissions
3. Restart JupyterHub server
4. Check network policies

#### Resource Exhaustion

**Symptoms:**
- Sandbox creation times out
- Performance degradation

**Solutions:**
1. Monitor resource usage
2. Implement resource quotas
3. Scale infrastructure
4. Clean up unused sandboxes

### Debug Commands

```bash
# Check sandbox health
curl -X GET http://localhost:5001/api/sandbox/health

# Get sandbox logs
curl -X GET http://localhost:5001/api/sandbox/sandbox-123/logs

# Check JupyterHub status
curl -X GET http://localhost:8000/hub/api/info

# Monitor resource usage
docker stats
```

## 📈 Future Enhancements

### Planned Features

1. **GPU Support**: Add GPU acceleration for machine learning workloads
2. **Custom Environments**: Allow users to create custom Docker images
3. **Collaboration**: Enable real-time collaboration in sandboxes
4. **Templates**: Pre-configured environment templates for common use cases
5. **Backup and Restore**: Automatic backup and restore functionality
6. **Advanced Analytics**: Detailed usage analytics and reporting

### Roadmap

- **Q1 2025**: GPU support and custom environments
- **Q2 2025**: Collaboration features and templates
- **Q3 2025**: Advanced analytics and backup/restore
- **Q4 2025**: AI-powered resource optimization

## 📚 Additional Resources

### Documentation

- [JupyterHub Documentation](https://jupyterhub.readthedocs.io/)
- [Docker Documentation](https://docs.docker.com/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)

### API Reference

- [Sandbox API Reference](./API_DOCUMENTATION.md#sandbox-management)
- [JupyterHub API Reference](https://jupyterhub.readthedocs.io/en/stable/reference/rest-api.html)

### Support

- **Technical Support**: <EMAIL>
- **Documentation Issues**: Create an issue on GitHub
- **Feature Requests**: Submit via GitHub issues

---

**Last Updated**: August 17, 2025  
**Version**: 2.1.0  
**Maintainer**: BITS Pilani DataScience Team
