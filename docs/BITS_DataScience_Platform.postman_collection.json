{"info": {"name": "BITS-DataScience Projects Platform API", "description": "Complete API collection for the BITS-DataScience Projects Platform with LTI 1.3 and S3 integration", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "courseId", "value": "", "type": "string"}, {"key": "projectId", "value": "", "type": "string"}, {"key": "submissionId", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "item": [{"name": "Authentication", "item": [{"name": "Google OAuth <PERSON>gin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"googleToken\": \"ya29.example-google-token\",\n  \"lmsData\": {\n    \"userId\": \"12345\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON>\",\n    \"roles\": [\"Student\"]\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/google", "host": ["{{baseUrl}}"], "path": ["api", "auth", "google"]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    pm.environment.set('accessToken', responseJson.tokens.accessToken);", "    pm.environment.set('refreshToken', responseJson.tokens.refreshToken);", "    pm.environment.set('userId', responseJson.user.id);", "    console.log('Login successful, tokens saved');", "}"], "type": "text/javascript"}}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get Users (Admin/Instructor)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users?page=1&limit=10&search=&role=&status=", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "role", "value": ""}, {"key": "status", "value": ""}]}}, "response": []}, {"name": "Get Current User Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/profile", "host": ["{{baseUrl}}"], "path": ["api", "users", "profile"]}}, "response": []}, {"name": "Update Current User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"profileData\": {\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"Do<PERSON>\",\n    \"bio\": \"Computer Science student at BITS Pilani\",\n    \"preferences\": {\n      \"theme\": \"dark\",\n      \"notifications\": true\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/users/profile", "host": ["{{baseUrl}}"], "path": ["api", "users", "profile"]}}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}, "response": []}, {"name": "Update User Status (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "status"]}}, "response": []}]}, {"name": "Course Management", "item": [{"name": "Get Courses", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/courses?status=active&semester=&search=", "host": ["{{baseUrl}}"], "path": ["api", "courses"], "query": [{"key": "status", "value": "active"}, {"key": "semester", "value": ""}, {"key": "search", "value": ""}]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.courses && responseJson.courses.length > 0) {", "        pm.environment.set('courseId', responseJson.courses[0].id);", "    }", "}"], "type": "text/javascript"}}]}, {"name": "Create Course", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Data Science Fundamentals\",\n  \"code\": \"CS F364\",\n  \"description\": \"Introduction to data science concepts and techniques\",\n  \"semester\": \"2024-1\",\n  \"settings\": {\n    \"allowLateSubmissions\": true,\n    \"autoGrading\": false,\n    \"maxAttempts\": 3\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/courses", "host": ["{{baseUrl}}"], "path": ["api", "courses"]}}, "response": []}, {"name": "Get Course Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/courses/{{courseId}}", "host": ["{{baseUrl}}"], "path": ["api", "courses", "{{courseId}}"]}}, "response": []}, {"name": "Update Course", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Advanced Data Science\",\n  \"description\": \"Advanced topics in data science and machine learning\",\n  \"settings\": {\n    \"allowLateSubmissions\": false,\n    \"autoGrading\": true,\n    \"maxAttempts\": 2\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/courses/{{courseId}}", "host": ["{{baseUrl}}"], "path": ["api", "courses", "{{courseId}}"]}}, "response": []}, {"name": "Get Course Enrollments", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/courses/{{courseId}}/enrollments", "host": ["{{baseUrl}}"], "path": ["api", "courses", "{{courseId}}", "enrollments"]}}, "response": []}, {"name": "Enroll User in Course", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{userId}}\",\n  \"role\": \"student\"\n}"}, "url": {"raw": "{{baseUrl}}/api/courses/{{courseId}}/enroll", "host": ["{{baseUrl}}"], "path": ["api", "courses", "{{courseId}}", "enroll"]}}, "response": []}]}, {"name": "Project Management", "item": [{"name": "Get Projects", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/projects?courseId={{courseId}}&status=published&difficulty=&search=", "host": ["{{baseUrl}}"], "path": ["api", "projects"], "query": [{"key": "courseId", "value": "{{courseId}}"}, {"key": "status", "value": "published"}, {"key": "difficulty", "value": ""}, {"key": "search", "value": ""}]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.projects && responseJson.projects.length > 0) {", "        pm.environment.set('projectId', responseJson.projects[0].id);", "    }", "}"], "type": "text/javascript"}}]}, {"name": "Create Project", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Customer Segmentation Analysis\",\n  \"description\": \"Analyze customer data to identify distinct segments\",\n  \"courseId\": \"{{courseId}}\",\n  \"difficultyLevel\": \"intermediate\",\n  \"dueDate\": \"2024-03-15T23:59:59Z\",\n  \"maxAttempts\": 3,\n  \"instructions\": \"Use K-means clustering to segment customers based on their purchasing behavior\",\n  \"requirements\": [\n    \"Python pandas and scikit-learn\",\n    \"Jupyter notebook with analysis\",\n    \"Visualization of clusters\"\n  ],\n  \"resources\": [\n    {\n      \"type\": \"dataset\",\n      \"name\": \"customer_data.csv\",\n      \"url\": \"https://example.com/dataset\",\n      \"description\": \"Customer transaction data\"\n    }\n  ],\n  \"rubrics\": [\n    {\n      \"criteria\": \"Data Cleaning\",\n      \"description\": \"Proper handling of missing values and outliers\",\n      \"maxScore\": 20,\n      \"weight\": 0.2\n    },\n    {\n      \"criteria\": \"Analysis Quality\",\n      \"description\": \"Correct implementation of clustering algorithm\",\n      \"maxScore\": 40,\n      \"weight\": 0.4\n    },\n    {\n      \"criteria\": \"Visualization\",\n      \"description\": \"Clear and informative visualizations\",\n      \"maxScore\": 25,\n      \"weight\": 0.25\n    },\n    {\n      \"criteria\": \"Interpretation\",\n      \"description\": \"Meaningful insights and conclusions\",\n      \"maxScore\": 15,\n      \"weight\": 0.15\n    }\n  ],\n  \"isTemplate\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/projects", "host": ["{{baseUrl}}"], "path": ["api", "projects"]}}, "response": []}, {"name": "Get Project Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "projects", "{{projectId}}"]}}, "response": []}, {"name": "Update Project", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Advanced Customer Segmentation Analysis\",\n  \"description\": \"Updated description with advanced techniques\",\n  \"difficultyLevel\": \"advanced\",\n  \"maxAttempts\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "projects", "{{projectId}}"]}}, "response": []}, {"name": "Duplicate Project as Template", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/projects/{{projectId}}/duplicate", "host": ["{{baseUrl}}"], "path": ["api", "projects", "{{projectId}}", "duplicate"]}}, "response": []}, {"name": "Get Project Templates", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/projects/templates", "host": ["{{baseUrl}}"], "path": ["api", "projects", "templates"]}}, "response": []}]}, {"name": "Submission Management", "item": [{"name": "Get Submissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/submissions?projectId={{projectId}}&userId=&status=&graded=", "host": ["{{baseUrl}}"], "path": ["api", "submissions"], "query": [{"key": "projectId", "value": "{{projectId}}"}, {"key": "userId", "value": ""}, {"key": "status", "value": ""}, {"key": "graded", "value": ""}]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.submissions && responseJson.submissions.length > 0) {", "        pm.environment.set('submissionId', responseJson.submissions[0].id);", "    }", "}"], "type": "text/javascript"}}]}, {"name": "Create Submission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{projectId}}\",\n  \"notebooks\": [\n    {\n      \"name\": \"customer_analysis.ipynb\",\n      \"content\": \"{\\\"cells\\\": [{\\\"cell_type\\\": \\\"code\\\", \\\"source\\\": [\\\"import pandas as pd\\\\n\\\", \\\"import numpy as np\\\"]}], \\\"metadata\\\": {}, \\\"nbformat\\\": 4}\",\n      \"cellOutputs\": []\n    }\n  ],\n  \"files\": [\n    {\n      \"name\": \"results.csv\",\n      \"s3Key\": \"submissions/user123/project456/results.csv\",\n      \"type\": \"text/csv\"\n    }\n  ],\n  \"metadata\": {\n    \"environment\": \"jupyter\",\n    \"packages\": [\"pandas\", \"numpy\", \"scikit-learn\"],\n    \"executionTime\": 1200\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/submissions", "host": ["{{baseUrl}}"], "path": ["api", "submissions"]}}, "response": []}, {"name": "Get Submission Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/submissions/{{submissionId}}", "host": ["{{baseUrl}}"], "path": ["api", "submissions", "{{submissionId}}"]}}, "response": []}, {"name": "Update Submission", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notebooks\": [\n    {\n      \"name\": \"customer_analysis_updated.ipynb\",\n      \"content\": \"{\\\"cells\\\": [{\\\"cell_type\\\": \\\"code\\\", \\\"source\\\": [\\\"# Updated analysis\\\\n\\\", \\\"import pandas as pd\\\"]}]}\",\n      \"cellOutputs\": []\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/submissions/{{submissionId}}", "host": ["{{baseUrl}}"], "path": ["api", "submissions", "{{submissionId}}"]}}, "response": []}, {"name": "Submit for Grading", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/submissions/{{submissionId}}/submit", "host": ["{{baseUrl}}"], "path": ["api", "submissions", "{{submissionId}}", "submit"]}}, "response": []}, {"name": "Auto-save Submission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notebooks\": [\n    {\n      \"name\": \"customer_analysis.ipynb\",\n      \"content\": \"{\\\"cells\\\": [{\\\"cell_type\\\": \\\"code\\\", \\\"source\\\": [\\\"# Work in progress\\\\n\\\", \\\"import pandas as pd\\\"]}]}\",\n      \"cellOutputs\": []\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/submissions/{{submissionId}}/auto-save", "host": ["{{baseUrl}}"], "path": ["api", "submissions", "{{submissionId}}", "auto-save"]}}, "response": []}]}, {"name": "Grade Management", "item": [{"name": "Get Grades", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/grades?submissionId={{submissionId}}&projectId={{projectId}}&userId=&evaluatorId=", "host": ["{{baseUrl}}"], "path": ["api", "grades"], "query": [{"key": "submissionId", "value": "{{submissionId}}"}, {"key": "projectId", "value": "{{projectId}}"}, {"key": "userId", "value": ""}, {"key": "evaluatorId", "value": ""}]}}, "response": []}, {"name": "Create Grade", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"submissionId\": \"{{submissionId}}\",\n  \"rubricScores\": [\n    {\n      \"rubricId\": \"rubric-1\",\n      \"score\": 18,\n      \"feedback\": \"Good data cleaning approach\"\n    },\n    {\n      \"rubricId\": \"rubric-2\",\n      \"score\": 35,\n      \"feedback\": \"Clustering algorithm implemented correctly\"\n    },\n    {\n      \"rubricId\": \"rubric-3\",\n      \"score\": 22,\n      \"feedback\": \"Excellent visualizations\"\n    },\n    {\n      \"rubricId\": \"rubric-4\",\n      \"score\": 12,\n      \"feedback\": \"Insights could be more detailed\"\n    }\n  ],\n  \"overallFeedback\": \"Great work overall! The analysis is solid and the visualizations are clear. Consider providing more detailed business insights.\",\n  \"sendNotification\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/grades", "host": ["{{baseUrl}}"], "path": ["api", "grades"]}}, "response": []}, {"name": "Update Grade", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rubricScores\": [\n    {\n      \"rubricId\": \"rubric-1\",\n      \"score\": 20,\n      \"feedback\": \"Excellent data cleaning approach\"\n    }\n  ],\n  \"overallFeedback\": \"Updated: Outstanding work on this project!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/grades/grade-id-here", "host": ["{{baseUrl}}"], "path": ["api", "grades", "grade-id-here"]}}, "response": []}, {"name": "Bulk Grade Submissions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grades\": [\n    {\n      \"submissionId\": \"submission-1\",\n      \"rubricScores\": [\n        {\"rubricId\": \"rubric-1\", \"score\": 18, \"feedback\": \"Good work\"}\n      ],\n      \"overallFeedback\": \"Well done\"\n    },\n    {\n      \"submissionId\": \"submission-2\",\n      \"rubricScores\": [\n        {\"rubricId\": \"rubric-1\", \"score\": 20, \"feedback\": \"Excellent\"}\n      ],\n      \"overallFeedback\": \"Outstanding\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/grades/bulk", "host": ["{{baseUrl}}"], "path": ["api", "grades", "bulk"]}}, "response": []}, {"name": "Get Grading Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/grades/analytics/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "grades", "analytics", "{{projectId}}"]}}, "response": []}]}, {"name": "Role & Permission Management", "item": [{"name": "Get All Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/roles", "host": ["{{baseUrl}}"], "path": ["api", "roles"]}}, "response": []}, {"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Teaching Assistant\",\n  \"description\": \"Assists instructors with course management and grading\",\n  \"permissions\": [\"read_submissions\", \"grade_submissions\", \"read_users\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/roles", "host": ["{{baseUrl}}"], "path": ["api", "roles"]}}, "response": []}, {"name": "Get All Permissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/roles/permissions", "host": ["{{baseUrl}}"], "path": ["api", "roles", "permissions"]}}, "response": []}, {"name": "Update Role Permissions", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"permissions\": [\"read_submissions\", \"grade_submissions\", \"read_users\", \"manage_projects\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/roles/role-id-here/permissions", "host": ["{{baseUrl}}"], "path": ["api", "roles", "role-id-here", "permissions"]}}, "response": []}, {"name": "Assign Role to User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roleId\": \"role-id-here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/roles", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "roles"]}}, "response": []}]}, {"name": "File Storage (S3)", "item": [{"name": "Get Upload URL", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fileName\": \"customer_data.csv\",\n  \"fileType\": \"text/csv\",\n  \"purpose\": \"submission\",\n  \"metadata\": {\n    \"projectId\": \"{{projectId}}\",\n    \"submissionId\": \"{{submissionId}}\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/s3/upload", "host": ["{{baseUrl}}"], "path": ["api", "s3", "upload"]}}, "response": []}, {"name": "Get Download URL", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/s3/download/submissions%2Fuser123%2Fproject456%2Fcustomer_data.csv", "host": ["{{baseUrl}}"], "path": ["api", "s3", "download", "submissions%2Fuser123%2Fproject456%2Fcustomer_data.csv"]}}, "response": []}, {"name": "List User Files", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/s3/list?purpose=submission&projectId={{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "s3", "list"], "query": [{"key": "purpose", "value": "submission"}, {"key": "projectId", "value": "{{projectId}}"}]}}, "response": []}, {"name": "Delete File", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/s3/submissions%2Fuser123%2Fproject456%2Fold_file.csv", "host": ["{{baseUrl}}"], "path": ["api", "s3", "submissions%2Fuser123%2Fproject456%2Fold_file.csv"]}}, "response": []}]}, {"name": "LMS Integration", "item": [{"name": "Sync Users from LMS", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/lms/sync/users", "host": ["{{baseUrl}}"], "path": ["api", "lms", "sync", "users"]}}, "response": []}, {"name": "Sync Courses from LMS", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/lms/sync/courses", "host": ["{{baseUrl}}"], "path": ["api", "lms", "sync", "courses"]}}, "response": []}, {"name": "Get LMS Sync Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lms/status", "host": ["{{baseUrl}}"], "path": ["api", "lms", "status"]}}, "response": []}]}, {"name": "LTI Integration", "item": [{"name": "Get LTI Configuration", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lti/config", "host": ["{{baseUrl}}"], "path": ["api", "lti", "config"]}}, "response": []}, {"name": "LTI Login Initiation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "iss", "value": "https://brightspace.bits.edu"}, {"key": "login_hint", "value": "user123"}, {"key": "target_link_uri", "value": "https://platform.bits.edu/lti/launch"}, {"key": "client_id", "value": "bits-datascience-platform"}, {"key": "lti_message_hint", "value": "optional-hint"}]}, "url": {"raw": "{{baseUrl}}/api/lti/login", "host": ["{{baseUrl}}"], "path": ["api", "lti", "login"]}}, "response": []}, {"name": "LTI Launch", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "id_token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."}, {"key": "state", "value": "state-value-from-login"}]}, "url": {"raw": "{{baseUrl}}/api/lti/launch", "host": ["{{baseUrl}}"], "path": ["api", "lti", "launch"]}}, "response": []}, {"name": "Get JWKS", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lti/jwks", "host": ["{{baseUrl}}"], "path": ["api", "lti", "jwks"]}}, "response": []}, {"name": "Get LTI Platforms", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lti/platforms", "host": ["{{baseUrl}}"], "path": ["api", "lti", "platforms"]}}, "response": []}, {"name": "Register LTI Platform", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"platformId\": \"https://brightspace.bits.edu\",\n  \"platformName\": \"BITS Pilani Brightspace\",\n  \"clientId\": \"bits-datascience-platform\",\n  \"authLoginUrl\": \"https://brightspace.bits.edu/d2l/lti/authenticate\",\n  \"authTokenUrl\": \"https://brightspace.bits.edu/d2l/lti/token\",\n  \"keySetUrl\": \"https://brightspace.bits.edu/d2l/lti/keys\",\n  \"settings\": {\n    \"description\": \"Main LMS platform for BITS Pilani\",\n    \"supportedScopes\": [\n      \"openid\",\n      \"https://purl.imsglobal.org/spec/lti-ags/scope/lineitem\",\n      \"https://purl.imsglobal.org/spec/lti-ags/scope/score\"\n    ]\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/lti/platforms", "host": ["{{baseUrl}}"], "path": ["api", "lti", "platforms"]}}, "response": []}, {"name": "Send Grade to LMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"submissionId\": \"{{submissionId}}\",\n  \"gradeId\": \"grade-id-here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/lti/grades", "host": ["{{baseUrl}}"], "path": ["api", "lti", "grades"]}}, "response": []}, {"name": "Get Course LTI Contexts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/lti/contexts/course/{{courseId}}", "host": ["{{baseUrl}}"], "path": ["api", "lti", "contexts", "course", "{{courseId}}"]}}, "response": []}]}, {"name": "Health & Utility", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-refresh token if needed", "const accessToken = pm.environment.get('accessToken');", "if (!accessToken && pm.request.url.path.join('/') !== 'api/auth/google') {", "    console.log('No access token found. Please authenticate first.');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global response logging", "if (pm.response.code >= 400) {", "    console.log('Request failed with status:', pm.response.code);", "    console.log('Response:', pm.response.text());", "}"]}}]}