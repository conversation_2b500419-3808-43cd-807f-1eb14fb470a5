# User Creation Scripts

This directory contains scripts for creating and managing users in the BITS DataScience Platform.

## create-users.js

A comprehensive script for creating users with different roles (admin, instructor, student, etc.).

### Usage

#### 1. Create predefined users

```bash
# Create predefined users (skips existing ones)
npm run create-users -- --predefined

# Force recreate predefined users (deletes existing ones first)
npm run create-users -- --predefined --force
```

#### 2. Create a batch of users with all roles

```bash
npm run create-users -- --batch
```

This creates:

- 1 super_admin
- 2 admins
- 5 instructors
- 3 TAs
- 20 students

#### 3. Create specific users by role

```bash
# Create 1 student (default)
npm run create-users

# Create 5 students
npm run create-users -- --role student --count 5

# Create 2 instructors
npm run create-users -- --role instructor --count 2

# Create 1 admin
npm run create-users -- --role admin --count 1
```

#### 4. Interactive user creation

```bash
npm run create-users -- --interactive
```

This will prompt you to enter:

- User name
- Email address
- Role

#### 5. Direct script execution

```bash
# Using node directly
node scripts/create-users.js --batch
node scripts/create-users.js --role instructor --count 3
node scripts/create-users.js --interactive
```

### Available Roles

- `super_admin` - Full system access
- `admin` - Administrative access
- `instructor` - Course instructor
- `ta` - Teaching assistant
- `student` - Student user

### Generated User Data

The script generates realistic user data including:

**For all users:**

- Name (using faker.js)
- Email address
- Profile picture URL
- User preferences (theme, language, notifications)
- Status: active

**Role-specific metadata:**

**Admin/Super Admin:**

- Department
- Employee ID
- Access level

**Instructor:**

- Department
- Employee ID
- Office hours
- Research interests

**Student:**

- Student ID
- Academic year
- Branch/Department
- CGPA

**TA (Teaching Assistant):**

- Student ID
- Academic year
- Branch/Department
- TA subjects

### Default Credentials

All created users have the default password: `password123`

### Examples

```bash
# Quick setup for development
npm run create-users -- --batch

# Create test instructors
npm run create-users -- --role instructor --count 3

# Create a specific user interactively
npm run create-users -- --interactive

# Create many students for testing
npm run create-users -- --role student --count 50
```

### Output

The script provides detailed output including:

- ✅ Success messages for each created user
- 🎭 Role assignment confirmations
- 📊 Summary with user counts by role
- 🔑 Sample login credentials

### Error Handling

The script handles common errors:

- Database connection issues
- Invalid role names
- Duplicate email addresses
- Missing required parameters

### Database Requirements

Ensure your database is set up and running:

1. PostgreSQL running on localhost:5432
2. Database `bits_platform` exists
3. All migrations have been run
4. Role seeding has been completed

### Troubleshooting

**Database connection errors:**

```bash
# Check if PostgreSQL is running
brew services list | grep postgresql

# Start PostgreSQL if needed
brew services start postgresql@15
```

**Missing roles error:**

```bash
# Run database seeders
npm run db:seed
```

**Permission errors:**

```bash
# Make script executable
chmod +x scripts/create-users.js
```
