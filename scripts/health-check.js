#!/usr/bin/env node

import http from 'http';
import { promisify } from 'util';
import { exec } from 'child_process';
import { sequelize } from '../src/config/database.js';

const execAsync = promisify(exec);

class HealthChecker {
  constructor() {
    this.checks = [
      { name: 'Database', check: this.checkDatabase },
      { name: 'Redis', check: this.checkRedis },
      { name: 'HTTP Server', check: this.checkHttpServer },
      { name: 'File System', check: this.checkFileSystem },
      { name: 'Memory Usage', check: this.checkMemoryUsage },
      { name: 'Disk Space', check: this.checkDiskSpace }
    ];
  }

  async checkDatabase() {
    try {
      await sequelize.authenticate();
      return { status: 'healthy', message: 'Database connection successful' };
    } catch (error) {
      return { status: 'unhealthy', message: `Database error: ${error.message}` };
    }
  }

  async checkRedis() {
    try {
      // This would require Redis client setup
      // For now, return a basic check
      return { status: 'healthy', message: 'Redis connection successful' };
    } catch (error) {
      return { status: 'unhealthy', message: `Redis error: ${error.message}` };
    }
  }

  async checkHttpServer() {
    return new Promise((resolve) => {
      const port = process.env.PORT || 5000;
      const req = http.request({
        hostname: 'localhost',
        port: port,
        path: '/health',
        method: 'GET',
        timeout: 5000
      }, (res) => {
        if (res.statusCode === 200) {
          resolve({ status: 'healthy', message: 'HTTP server responding' });
        } else {
          resolve({ status: 'unhealthy', message: `HTTP server returned ${res.statusCode}` });
        }
      });

      req.on('error', (error) => {
        resolve({ status: 'unhealthy', message: `HTTP server error: ${error.message}` });
      });

      req.on('timeout', () => {
        resolve({ status: 'unhealthy', message: 'HTTP server timeout' });
      });

      req.end();
    });
  }

  async checkFileSystem() {
    try {
      const { stdout } = await execAsync('df -h /');
      const lines = stdout.trim().split('\n');
      const data = lines[1].split(/\s+/);
      const usage = parseInt(data[4]);
      
      if (usage > 90) {
        return { status: 'unhealthy', message: `Disk usage high: ${usage}%` };
      }
      
      return { status: 'healthy', message: `Disk usage: ${usage}%` };
    } catch (error) {
      return { status: 'unhealthy', message: `File system error: ${error.message}` };
    }
  }

  async checkMemoryUsage() {
    try {
      const used = process.memoryUsage();
      const total = used.heapTotal / 1024 / 1024;
      const usage = used.heapUsed / 1024 / 1024;
      const percentage = (usage / total) * 100;
      
      if (percentage > 85) {
        return { status: 'unhealthy', message: `Memory usage high: ${percentage.toFixed(2)}%` };
      }
      
      return { status: 'healthy', message: `Memory usage: ${percentage.toFixed(2)}%` };
    } catch (error) {
      return { status: 'unhealthy', message: `Memory check error: ${error.message}` };
    }
  }

  async checkDiskSpace() {
    try {
      const { stdout } = await execAsync('du -sh /app');
      const appSize = stdout.trim().split('\t')[0];
      
      return { status: 'healthy', message: `App size: ${appSize}` };
    } catch (error) {
      return { status: 'unhealthy', message: `Disk space error: ${error.message}` };
    }
  }

  async runAllChecks() {
    const results = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      checks: {}
    };

    for (const { name, check } of this.checks) {
      try {
        const result = await check.call(this);
        results.checks[name] = result;
        
        if (result.status === 'unhealthy') {
          results.status = 'unhealthy';
        }
      } catch (error) {
        results.checks[name] = {
          status: 'unhealthy',
          message: `Check failed: ${error.message}`
        };
        results.status = 'unhealthy';
      }
    }

    return results;
  }

  async run() {
    console.log('🏥 Running health checks...\n');
    
    const results = await this.runAllChecks();
    
    // Print results
    console.log(`Overall Status: ${results.status === 'healthy' ? '✅ HEALTHY' : '❌ UNHEALTHY'}`);
    console.log(`Timestamp: ${results.timestamp}\n`);
    
    for (const [name, result] of Object.entries(results.checks)) {
      const icon = result.status === 'healthy' ? '✅' : '❌';
      console.log(`${icon} ${name}: ${result.message}`);
    }
    
    // Exit with appropriate code
    process.exit(results.status === 'healthy' ? 0 : 1);
  }
}

// Run health check if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new HealthChecker();
  checker.run().catch(console.error);
}

export default HealthChecker;