#!/usr/bin/env node

/**
 * Test script for multi-user Jupyter integration
 */

import axios from 'axios';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3001/api/jupyter';
const JWT_SECRET = process.env.JWT_SECRET;

// Generate test tokens for different users
const generateTestToken = (userId, username) => {
  const testUser = {
    userId,
    email: `${username}@example.com`,
    username,
    name: username
  };

  const options = {
    expiresIn: '24h',
    issuer: process.env.JWT_ISSUER || 'bits-dataScience-platform',
    audience: process.env.JWT_AUDIENCE || 'bits-platform-users'
  };

  return jwt.sign(testUser, JWT_SECRET, options);
};

class MultiUserJupyterTester {
  constructor() {
    this.users = [
      { id: 1, username: 'alice', token: generateTestToken(1, 'alice') },
      { id: 2, username: 'bob', token: generateTestToken(2, 'bob') },
      { id: 3, username: 'charlie', token: generateTestToken(3, 'charlie') }
    ];
    this.createdResources = new Map(); // Track resources per user
  }

  createUserAPI(user) {
    return axios.create({
      baseURL: BASE_URL,
      timeout: 60000, // Increased timeout for server startup
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${user.token}`
      }
    });
  }

  async testUserAuthentication(user) {
    console.log(`\n👤 Testing authentication for user: ${user.username}`);
    try {
      const api = this.createUserAPI(user);
      const response = await api.post('/authenticate', {
        username: user.username,
        password: 'dummy_password'
      });

      if (response.status === 200) {
        console.log(`✅ User ${user.username} authenticated successfully`);
        console.log(`   Server URL: ${response.data.serverURL}`);
        return response.data;
      }
    } catch (error) {
      console.log(`❌ Authentication failed for ${user.username}:`);
      console.log(`   Error: ${error.response?.data?.message || error.message}`);
      return null;
    }
  }

  async testUserStatus(user) {
    console.log(`\n🔍 Testing status for user: ${user.username}`);
    try {
      const api = this.createUserAPI(user);
      const response = await api.get('/status');

      if (response.status === 200) {
        console.log(`✅ Status check successful for ${user.username}`);
        console.log(`   Server: ${response.data.server_url}`);
        console.log(`   Version: ${response.data.jupyter_version}`);
        return response.data;
      }
    } catch (error) {
      console.log(`❌ Status check failed for ${user.username}:`);
      console.log(`   Error: ${error.response?.data?.message || error.message}`);
      return null;
    }
  }

  async testCreateWorkspace(user, projectId) {
    console.log(`\n🏗️  Creating workspace for user: ${user.username}, project: ${projectId}`);
    try {
      const api = this.createUserAPI(user);
      const response = await api.post('/workspace', {
        projectId,
        kernelName: 'python3',
        username: user.username,
        password: 'dummy_password'
      });

      if (response.status === 201) {
        console.log(`✅ Workspace created successfully for ${user.username}`);
        console.log(`   Project Directory: ${response.data.projectDirectory}`);
        console.log(`   Workspace Notebook: ${response.data.workspaceNotebook}`);
        console.log(`   Kernel ID: ${response.data.kernel_id}`);
        console.log(`   Direct Access: ${response.data.directAccessURL}`);
        
        // Track created resources
        if (!this.createdResources.has(user.id)) {
          this.createdResources.set(user.id, { kernels: [], sessions: [] });
        }
        this.createdResources.get(user.id).kernels.push(response.data.kernel_id);
        this.createdResources.get(user.id).sessions.push(response.data.session.id);
        
        return response.data;
      }
    } catch (error) {
      console.log(`❌ Workspace creation failed for ${user.username}:`);
      console.log(`   Error: ${error.response?.data?.message || error.message}`);
      return null;
    }
  }

  async testExecuteCode(user, kernelId, code) {
    console.log(`\n⚡ Executing code for user: ${user.username}`);
    try {
      const api = this.createUserAPI(user);
      const response = await api.post(`/kernels/${kernelId}/execute`, {
        code,
        timeout: 30000
      });

      if (response.status === 200) {
        console.log(`✅ Code executed successfully for ${user.username}`);
        console.log(`   Execution Count: ${response.data.execution_count}`);
        console.log(`   Status: ${response.data.status}`);
        if (response.data.outputs && response.data.outputs.length > 0) {
          console.log(`   Output: ${JSON.stringify(response.data.outputs[0], null, 2)}`);
        }
        return response.data;
      }
    } catch (error) {
      console.log(`❌ Code execution failed for ${user.username}:`);
      console.log(`   Error: ${error.response?.data?.message || error.message}`);
      return null;
    }
  }

  async testListKernels(user) {
    console.log(`\n📋 Listing kernels for user: ${user.username}`);
    try {
      const api = this.createUserAPI(user);
      const response = await api.get('/kernels');

      if (response.status === 200) {
        console.log(`✅ Listed ${response.data.length} kernels for ${user.username}`);
        response.data.forEach(kernel => {
          console.log(`   - ${kernel.id} (${kernel.name}) - ${kernel.execution_state}`);
        });
        return response.data;
      }
    } catch (error) {
      console.log(`❌ Kernel listing failed for ${user.username}:`);
      console.log(`   Error: ${error.response?.data?.message || error.message}`);
      return [];
    }
  }

  async runFullTest() {
    console.log('🚀 Starting Multi-User Jupyter Integration Test\n');
    console.log('📝 Test Users:');
    this.users.forEach(user => {
      console.log(`   - ${user.username} (ID: ${user.id})`);
    });

    // Test each user independently
    for (const user of this.users) {
      console.log(`\n${'='.repeat(60)}`);
      console.log(`🧪 Testing User: ${user.username.toUpperCase()}`);
      console.log(`${'='.repeat(60)}`);

      // Step 1: Authenticate user
      const authResult = await this.testUserAuthentication(user);
      if (!authResult) {
        console.log(`❌ Skipping further tests for ${user.username} due to auth failure`);
        continue;
      }

      // Step 2: Check status
      await this.testUserStatus(user);

      // Step 3: Create workspace for multiple projects
      const projects = [`project-${user.id}-1`, `project-${user.id}-2`];
      const workspaces = [];

      for (const projectId of projects) {
        const workspace = await this.testCreateWorkspace(user, projectId);
        if (workspace) {
          workspaces.push(workspace);
        }
      }

      // Step 4: List kernels
      await this.testListKernels(user);

      // Step 5: Execute code in each workspace
      for (const workspace of workspaces) {
        const testCode = `
# Test code for ${user.username}
import sys
import os
print(f"Hello from {user.username}!")
print(f"Python version: {sys.version}")
print(f"Current directory: {os.getcwd()}")
print(f"User ID: ${user.id}")
print(f"Project: ${workspace.projectId}")

# Test data science libraries
try:
    import pandas as pd
    import numpy as np
    print(f"Pandas: {pd.__version__}")
    print(f"NumPy: {np.__version__}")
    
    # Create a simple dataset
    data = {'user': ['${user.username}'], 'value': [${user.id * 10}]}
    df = pd.DataFrame(data)
    print("Sample DataFrame:")
    print(df)
except ImportError as e:
    print(f"Library import error: {e}")
`;

        await this.testExecuteCode(user, workspace.kernel_id, testCode);
      }
    }

    // Test isolation - verify users can't access each other's resources
    console.log(`\n${'='.repeat(60)}`);
    console.log('🔒 TESTING USER ISOLATION');
    console.log(`${'='.repeat(60)}`);
    
    await this.testUserIsolation();

    console.log('\n🎉 Multi-User Jupyter Integration Test Completed!');
    console.log('\n📊 Summary:');
    console.log(`   - Tested ${this.users.length} users`);
    console.log(`   - Each user has isolated Jupyter environment`);
    console.log(`   - Project-based workspace structure working`);
    console.log(`   - Code execution working per user`);
  }

  async testUserIsolation() {
    console.log('\n🔒 Testing user isolation...');
    
    // Try to access another user's kernels
    if (this.users.length >= 2) {
      const user1 = this.users[0];
      const user2 = this.users[1];
      
      console.log(`\n🧪 Testing if ${user1.username} can access ${user2.username}'s resources...`);
      
      const user1API = this.createUserAPI(user1);
      const user2Kernels = await this.testListKernels(user2);
      
      if (user2Kernels && user2Kernels.length > 0) {
        try {
          // Try to execute code in user2's kernel using user1's credentials
          await user1API.post(`/kernels/${user2Kernels[0].id}/execute`, {
            code: 'print("This should not work!")'
          });
          console.log('❌ SECURITY ISSUE: User isolation failed!');
        } catch (error) {
          console.log('✅ User isolation working: Cannot access other user\'s kernels');
        }
      }
    }
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  if (!JWT_SECRET) {
    console.error('❌ JWT_SECRET not found in environment variables');
    process.exit(1);
  }

  const tester = new MultiUserJupyterTester();
  tester.runFullTest().catch(error => {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  });
}

export default MultiUserJupyterTester;
