-- Database initialization script for BITS-DataScience Platform
-- This script is run when the PostgreSQL container is first created

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE bits_platform'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'bits_platform');

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create custom types
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_status') THEN
        CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('student', 'instructor', 'admin');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'project_status') THEN
        CREATE TYPE project_status AS ENUM ('draft', 'published', 'archived');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'difficulty_level') THEN
        CREATE TYPE difficulty_level AS ENUM ('beginner', 'intermediate', 'advanced');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'submission_status') THEN
        CREATE TYPE submission_status AS ENUM ('draft', 'submitted', 'graded');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'course_status') THEN
        CREATE TYPE course_status AS ENUM ('active', 'archived');
    END IF;
END$$;

-- Create audit function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        NEW.created_at = CURRENT_TIMESTAMP;
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create search function
CREATE OR REPLACE FUNCTION search_users(search_term TEXT)
RETURNS TABLE(
    id UUID,
    name VARCHAR,
    email VARCHAR,
    similarity REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT u.id, u.name, u.email, 
           GREATEST(
               similarity(u.name, search_term),
               similarity(u.email, search_term)
           ) as sim
    FROM users u
    WHERE u.name % search_term OR u.email % search_term
    ORDER BY sim DESC
    LIMIT 50;
END;
$$ LANGUAGE plpgsql;

-- Create project search function
CREATE OR REPLACE FUNCTION search_projects(search_term TEXT)
RETURNS TABLE(
    id UUID,
    title VARCHAR,
    description TEXT,
    similarity REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT p.id, p.title, p.description,
           GREATEST(
               similarity(p.title, search_term),
               similarity(p.description, search_term)
           ) as sim
    FROM projects p
    WHERE p.title % search_term OR p.description % search_term
    ORDER BY sim DESC
    LIMIT 50;
END;
$$ LANGUAGE plpgsql;

-- Create notification function
CREATE OR REPLACE FUNCTION notify_grade_update()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM pg_notify('grade_updated', json_build_object(
        'grade_id', NEW.id,
        'submission_id', NEW.submission_id,
        'total_score', NEW.total_score,
        'evaluator_id', NEW.evaluator_id
    )::text);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create backup function
CREATE OR REPLACE FUNCTION create_backup_tables()
RETURNS void AS $$
DECLARE
    table_name text;
    backup_suffix text := '_backup_' || to_char(now(), 'YYYY_MM_DD_HH24_MI_SS');
BEGIN
    FOR table_name IN 
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT LIKE '%_backup_%'
    LOOP
        EXECUTE format('CREATE TABLE %I AS SELECT * FROM %I', 
                      table_name || backup_suffix, table_name);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create cleanup function for old sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS void AS $$
BEGIN
    DELETE FROM sessions 
    WHERE expires < CURRENT_TIMESTAMP;
    
    DELETE FROM lti_launch_sessions 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    -- Log cleanup
    RAISE NOTICE 'Cleaned up expired sessions at %', CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Create performance monitoring view
CREATE OR REPLACE VIEW performance_stats AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation,
    most_common_vals,
    most_common_freqs
FROM pg_stats
WHERE schemaname = 'public'
ORDER BY schemaname, tablename, attname;

-- Create user activity view
CREATE OR REPLACE VIEW user_activity AS
SELECT 
    u.id,
    u.name,
    u.email,
    COUNT(DISTINCT s.id) as total_submissions,
    COUNT(DISTINCT g.id) as total_grades,
    MAX(s.created_at) as last_submission,
    MAX(g.created_at) as last_grade
FROM users u
LEFT JOIN submissions s ON u.id = s.user_id
LEFT JOIN grades g ON g.submission_id = s.id
GROUP BY u.id, u.name, u.email;

-- Create course statistics view
CREATE OR REPLACE VIEW course_stats AS
SELECT 
    c.id,
    c.name,
    c.code,
    COUNT(DISTINCT ce.user_id) as enrolled_students,
    COUNT(DISTINCT p.id) as total_projects,
    COUNT(DISTINCT s.id) as total_submissions,
    AVG(g.total_score) as average_grade
FROM courses c
LEFT JOIN course_enrollments ce ON c.id = ce.course_id
LEFT JOIN projects p ON c.id = p.course_id
LEFT JOIN submissions s ON p.id = s.project_id
LEFT JOIN grades g ON s.id = g.submission_id
GROUP BY c.id, c.name, c.code;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_lms_user_id ON users(lms_user_id);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_search ON users USING gin(name gin_trgm_ops, email gin_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_projects_course_id ON projects(course_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_creator_id ON projects(creator_id);
CREATE INDEX IF NOT EXISTS idx_projects_search ON projects USING gin(title gin_trgm_ops, description gin_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_submissions_user_id ON submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_submissions_project_id ON submissions(project_id);
CREATE INDEX IF NOT EXISTS idx_submissions_status ON submissions(status);
CREATE INDEX IF NOT EXISTS idx_submissions_created_at ON submissions(created_at);

CREATE INDEX IF NOT EXISTS idx_grades_submission_id ON grades(submission_id);
CREATE INDEX IF NOT EXISTS idx_grades_evaluator_id ON grades(evaluator_id);
CREATE INDEX IF NOT EXISTS idx_grades_created_at ON grades(created_at);

CREATE INDEX IF NOT EXISTS idx_course_enrollments_course_id ON course_enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_course_enrollments_user_id ON course_enrollments(user_id);

-- Create full-text search indexes
CREATE INDEX IF NOT EXISTS idx_projects_fulltext ON projects USING gin(to_tsvector('english', title || ' ' || description));
CREATE INDEX IF NOT EXISTS idx_users_fulltext ON users USING gin(to_tsvector('english', name || ' ' || email));

-- Print initialization complete message
SELECT 'Database initialization completed successfully!' as message;