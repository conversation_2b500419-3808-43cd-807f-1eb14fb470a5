#!/usr/bin/env node

/**
 * Generate a test JWT token for API testing
 */

import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error('❌ JWT_SECRET not found in environment variables');
  process.exit(1);
}

// Create a test user payload
const testUser = {
  userId: 1,
  email: '<EMAIL>',
  name: 'Test User'
};

const options = {
  expiresIn: '24h',
  issuer: process.env.JWT_ISSUER || 'bits-dataScience-platform',
  audience: process.env.JWT_AUDIENCE || 'bits-platform-users'
};

try {
  const token = jwt.sign(testUser, JWT_SECRET, options);
  
  console.log('✅ Test JWT Token Generated:');
  console.log('');
  console.log(token);
  console.log('');
  console.log('📋 Usage:');
  console.log(`curl -X GET "http://localhost:3001/api/jupyter/status" \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -H "Authorization: Bearer ${token}"`);
  console.log('');
  console.log('⚠️  Note: This token is for testing only and assumes userId 1 exists in the database.');
  console.log('   For production, use proper authentication endpoints.');
  
} catch (error) {
  console.error('❌ Failed to generate token:', error.message);
  process.exit(1);
}
