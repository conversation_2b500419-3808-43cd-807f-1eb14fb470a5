#!/usr/bin/env node

/**
 * List all users in the database
 */

import { User } from '../src/models/associations.js';
import { sequelize } from '../src/config/database.js';

async function listUsers() {
  try {
    console.log('📋 Listing all users...');

    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connected');

    // Get all users
    const users = await User.findAll({
      attributes: ['id', 'name', 'email', 'status', 'last_login', 'createdAt']
    });

    if (users.length === 0) {
      console.log('📭 No users found in database');
    } else {
      console.log(`👥 Found ${users.length} users:`);
      users.forEach((user, index) => {
        console.log(`\n${index + 1}. ${user.name}`);
        console.log(`   ID: ${user.id}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Status: ${user.status}`);
        console.log(`   Last Login: ${user.last_login || 'Never'}`);
        console.log(`   Created: ${user.createdAt}`);
      });
    }

  } catch (error) {
    console.error('❌ Error listing users:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  listUsers().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

export default listUsers;
