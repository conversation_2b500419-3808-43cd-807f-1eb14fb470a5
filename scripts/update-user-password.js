#!/usr/bin/env node

/**
 * Update user password for testing
 */

import { User } from '../src/models/associations.js';
import { sequelize } from '../src/config/database.js';

async function updateUserPassword() {
  try {
    console.log('🔧 Updating user password...');

    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connected');

    // Find student1
    const user = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log(`👤 Found user: ${user.name} (${user.email})`);

    // Update password (will be automatically hashed by the model hook)
    await user.update({
      password_hash: 'password123'
    });

    console.log('✅ Password updated successfully!');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');

  } catch (error) {
    console.error('❌ Error updating password:', error.message);
    if (error.errors) {
      console.error('Validation errors:');
      error.errors.forEach(err => {
        console.error(`  - ${err.path}: ${err.message}`);
      });
    }
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  updateUserPassword().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

export default updateUserPassword;
