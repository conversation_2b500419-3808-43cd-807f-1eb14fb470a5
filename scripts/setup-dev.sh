#!/bin/bash

# BITS-DataScience Platform Backend Development Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up BITS-DataScience Platform Backend Development Environment"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    log_error "Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    log_error "Node.js version $NODE_VERSION is too old. Please install Node.js 18 or higher."
    exit 1
fi

log_info "Node.js version $NODE_VERSION is compatible ✅"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    log_error "npm is not installed. Please install npm."
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    log_warn "Docker is not installed. Some features may not work."
    log_warn "Please install Docker and Docker Compose for full functionality."
else
    log_info "Docker is installed ✅"
fi

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    log_warn "PostgreSQL client is not installed. Database operations may not work."
    log_warn "Please install PostgreSQL client or use Docker."
else
    log_info "PostgreSQL client is installed ✅"
fi

# Install dependencies
log_info "Installing Node.js dependencies..."
npm install

# Create necessary directories
log_info "Creating necessary directories..."
mkdir -p logs
mkdir -p uploads
mkdir -p temp
mkdir -p backups
mkdir -p tests/unit
mkdir -p tests/integration
mkdir -p tests/fixtures

# Copy environment file
if [ ! -f .env ]; then
    log_info "Creating .env file from .env.example..."
    cp .env.example .env
    log_warn "Please edit .env file with your configuration before starting the server."
else
    log_info ".env file already exists ✅"
fi

# Generate JWT secrets if not present
if ! grep -q "JWT_SECRET=your-super-secret" .env; then
    log_info "JWT secrets are already configured ✅"
else
    log_info "Generating JWT secrets..."
    JWT_SECRET=$(openssl rand -hex 32)
    SESSION_SECRET=$(openssl rand -hex 32)
    
    sed -i "s/JWT_SECRET=your-super-secret-jwt-key-change-this-in-production/JWT_SECRET=$JWT_SECRET/" .env
    sed -i "s/SESSION_SECRET=your-super-secret-session-key-change-this-in-production/SESSION_SECRET=$SESSION_SECRET/" .env
    
    log_info "JWT secrets generated ✅"
fi

# Set up Git hooks
if [ -d .git ]; then
    log_info "Setting up Git hooks..."
    npx husky install
    npx husky add .husky/pre-commit "npm run precommit"
    npx husky add .husky/pre-push "npm run test"
    log_info "Git hooks installed ✅"
else
    log_warn "Not a Git repository. Skipping Git hooks setup."
fi

# Start Docker services for development
if command -v docker-compose &> /dev/null; then
    log_info "Starting Docker services for development..."
    docker-compose -f docker-compose.dev.yml up -d postgres redis
    
    # Wait for services to be ready
    log_info "Waiting for database to be ready..."
    sleep 10
    
    # Run database migrations
    log_info "Running database migrations..."
    npm run db:migrate
    
    # Seed database
    log_info "Seeding database..."
    npm run db:seed
    
    log_info "Docker services started ✅"
else
    log_warn "Docker Compose not found. Please start PostgreSQL and Redis manually."
fi

# Run tests
log_info "Running tests to verify setup..."
npm run test

# Check application health
log_info "Starting development server to verify setup..."
npm run dev &
SERVER_PID=$!

# Wait for server to start
sleep 5

# Check if server is responding
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    log_info "Development server is running ✅"
else
    log_error "Development server is not responding"
fi

# Kill the server
kill $SERVER_PID

echo ""
echo "=================================================================="
echo -e "${GREEN}✅ Development environment setup completed successfully!${NC}"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your configuration"
echo "2. Start the development server: npm run dev"
echo "3. Visit http://localhost:5000/health to verify"
echo "4. Access API documentation at http://localhost:5000/api-docs"
echo "5. Access database admin at http://localhost:8080 (Adminer)"
echo "6. Access Redis admin at http://localhost:8081 (Redis Commander)"
echo ""
echo "Useful commands:"
echo "  npm run dev          - Start development server"
echo "  npm run test         - Run tests"
echo "  npm run lint         - Run linting"
echo "  npm run db:migrate   - Run database migrations"
echo "  npm run db:seed      - Seed database"
echo "  npm run health       - Check application health"
echo ""
echo "For more information, see README.md"
echo "=================================================================="