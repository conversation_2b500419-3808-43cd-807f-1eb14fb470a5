#!/usr/bin/env node

/**
 * Test script for Jupyter authentication
 */

import jwt from 'jsonwebtoken';
import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3001';
const JWT_SECRET = process.env.JWT_SECRET;

// Generate a test token
function generateTestToken(userId = 1, username = 'student1') {
  const testUser = {
    userId,
    email: `${username}@example.com`,
    username,
    name: username
  };

  const options = {
    expiresIn: '24h',
    issuer: process.env.JWT_ISSUER || 'bits-dataScience-platform',
    audience: process.env.JWT_AUDIENCE || 'bits-platform-users'
  };

  return jwt.sign(testUser, JWT_SECRET, options);
}

async function testJupyterAuthentication() {
  console.log('🧪 Testing Jupyter Authentication\n');

  const testUsers = [
    { id: 1, username: 'student1' },
    { id: 2, username: 'student2' },
    { id: 3, username: 'alice' }
  ];

  for (const user of testUsers) {
    console.log(`\n👤 Testing user: ${user.username} (ID: ${user.id})`);
    
    try {
      // Generate token for this user
      const token = generateTestToken(user.id, user.username);
      console.log(`   Token generated: ${token.substring(0, 50)}...`);

      // Test authentication
      const authResponse = await axios.post(
        `${BASE_URL}/api/jupyter/authenticate`,
        {
          username: user.username,
          password: 'dummy_password'
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (authResponse.status === 200) {
        console.log(`   ✅ Authentication successful`);
        console.log(`   📍 Server URL: ${authResponse.data.serverURL}`);
        console.log(`   🔧 Dev Mode: ${authResponse.data.devMode || false}`);
        console.log(`   🔄 Fallback: ${authResponse.data.fallback || false}`);

        // Test status check
        const statusResponse = await axios.get(
          `${BASE_URL}/api/jupyter/status`,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        if (statusResponse.status === 200) {
          console.log(`   ✅ Status check successful`);
          console.log(`   📊 Version: ${statusResponse.data.jupyter_version}`);
          console.log(`   🔗 Connected: ${statusResponse.data.status}`);
        }

        // Test workspace creation
        const workspaceResponse = await axios.post(
          `${BASE_URL}/api/jupyter/workspace`,
          {
            projectId: `test-project-${user.id}`,
            kernelName: 'python3',
            username: user.username,
            password: 'dummy_password'
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          }
        );

        if (workspaceResponse.status === 201) {
          console.log(`   ✅ Workspace created successfully`);
          console.log(`   📁 Project Directory: ${workspaceResponse.data.projectDirectory}`);
          console.log(`   📓 Workspace Notebook: ${workspaceResponse.data.workspaceNotebook}`);
          console.log(`   🔑 Kernel ID: ${workspaceResponse.data.kernel_id}`);
          
          if (workspaceResponse.data.directAccessURL) {
            console.log(`   🌐 Direct Access: ${workspaceResponse.data.directAccessURL}`);
          }
        }

      } else {
        console.log(`   ❌ Authentication failed: ${authResponse.status}`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
      if (error.response?.data) {
        console.log(`   📝 Response: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }
  }

  console.log('\n🎉 Jupyter Authentication Test Complete!');
}

// Test login with automatic Jupyter authentication
async function testLoginWithJupyter() {
  console.log('\n🔐 Testing Login with Automatic Jupyter Authentication\n');

  try {
    // Test login (this should automatically authenticate with Jupyter)
    const loginResponse = await axios.post(
      `${BASE_URL}/api/auth/login`,
      {
        email: '<EMAIL>',
        password: 'password123'
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (loginResponse.status === 200) {
      console.log('✅ Login successful');
      console.log(`👤 User: ${loginResponse.data.user.name} (${loginResponse.data.user.email})`);
      
      if (loginResponse.data.user.jupyter) {
        console.log('🚀 Jupyter Authentication:');
        console.log(`   ✅ Authenticated: ${loginResponse.data.user.jupyter.authenticated}`);
        console.log(`   📍 Server URL: ${loginResponse.data.user.jupyter.serverURL}`);
        console.log(`   🔧 Dev Mode: ${loginResponse.data.user.jupyter.devMode}`);
        console.log(`   🔄 Fallback: ${loginResponse.data.user.jupyter.fallback}`);
      } else {
        console.log('❌ No Jupyter authentication info in response');
      }
    }

  } catch (error) {
    console.log(`❌ Login failed: ${error.response?.data?.message || error.message}`);
    if (error.response?.data) {
      console.log(`📝 Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

// Run tests
async function runAllTests() {
  if (!JWT_SECRET) {
    console.error('❌ JWT_SECRET not found in environment variables');
    process.exit(1);
  }

  console.log('🚀 Starting Jupyter Integration Tests\n');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`🔑 JWT Secret configured: ${JWT_SECRET ? 'Yes' : 'No'}\n`);

  await testJupyterAuthentication();
  await testLoginWithJupyter();

  console.log('\n✨ All tests completed!');
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  });
}

export default { testJupyterAuthentication, testLoginWithJupyter };
