#!/usr/bin/env node

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import { join } from 'path';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { createReadStream } from 'fs';

class DatabaseBackup {
  constructor() {
    this.backupDir = process.env.BACKUP_DIR || join(process.cwd(), 'backups');
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      }
    });
    this.s3Bucket = process.env.BACKUP_S3_BUCKET || 'bits-platform-backups';
  }

  async ensureBackupDir() {
    try {
      await fs.mkdir(this.backupDir, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  async createDatabaseBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `bits-platform-backup-${timestamp}.sql`;
    const filepath = join(this.backupDir, filename);

    return new Promise((resolve, reject) => {
      const pgDump = spawn('pg_dump', [
        process.env.DATABASE_URL,
        '--verbose',
        '--clean',
        '--no-acl',
        '--no-owner',
        '--format=plain',
        '--file=' + filepath
      ]);

      let stderr = '';

      pgDump.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pgDump.on('close', (code) => {
        if (code === 0) {
          console.log(`✅ Database backup created: ${filename}`);
          resolve({ filename, filepath });
        } else {
          console.error(`❌ Database backup failed with code ${code}`);
          console.error(stderr);
          reject(new Error(`pg_dump failed with code ${code}`));
        }
      });

      pgDump.on('error', (error) => {
        console.error(`❌ Database backup error: ${error.message}`);
        reject(error);
      });
    });
  }

  async uploadToS3(filepath, filename) {
    try {
      const fileStream = createReadStream(filepath);
      const uploadParams = {
        Bucket: this.s3Bucket,
        Key: `database-backups/${filename}`,
        Body: fileStream,
        ContentType: 'application/sql',
        Metadata: {
          'backup-type': 'database',
          'created-at': new Date().toISOString(),
          'app-name': 'bits-datascience-platform'
        }
      };

      const command = new PutObjectCommand(uploadParams);
      await this.s3Client.send(command);
      
      console.log(`✅ Backup uploaded to S3: ${filename}`);
      return true;
    } catch (error) {
      console.error(`❌ S3 upload failed: ${error.message}`);
      return false;
    }
  }

  async cleanupOldBackups() {
    try {
      const retentionDays = parseInt(process.env.BACKUP_RETENTION_DAYS) || 30;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const files = await fs.readdir(this.backupDir);
      const backupFiles = files.filter(file => file.startsWith('bits-platform-backup-'));

      for (const file of backupFiles) {
        const filepath = join(this.backupDir, file);
        const stats = await fs.stat(filepath);
        
        if (stats.mtime < cutoffDate) {
          await fs.unlink(filepath);
          console.log(`🗑️  Cleaned up old backup: ${file}`);
        }
      }
    } catch (error) {
      console.error(`❌ Cleanup failed: ${error.message}`);
    }
  }

  async run() {
    console.log('🔄 Starting database backup...\n');
    
    try {
      // Ensure backup directory exists
      await this.ensureBackupDir();
      
      // Create database backup
      const { filename, filepath } = await this.createDatabaseBackup();
      
      // Upload to S3 if configured
      if (process.env.BACKUP_S3_BUCKET) {
        await this.uploadToS3(filepath, filename);
      }
      
      // Clean up old backups
      await this.cleanupOldBackups();
      
      console.log('\n✅ Database backup completed successfully!');
      
      // Get file size
      const stats = await fs.stat(filepath);
      const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
      console.log(`📊 Backup size: ${fileSizeMB} MB`);
      
    } catch (error) {
      console.error(`❌ Backup failed: ${error.message}`);
      process.exit(1);
    }
  }
}

// Run backup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const backup = new DatabaseBackup();
  backup.run().catch(console.error);
}

export default DatabaseBackup;