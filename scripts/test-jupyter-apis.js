#!/usr/bin/env node

/**
 * Comprehensive test script for Jupyter API endpoints
 */

import axios from 'axios';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3001/api/jupyter';
const JWT_SECRET = process.env.JWT_SECRET;

// Generate test token
const generateTestToken = () => {
  const testUser = {
    userId: 1,
    email: '<EMAIL>',
    name: 'Test User'
  };

  const options = {
    expiresIn: '24h',
    issuer: process.env.JWT_ISSUER || 'bits-dataScience-platform',
    audience: process.env.JWT_AUDIENCE || 'bits-platform-users'
  };

  return jwt.sign(testUser, JWT_SECRET, options);
};

class JupyterAPITester {
  constructor() {
    this.token = generateTestToken();
    this.api = axios.create({
      baseURL: BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      }
    });
    this.createdResources = {
      kernels: [],
      sessions: []
    };
  }

  async testEndpoint(name, method, url, data = null) {
    try {
      console.log(`\n🧪 Testing ${name}...`);
      const response = await this.api.request({
        method,
        url,
        data
      });
      console.log(`✅ ${name}: SUCCESS (${response.status})`);
      return response.data;
    } catch (error) {
      console.log(`❌ ${name}: FAILED`);
      console.log(`   Status: ${error.response?.status || 'No response'}`);
      console.log(`   Error: ${error.response?.data?.message || error.message}`);
      return null;
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Jupyter API Tests\n');
    console.log(`🔑 Using JWT Token: ${this.token.substring(0, 50)}...`);

    // Test 1: Status Check
    await this.testEndpoint('Status Check', 'GET', '/status');

    // Test 2: List Kernel Specs
    await this.testEndpoint('List Kernel Specs', 'GET', '/kernelspecs');

    // Test 3: List Kernels (should be empty initially)
    await this.testEndpoint('List Kernels (Initial)', 'GET', '/kernels');

    // Test 4: Create Kernel
    const kernel = await this.testEndpoint('Create Kernel', 'POST', '/kernels', {
      name: 'python3'
    });
    if (kernel) {
      this.createdResources.kernels.push(kernel.id);
      console.log(`   Created kernel: ${kernel.id}`);
    }

    // Test 5: List Kernels (should show our new kernel)
    await this.testEndpoint('List Kernels (After Creation)', 'GET', '/kernels');

    // Test 6: Get Kernel Info
    if (kernel) {
      await this.testEndpoint('Get Kernel Info', 'GET', `/kernels/${kernel.id}`);
    }

    // Test 7: Execute Code
    if (kernel) {
      await this.testEndpoint('Execute Code', 'POST', `/kernels/${kernel.id}/execute`, {
        code: 'print("Hello from Jupyter API!")\nimport sys\nprint(f"Python: {sys.version}")'
      });
    }

    // Test 8: Create Session
    const session = await this.testEndpoint('Create Session', 'POST', '/sessions', {
      path: 'test-notebook.ipynb',
      type: 'notebook',
      kernel: { name: 'python3' }
    });
    if (session) {
      this.createdResources.sessions.push(session.id);
      console.log(`   Created session: ${session.id}`);
    }

    // Test 9: List Sessions
    await this.testEndpoint('List Sessions', 'GET', '/sessions');

    // Test 10: Create Workspace
    const workspace = await this.testEndpoint('Create Workspace', 'POST', '/workspace', {
      projectId: 'test-project-123',
      kernelName: 'python3'
    });
    if (workspace) {
      this.createdResources.sessions.push(workspace.session.id);
      this.createdResources.kernels.push(workspace.kernel_id);
      console.log(`   Created workspace for project: test-project-123`);
    }

    // Test 11: Get User Workspaces
    await this.testEndpoint('Get User Workspaces', 'GET', '/workspaces');

    // Test 12: Execute Code in Workspace Kernel
    if (workspace) {
      await this.testEndpoint('Execute Code in Workspace', 'POST', `/kernels/${workspace.kernel_id}/execute`, {
        code: 'import pandas as pd\nprint(f"Pandas version: {pd.__version__}")\ndf = pd.DataFrame({"test": [1, 2, 3]})\nprint(df)'
      });
    }

    // Cleanup
    await this.cleanup();

    console.log('\n🎉 All tests completed!');
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up created resources...');

    // Delete sessions
    for (const sessionId of this.createdResources.sessions) {
      try {
        await this.api.delete(`/sessions/${sessionId}`);
        console.log(`✅ Deleted session: ${sessionId}`);
      } catch (error) {
        console.log(`❌ Failed to delete session ${sessionId}: ${error.message}`);
      }
    }

    // Delete kernels
    for (const kernelId of this.createdResources.kernels) {
      try {
        await this.api.delete(`/kernels/${kernelId}`);
        console.log(`✅ Deleted kernel: ${kernelId}`);
      } catch (error) {
        console.log(`❌ Failed to delete kernel ${kernelId}: ${error.message}`);
      }
    }
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  if (!JWT_SECRET) {
    console.error('❌ JWT_SECRET not found in environment variables');
    process.exit(1);
  }

  const tester = new JupyterAPITester();
  tester.runAllTests().catch(error => {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  });
}

export default JupyterAPITester;
