#!/bin/bash

# BITS-DataScience Platform Backend Deployment Script
# This script handles deployment to staging and production environments

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-staging}"
BRANCH="${2:-main}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(staging|production)$ ]]; then
    log_error "Invalid environment: $ENVIRONMENT"
    log_error "Usage: $0 <staging|production> [branch]"
    exit 1
fi

log_header "Deploying to $ENVIRONMENT environment from $BRANCH branch"
echo "=================================================================="

# Check if we're in the correct directory
if [[ ! -f "$PROJECT_DIR/package.json" ]]; then
    log_error "package.json not found. Are you in the correct directory?"
    exit 1
fi

# Check if required tools are installed
REQUIRED_TOOLS=("node" "npm" "docker" "docker-compose")
for tool in "${REQUIRED_TOOLS[@]}"; do
    if ! command -v "$tool" &> /dev/null; then
        log_error "$tool is not installed"
        exit 1
    fi
done

# Check if environment file exists
ENV_FILE="$PROJECT_DIR/.env.$ENVIRONMENT"
if [[ ! -f "$ENV_FILE" ]]; then
    log_error "Environment file not found: $ENV_FILE"
    exit 1
fi

# Load environment variables
source "$ENV_FILE"

# Pre-deployment checks
log_info "Running pre-deployment checks..."

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if [[ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]]; then
    log_error "Node.js version $NODE_VERSION is too old. Please install Node.js 18 or higher."
    exit 1
fi

# Run tests
log_info "Running tests..."
npm run test

# Run linting
log_info "Running linting..."
npm run lint

# Run security audit
log_info "Running security audit..."
npm audit --audit-level=high

# Build application
log_info "Building application..."
npm run build

# Create backup of current deployment
if [[ "$ENVIRONMENT" == "production" ]]; then
    log_info "Creating backup of current deployment..."
    npm run db:backup
fi

# Docker build and deployment
log_info "Building Docker image..."
docker build -t "bits-datascience-backend:$ENVIRONMENT" -f Dockerfile .

# Tag image with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
docker tag "bits-datascience-backend:$ENVIRONMENT" "bits-datascience-backend:$ENVIRONMENT-$TIMESTAMP"

# Stop existing containers
log_info "Stopping existing containers..."
docker-compose -f "docker-compose.$ENVIRONMENT.yml" down

# Start new containers
log_info "Starting new containers..."
docker-compose -f "docker-compose.$ENVIRONMENT.yml" up -d

# Wait for services to be ready
log_info "Waiting for services to be ready..."
sleep 30

# Run database migrations
log_info "Running database migrations..."
docker-compose -f "docker-compose.$ENVIRONMENT.yml" exec -T backend npm run db:migrate

# Health check
log_info "Performing health check..."
MAX_RETRIES=30
RETRY_COUNT=0

while [[ $RETRY_COUNT -lt $MAX_RETRIES ]]; do
    if curl -f "http://localhost:${PORT:-5000}/health" > /dev/null 2>&1; then
        log_info "Health check passed ✅"
        break
    fi
    
    RETRY_COUNT=$((RETRY_COUNT + 1))
    log_info "Health check failed, retrying ($RETRY_COUNT/$MAX_RETRIES)..."
    sleep 2
done

if [[ $RETRY_COUNT -eq $MAX_RETRIES ]]; then
    log_error "Health check failed after $MAX_RETRIES attempts"
    log_error "Rolling back deployment..."
    
    # Rollback
    docker-compose -f "docker-compose.$ENVIRONMENT.yml" down
    docker run -d --name "bits-backend-rollback" "bits-datascience-backend:$ENVIRONMENT-previous"
    
    exit 1
fi

# Cleanup old images
log_info "Cleaning up old Docker images..."
docker image prune -f

# Update PM2 processes if using PM2
if command -v pm2 &> /dev/null; then
    log_info "Updating PM2 processes..."
    pm2 restart ecosystem.config.js --env "$ENVIRONMENT"
fi

# Post-deployment tasks
log_info "Running post-deployment tasks..."

# Clear cache
if [[ -n "$REDIS_URL" ]]; then
    log_info "Clearing Redis cache..."
    redis-cli -u "$REDIS_URL" FLUSHALL
fi

# Send deployment notification
if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
    log_info "Sending deployment notification..."
    curl -X POST -H 'Content-type: application/json' \
         --data "{\"text\":\"✅ BITS-DataScience Backend deployed to $ENVIRONMENT environment\"}" \
         "$SLACK_WEBHOOK_URL"
fi

# Generate deployment report
DEPLOYMENT_REPORT="$PROJECT_DIR/deployment-report-$ENVIRONMENT-$TIMESTAMP.json"
cat > "$DEPLOYMENT_REPORT" << EOF
{
  "environment": "$ENVIRONMENT",
  "branch": "$BRANCH",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "node_version": "$NODE_VERSION",
  "docker_image": "bits-datascience-backend:$ENVIRONMENT-$TIMESTAMP",
  "health_check": "passed",
  "deployment_status": "success"
}
EOF

log_info "Deployment report generated: $DEPLOYMENT_REPORT"

# Success message
echo ""
echo "=================================================================="
log_info "✅ Deployment to $ENVIRONMENT completed successfully!"
echo ""
echo "Deployment Details:"
echo "  Environment: $ENVIRONMENT"
echo "  Branch: $BRANCH"
echo "  Timestamp: $TIMESTAMP"
echo "  Docker Image: bits-datascience-backend:$ENVIRONMENT-$TIMESTAMP"
echo ""
echo "Next steps:"
echo "1. Monitor application logs: docker-compose -f docker-compose.$ENVIRONMENT.yml logs -f"
echo "2. Check application health: curl http://localhost:${PORT:-5000}/health"
echo "3. Access API documentation: http://localhost:${PORT:-5000}/api-docs"
echo ""
echo "Rollback command (if needed):"
echo "  docker-compose -f docker-compose.$ENVIRONMENT.yml down"
echo "  docker run -d --name bits-backend-rollback bits-datascience-backend:$ENVIRONMENT-previous"
echo "=================================================================="