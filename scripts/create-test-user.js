#!/usr/bin/env node

/**
 * Create a test user for testing login with automatic Jupyter authentication
 */

import bcrypt from 'bcryptjs';
import { User } from '../src/models/associations.js';
import { sequelize } from '../src/config/database.js';
import logger from '../src/config/logger.js';

async function createTestUser() {
  try {
    console.log('🔧 Creating test user...');

    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connected');

    // Check if test user already exists
    const existingUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('👤 Test user already exists, deleting and recreating...');
      await User.destroy({
        where: { email: '<EMAIL>' }
      });
      console.log('✅ Existing user deleted');
    }

    // Create test user (password will be automatically hashed by the model hook)
    const testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password_hash: 'password123', // This will be hashed automatically
      status: 'active',
      preferences: {
        theme: 'light',
        notifications: true
      }
    });

    console.log('✅ Test user created successfully!');
    console.log(`   ID: ${testUser.id}`);
    console.log(`   Name: ${testUser.name}`);
    console.log(`   Email: ${testUser.email}`);
    console.log(`   Password: password123`);

    return testUser;
  } catch (error) {
    console.error('❌ Error creating test user:', error.message);
    if (error.errors) {
      console.error('Validation errors:');
      error.errors.forEach(err => {
        console.error(`  - ${err.path}: ${err.message}`);
      });
    }
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  createTestUser().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

export default createTestUser;
