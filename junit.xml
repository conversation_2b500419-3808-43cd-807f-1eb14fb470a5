<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="18" failures="0" errors="0" time="2.388">
  <testsuite name="TokenBlacklistService" errors="0" failures="0" skipped="0" timestamp="2025-08-18T12:54:25" time="0.968" tests="18">
    <testcase classname="TokenBlacklistService blacklistToken should blacklist token successfully" name="TokenBlacklistService blacklistToken should blacklist token successfully" time="0.007">
    </testcase>
    <testcase classname="TokenBlacklistService blacklistToken should use default expiry when not provided" name="TokenBlacklistService blacklistToken should use default expiry when not provided" time="0.001">
    </testcase>
    <testcase classname="TokenBlacklistService blacklistToken should handle Redis error" name="TokenBlacklistService blacklistToken should handle Redis error" time="0.013">
    </testcase>
    <testcase classname="TokenBlacklistService isTokenBlacklisted should return true for blacklisted token" name="TokenBlacklistService isTokenBlacklisted should return true for blacklisted token" time="0.001">
    </testcase>
    <testcase classname="TokenBlacklistService isTokenBlacklisted should return false for non-blacklisted token" name="TokenBlacklistService isTokenBlacklisted should return false for non-blacklisted token" time="0.001">
    </testcase>
    <testcase classname="TokenBlacklistService isTokenBlacklisted should handle Redis error gracefully" name="TokenBlacklistService isTokenBlacklisted should handle Redis error gracefully" time="0.001">
    </testcase>
    <testcase classname="TokenBlacklistService removeFromBlacklist should remove token from blacklist successfully" name="TokenBlacklistService removeFromBlacklist should remove token from blacklist successfully" time="0.008">
    </testcase>
    <testcase classname="TokenBlacklistService removeFromBlacklist should handle Redis error" name="TokenBlacklistService removeFromBlacklist should handle Redis error" time="0.002">
    </testcase>
    <testcase classname="TokenBlacklistService getBlacklistStats should return blacklist statistics" name="TokenBlacklistService getBlacklistStats should return blacklist statistics" time="0.002">
    </testcase>
    <testcase classname="TokenBlacklistService getBlacklistStats should handle Redis error gracefully" name="TokenBlacklistService getBlacklistStats should handle Redis error gracefully" time="0.002">
    </testcase>
    <testcase classname="TokenBlacklistService getBlacklistStats should handle memory command error" name="TokenBlacklistService getBlacklistStats should handle memory command error" time="0">
    </testcase>
    <testcase classname="TokenBlacklistService cleanupExpiredEntries should cleanup expired entries successfully" name="TokenBlacklistService cleanupExpiredEntries should cleanup expired entries successfully" time="0.001">
    </testcase>
    <testcase classname="TokenBlacklistService cleanupExpiredEntries should handle no expired entries" name="TokenBlacklistService cleanupExpiredEntries should handle no expired entries" time="0">
    </testcase>
    <testcase classname="TokenBlacklistService cleanupExpiredEntries should handle Redis error gracefully" name="TokenBlacklistService cleanupExpiredEntries should handle Redis error gracefully" time="0">
    </testcase>
    <testcase classname="TokenBlacklistService close should close Redis connection successfully" name="TokenBlacklistService close should close Redis connection successfully" time="0">
    </testcase>
    <testcase classname="TokenBlacklistService close should handle Redis close error" name="TokenBlacklistService close should handle Redis close error" time="0.001">
    </testcase>
    <testcase classname="TokenBlacklistService configuration should use correct Redis prefix" name="TokenBlacklistService configuration should use correct Redis prefix" time="0.003">
    </testcase>
    <testcase classname="TokenBlacklistService configuration should use correct default expiry" name="TokenBlacklistService configuration should use correct default expiry" time="0.001">
    </testcase>
  </testsuite>
</testsuites>