import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const LtiDeployment = sequelize.define('LtiDeployment', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  platformId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'platform_id'
  },
  deploymentId: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'deployment_id',
    comment: 'LTI deployment identifier'
  },
  deploymentName: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'deployment_name'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  settings: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'lti_deployments',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default LtiDeployment;