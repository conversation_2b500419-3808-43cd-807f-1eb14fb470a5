import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const Course = sequelize.define('Course', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  lms_course_id: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    comment: 'Course ID from D2L-Brightspace LMS'
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 200]
    }
  },
  code: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [2, 20]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  term: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Academic term (e.g., Fall 2024, Spring 2024)'
  },
  academic_year: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Academic year (e.g., 2024-2025)'
  },
  instructor_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Primary instructor for the course'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'archived', 'draft'),
    defaultValue: 'active'
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  settings: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Course-specific settings and configurations'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional metadata from LMS sync'
  }
}, {
  tableName: 'courses',
  indexes: [
    {
      fields: ['lms_course_id']
    },
    {
      fields: ['instructor_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['term']
    },
    {
      fields: ['academic_year']
    }
  ]
});

export default Course;