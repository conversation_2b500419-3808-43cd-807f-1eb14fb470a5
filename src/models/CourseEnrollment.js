import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const CourseEnrollment = sequelize.define('CourseEnrollment', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  course_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'courses',
      key: 'id'
    }
  },
  role_in_course: {
    type: DataTypes.ENUM('student', 'instructor', 'ta', 'observer'),
    allowNull: false,
    defaultValue: 'student'
  },
  enrollment_status: {
    type: DataTypes.ENUM('active', 'dropped', 'completed', 'withdrawn'),
    defaultValue: 'active'
  },
  enrolled_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  dropped_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  final_grade: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Final course grade (0-100)'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional enrollment metadata from LMS'
  }
}, {
  tableName: 'course_enrollments',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'course_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['course_id']
    },
    {
      fields: ['role_in_course']
    },
    {
      fields: ['enrollment_status']
    }
  ]
});

export default CourseEnrollment;