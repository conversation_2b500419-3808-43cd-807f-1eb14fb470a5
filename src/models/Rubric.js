import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const Rubric = sequelize.define('Rubric', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  criteria: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: [],
    comment: 'Array of rubric criteria with points and descriptions'
  },
  total_points: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: false,
    defaultValue: 100,
    validate: {
      min: 0
    }
  },
  grading_scale: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {
      'A': { min: 90, max: 100 },
      'B': { min: 80, max: 89.99 },
      'C': { min: 70, max: 79.99 },
      'D': { min: 60, max: 69.99 },
      'F': { min: 0, max: 59.99 }
    },
    comment: 'Letter grade scale mapping'
  },
  is_template: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this rubric can be used as a template'
  },
  template_name: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Name for template rubrics'
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'rubrics',
  indexes: [
    {
      fields: ['project_id']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['is_template']
    }
  ]
});

export default Rubric;