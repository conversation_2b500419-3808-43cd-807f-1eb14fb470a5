import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const Role = sequelize.define('Role', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      len: [2, 50]
    }
  },
  lms_role_reference: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Reference to role in D2L-Brightspace LMS'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_system_role: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'System roles cannot be deleted'
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Role priority for hierarchy (higher number = higher priority)'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional role metadata'
  }
}, {
  tableName: 'roles',
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['lms_role_reference']
    },
    {
      fields: ['priority']
    }
  ]
});

export default Role;