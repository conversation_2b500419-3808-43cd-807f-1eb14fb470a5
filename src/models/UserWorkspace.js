import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const UserWorkspace = sequelize.define('UserWorkspace', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  studentId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
    field: 'student_id'
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
    field: 'project_id'
  },
  s3Prefix: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'S3 prefix for the workspace files'
  },
  forkVersion: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Version of the project template when forked'
  },
  isReady: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false,
    comment: 'Whether the workspace is ready for use'
  },
  lastAccessed: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last time the workspace was accessed'
  },
  fileCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false,
    comment: 'Number of files in the workspace'
  },
  totalSize: {
    type: DataTypes.BIGINT,
    defaultValue: 0,
    allowNull: false,
    comment: 'Total size of workspace files in bytes'
  },
  status: {
    type: DataTypes.ENUM('creating', 'ready', 'error', 'archived'),
    defaultValue: 'creating',
    allowNull: false,
    comment: 'Current status of the workspace'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional workspace metadata'
  }
}, {
  tableName: 'user_workspaces',
  indexes: [
    {
      fields: ['student_id']
    },
    {
      fields: ['project_id']
    },
    {
      fields: ['status']
    },
    {
      unique: true,
      fields: ['student_id', 'project_id']
    }
  ],
  hooks: {
    beforeCreate: async (workspace) => {
      // Set fork version to current timestamp
      workspace.forkVersion = new Date().toISOString();
    },
    beforeUpdate: async (workspace) => {
      // Update last accessed timestamp when workspace is accessed
      if (workspace.changed('status') && workspace.status === 'ready') {
        workspace.lastAccessed = new Date();
      }
    }
  }
});

export default UserWorkspace;
