import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const LtiPlatform = sequelize.define(
  'LtiPlatform',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    platformId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      field: 'platform_id',
      comment: 'Platform identifier (iss claim)'
    },
    platformName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'platform_name'
    },
    clientId: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'client_id'
    },
    authLoginUrl: {
      type: DataTypes.TEXT,
      allowNull: false,
      field: 'auth_login_url',
      comment: 'Platform authentication URL'
    },
    authTokenUrl: {
      type: DataTypes.TEXT,
      allowNull: false,
      field: 'auth_token_url',
      comment: 'Platform token endpoint URL'
    },
    keySetUrl: {
      type: DataTypes.TEXT,
      allowNull: false,
      field: 'key_set_url',
      comment: 'Platform JWKS URL'
    },
    privateKey: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'private_key',
      comment: 'Tool private key for this platform'
    },
    publicKey: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'public_key',
      comment: 'Tool public key for this platform'
    },
    keyId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'key_id',
      comment: 'Key identifier for JWKS'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    },
    settings: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {}
    }
  },
  {
    tableName: 'lti_platforms',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
);

export default LtiPlatform;
