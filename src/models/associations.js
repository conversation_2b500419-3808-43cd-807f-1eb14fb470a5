import User from './User.js';
import Role from './Role.js';
import Permission from './Permission.js';
import UserRole from './UserRole.js';
import RolePermission from './RolePermission.js';
import Course from './Course.js';
import CourseEnrollment from './CourseEnrollment.js';
import Project from './Project.js';
import ProjectSandboxSettings from './ProjectSandboxSettings.js';
import UserWorkspace from './UserWorkspace.js';
import Submission from './Submission.js';
import Grade from './Grade.js';
import Rubric from './Rubric.js';
import {
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem,
  LtiLaunchSession
} from './ltiAssociations.js';

// User associations
User.belongsToMany(Role, {
  through: UserRole,
  foreignKey: 'user_id',
  otherKey: 'role_id',
  as: 'roles'
});

Role.belongsToMany(User, {
  through: UserRole,
  foreignKey: 'role_id',
  otherKey: 'user_id',
  as: 'users'
});

// Role associations
Role.belongsToMany(Permission, {
  through: RolePermission,
  foreignKey: 'role_id',
  otherKey: 'permission_id',
  as: 'permissions'
});

Permission.belongsToMany(Role, {
  through: RolePermission,
  foreignKey: 'permission_id',
  otherKey: 'role_id',
  as: 'roles'
});

// Course associations
Course.belongsTo(User, {
  foreignKey: 'instructor_id',
  as: 'instructor'
});

User.hasMany(Course, {
  foreignKey: 'instructor_id',
  as: 'instructedCourses'
});

Course.belongsToMany(User, {
  through: CourseEnrollment,
  foreignKey: 'course_id',
  otherKey: 'user_id',
  as: 'enrolledUsers'
});

User.belongsToMany(Course, {
  through: CourseEnrollment,
  foreignKey: 'user_id',
  otherKey: 'course_id',
  as: 'enrolledCourses'
});

// Project associations
Project.belongsTo(Course, {
  foreignKey: 'course_id',
  as: 'course'
});

Course.hasMany(Project, {
  foreignKey: 'course_id',
  as: 'projects'
});

Project.belongsTo(User, {
  foreignKey: 'creator_id',
  as: 'creator'
});

User.hasMany(Project, {
  foreignKey: 'creator_id',
  as: 'createdProjects'
});

// ProjectSandboxSettings associations
ProjectSandboxSettings.belongsTo(Project, {
  foreignKey: 'projectId',
  as: 'project'
});

Project.hasMany(ProjectSandboxSettings, {
  foreignKey: 'projectId',
  as: 'sandboxSettings'
});

ProjectSandboxSettings.belongsTo(User, {
  foreignKey: 'createdBy',
  as: 'creator'
});

ProjectSandboxSettings.belongsTo(User, {
  foreignKey: 'updatedBy',
  as: 'updater'
});

// UserWorkspace associations
UserWorkspace.belongsTo(User, {
  foreignKey: 'studentId',
  as: 'student'
});

UserWorkspace.belongsTo(Project, {
  foreignKey: 'projectId',
  as: 'project'
});

User.hasMany(UserWorkspace, {
  foreignKey: 'studentId',
  as: 'workspaces'
});

Project.hasMany(UserWorkspace, {
  foreignKey: 'projectId',
  as: 'userWorkspaces'
});

// Submission associations
Submission.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

User.hasMany(Submission, {
  foreignKey: 'user_id',
  as: 'submissions'
});

Submission.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(Submission, {
  foreignKey: 'project_id',
  as: 'submissions'
});

// Grade associations
Grade.belongsTo(Submission, {
  foreignKey: 'submission_id',
  as: 'submission'
});

Submission.hasOne(Grade, {
  foreignKey: 'submission_id',
  as: 'grade'
});

Grade.belongsTo(User, {
  foreignKey: 'evaluator_id',
  as: 'evaluator'
});

User.hasMany(Grade, {
  foreignKey: 'evaluator_id',
  as: 'gradedSubmissions'
});

// Rubric associations
Rubric.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(Rubric, {
  foreignKey: 'project_id',
  as: 'rubrics'
});

// Export all models for easy importing
export {
  User,
  Role,
  Permission,
  UserRole,
  RolePermission,
  Course,
  CourseEnrollment,
  Project,
  ProjectSandboxSettings,
  UserWorkspace,
  Submission,
  Grade,
  Rubric,
  // LTI Models
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem,
  LtiLaunchSession
};