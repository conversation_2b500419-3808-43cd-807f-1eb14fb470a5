import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const LtiLineItem = sequelize.define('LtiLineItem', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  platformId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'platform_id'
  },
  contextId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'context_id'
  },
  resourceLinkId: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'resource_link_id'
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'project_id'
  },
  lineItemId: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    field: 'line_item_id',
    comment: 'LTI line item identifier from AGS'
  },
  scoreMaximum: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 100.00,
    field: 'score_maximum'
  },
  label: {
    type: DataTypes.STRING,
    allowNull: true
  },
  tag: {
    type: DataTypes.STRING,
    allowNull: true
  },
  resourceId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'resource_id'
  },
  startDateTime: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'start_date_time'
  },
  endDateTime: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'end_date_time'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  }
}, {
  tableName: 'lti_line_items',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default LtiLineItem;