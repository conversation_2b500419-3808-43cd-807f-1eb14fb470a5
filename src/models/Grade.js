import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const Grade = sequelize.define('Grade', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  submission_id: {
    type: DataTypes.UUID,
    allowNull: false,
    unique: true,
    references: {
      model: 'submissions',
      key: 'id'
    }
  },
  evaluator_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Instructor or <PERSON><PERSON> who graded the submission'
  },
  rubric_scores: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {},
    comment: 'Scores for each rubric criterion'
  },
  total_score: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  max_score: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    comment: 'Calculated percentage (total_score / max_score * 100)'
  },
  letter_grade: {
    type: DataTypes.STRING(2),
    allowNull: true,
    comment: 'Calculated letter grade based on percentage'
  },
  feedback: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Overall feedback from the evaluator'
  },
  detailed_feedback: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Detailed feedback for each rubric criterion'
  },
  auto_graded_components: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Results from automated grading components'
  },
  grading_time_minutes: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Time spent grading in minutes'
  },
  is_final: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is the final grade for the submission'
  },
  graded_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  released_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the grade was released to the student'
  }
}, {
  tableName: 'grades',
  indexes: [
    {
      fields: ['submission_id']
    },
    {
      fields: ['evaluator_id']
    },
    {
      fields: ['percentage']
    },
    {
      fields: ['letter_grade']
    },
    {
      fields: ['is_final']
    },
    {
      fields: ['graded_at']
    }
  ]
});

export default Grade;