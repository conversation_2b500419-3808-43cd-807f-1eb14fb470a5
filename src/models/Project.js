import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const Project = sequelize.define('Project', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  instructions: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Detailed instructions for the project'
  },
  course_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'courses',
      key: 'id'
    }
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  status: {
    type: DataTypes.ENUM('draft', 'published', 'archived'),
    defaultValue: 'draft'
  },
  difficulty_level: {
    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
    defaultValue: 'beginner'
  },
  estimated_hours: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Estimated completion time in hours'
  },
  notebook_template_s3_url: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'S3 URL for the notebook template'
  },
  dataset_s3_url: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'S3 URL for the project dataset'
  },
  additional_files_s3_urls: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of S3 URLs for additional project files'
  },
  due_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  late_submission_allowed: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  late_penalty_percent: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0,
    comment: 'Percentage penalty per day late (0-100)'
  },
  max_attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: 'Maximum number of submission attempts allowed'
  },
  auto_grading_enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  learning_objectives: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of learning objectives'
  },
  prerequisites: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of prerequisite topics or projects'
  },
  tags: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of tags for categorization'
  },
  settings: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Project-specific settings and configurations'
  }
}, {
  tableName: 'projects',
  indexes: [
    {
      fields: ['course_id']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['status']
    },
    {
      fields: ['difficulty_level']
    },
    {
      fields: ['due_date']
    }
  ]
});

export default Project;