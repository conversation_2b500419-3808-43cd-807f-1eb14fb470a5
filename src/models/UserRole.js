import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

const UserRole = sequelize.define('UserRole', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  role_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'roles',
      key: 'id'
    }
  },
  assigned_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who assigned this role'
  },
  assigned_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  is_primary: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Primary role for the user'
  },
  context: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional context for role assignment (e.g., course-specific)'
  }
}, {
  tableName: 'user_roles',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'role_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['role_id']
    },
    {
      fields: ['is_primary']
    }
  ]
});

export default UserRole;