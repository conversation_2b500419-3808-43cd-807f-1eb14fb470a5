import { CodeBuildClient, StartBuildCommand, BatchGetBuildsCommand, StopBuildCommand } from '@aws-sdk/client-codebuild';
import logger from '../config/logger.js';

class BuildService {
  constructor() {
    this.client = new CodeBuildClient({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      }
    });
  }

  /**
   * Start a new build
   */
  async startBuild(projectName, options = {}) {
    try {
      const {
        sourceVersion = 'main',
        environmentVariables = [],
        artifacts = null,
        cache = null,
        timeoutInMinutes = 60
      } = options;

      const buildParams = {
        projectName: projectName,
        sourceVersion: sourceVersion,
        timeoutInMinutes: timeoutInMinutes
      };

      if (environmentVariables.length > 0) {
        buildParams.environmentVariablesOverride = environmentVariables;
      }

      if (artifacts) {
        buildParams.artifactsOverride = artifacts;
      }

      if (cache) {
        buildParams.cacheOverride = cache;
      }

      const command = new StartBuildCommand(buildParams);
      const result = await this.client.send(command);

      logger.info(`Build started for project: ${projectName}, Build ID: ${result.build.id}`);

      return {
        success: true,
        buildId: result.build.id,
        buildStatus: result.build.buildStatus,
        buildNumber: result.build.buildNumber,
        startTime: result.build.startTime,
        projectName: result.build.projectName
      };

    } catch (error) {
      logger.error('CodeBuild start build error:', error);
      throw new Error(`Failed to start build: ${error.message}`);
    }
  }

  /**
   * Get build information
   */
  async getBuild(buildId) {
    try {
      const command = new BatchGetBuildsCommand({
        ids: [buildId]
      });

      const result = await this.client.send(command);

      if (result.builds.length === 0) {
        throw new Error(`Build not found: ${buildId}`);
      }

      const build = result.builds[0];

      return {
        success: true,
        build: {
          id: build.id,
          projectName: build.projectName,
          buildStatus: build.buildStatus,
          buildNumber: build.buildNumber,
          startTime: build.startTime,
          endTime: build.endTime,
          currentPhase: build.currentPhase,
          phases: build.phases,
          logs: build.logs,
          artifacts: build.artifacts,
          environment: build.environment
        }
      };

    } catch (error) {
      logger.error('CodeBuild get build error:', error);
      throw new Error(`Failed to get build: ${error.message}`);
    }
  }

  /**
   * Get multiple builds
   */
  async getBuilds(buildIds) {
    try {
      const command = new BatchGetBuildsCommand({
        ids: buildIds
      });

      const result = await this.client.send(command);

      return {
        success: true,
        builds: result.builds.map(build => ({
          id: build.id,
          projectName: build.projectName,
          buildStatus: build.buildStatus,
          buildNumber: build.buildNumber,
          startTime: build.startTime,
          endTime: build.endTime,
          currentPhase: build.currentPhase
        }))
      };

    } catch (error) {
      logger.error('CodeBuild get builds error:', error);
      throw new Error(`Failed to get builds: ${error.message}`);
    }
  }

  /**
   * Stop a running build
   */
  async stopBuild(buildId) {
    try {
      const command = new StopBuildCommand({
        id: buildId
      });

      const result = await this.client.send(command);

      logger.info(`Build stopped: ${buildId}`);

      return {
        success: true,
        buildId: result.build.id,
        buildStatus: result.build.buildStatus,
        message: `Build ${buildId} stopped successfully`
      };

    } catch (error) {
      logger.error('CodeBuild stop build error:', error);
      throw new Error(`Failed to stop build: ${error.message}`);
    }
  }

  /**
   * Wait for build completion
   */
  async waitForBuild(buildId, timeoutMinutes = 30) {
    try {
      const startTime = Date.now();
      const timeoutMs = timeoutMinutes * 60 * 1000;

      while (true) {
        const buildInfo = await this.getBuild(buildId);
        const build = buildInfo.build;

        // Check if build is complete
        if (build.buildStatus === 'SUCCEEDED') {
          return {
            success: true,
            build: build,
            message: 'Build completed successfully'
          };
        }

        if (build.buildStatus === 'FAILED' || build.buildStatus === 'FAULT' || build.buildStatus === 'STOPPED') {
          return {
            success: false,
            build: build,
            message: `Build failed with status: ${build.buildStatus}`
          };
        }

        // Check timeout
        if (Date.now() - startTime > timeoutMs) {
          throw new Error(`Build timeout after ${timeoutMinutes} minutes`);
        }

        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 10000)); // 10 seconds
      }

    } catch (error) {
      logger.error('CodeBuild wait for build error:', error);
      throw new Error(`Failed to wait for build: ${error.message}`);
    }
  }

  /**
   * Create environment variables for build
   */
  createEnvironmentVariables(variables) {
    return Object.entries(variables).map(([name, value]) => ({
      name: name,
      value: value,
      type: 'PLAINTEXT'
    }));
  }

  /**
   * Create artifacts configuration
   */
  createArtifactsConfig(type = 'S3', location = null, name = null) {
    const config = {
      type: type
    };

    if (location) {
      config.location = location;
    }

    if (name) {
      config.name = name;
    }

    return config;
  }

  /**
   * Create cache configuration
   */
  createCacheConfig(type = 'NO_CACHE') {
    return {
      type: type
    };
  }

  /**
   * Build a Docker image
   */
  async buildDockerImage(projectName, imageTag, dockerfilePath = 'Dockerfile', contextPath = '.') {
    try {
      const environmentVariables = this.createEnvironmentVariables({
        IMAGE_TAG: imageTag,
        DOCKERFILE_PATH: dockerfilePath,
        CONTEXT_PATH: contextPath
      });

      const result = await this.startBuild(projectName, {
        environmentVariables: environmentVariables
      });

      logger.info(`Docker build started: ${imageTag}`);

      return result;

    } catch (error) {
      logger.error('Docker build error:', error);
      throw new Error(`Failed to start Docker build: ${error.message}`);
    }
  }

  /**
   * Build a Python application
   */
  async buildPythonApp(projectName, requirementsPath = 'requirements.txt', buildScript = null) {
    try {
      const environmentVariables = this.createEnvironmentVariables({
        REQUIREMENTS_PATH: requirementsPath
      });

      if (buildScript) {
        environmentVariables.push({
          name: 'BUILD_SCRIPT',
          value: buildScript,
          type: 'PLAINTEXT'
        });
      }

      const result = await this.startBuild(projectName, {
        environmentVariables: environmentVariables
      });

      logger.info(`Python app build started: ${projectName}`);

      return result;

    } catch (error) {
      logger.error('Python app build error:', error);
      throw new Error(`Failed to start Python app build: ${error.message}`);
    }
  }

  /**
   * Build a Node.js application
   */
  async buildNodeApp(projectName, packageJsonPath = 'package.json', buildScript = 'build') {
    try {
      const environmentVariables = this.createEnvironmentVariables({
        PACKAGE_JSON_PATH: packageJsonPath,
        BUILD_SCRIPT: buildScript
      });

      const result = await this.startBuild(projectName, {
        environmentVariables: environmentVariables
      });

      logger.info(`Node.js app build started: ${projectName}`);

      return result;

    } catch (error) {
      logger.error('Node.js app build error:', error);
      throw new Error(`Failed to start Node.js app build: ${error.message}`);
    }
  }

  /**
   * Get build logs
   */
  async getBuildLogs(buildId) {
    try {
      const buildInfo = await this.getBuild(buildId);
      const build = buildInfo.build;

      if (!build.logs) {
        return {
          success: false,
          message: 'No logs available for this build'
        };
      }

      return {
        success: true,
        logs: build.logs,
        logGroupName: build.logs.groupName,
        logStreamName: build.logs.streamName
      };

    } catch (error) {
      logger.error('CodeBuild get logs error:', error);
      throw new Error(`Failed to get build logs: ${error.message}`);
    }
  }

  /**
   * Get build metrics
   */
  async getBuildMetrics(buildId) {
    try {
      const buildInfo = await this.getBuild(buildId);
      const build = buildInfo.build;

      const metrics = {
        buildId: build.id,
        projectName: build.projectName,
        buildStatus: build.buildStatus,
        buildNumber: build.buildNumber,
        startTime: build.startTime,
        endTime: build.endTime,
        duration: null,
        phases: build.phases || []
      };

      // Calculate duration if build is complete
      if (build.startTime && build.endTime) {
        metrics.duration = build.endTime.getTime() - build.startTime.getTime();
      }

      return {
        success: true,
        metrics: metrics
      };

    } catch (error) {
      logger.error('CodeBuild get metrics error:', error);
      throw new Error(`Failed to get build metrics: ${error.message}`);
    }
  }
}

export default new BuildService();
