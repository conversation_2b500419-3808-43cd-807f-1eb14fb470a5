import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import axios from 'axios';
import { 
  LtiPlatform, 
  LtiDeployment, 
  LtiContext, 
  LtiResourceLink, 
  LtiLineItem,
  LtiLaunchSession 
} from '../models/ltiAssociations.js';
import { User, Course, Project, Grade, Submission } from '../models/associations.js';
import logger from '../config/logger.js';
import { Op } from 'sequelize';

/**
 * LTI 1.3 Service for Brightspace D2L Integration
 * Implements complete OIDC flow, JWT verification, and service endpoints
 */
class LtiService {
  constructor() {
    this.toolUrl = process.env.LTI_TOOL_URL || 'http://localhost:5001';
    this.privateKey = process.env.LTI_PRIVATE_KEY;
    this.publicKey = process.env.LTI_PUBLIC_KEY;
    this.keyId = process.env.LTI_KEY_ID || 'bits-datascience-key-1';
  }

  /**
   * Generate JWKS (JSON Web Key Set) for tool public keys
   */
  generateJWKS() {
    if (!this.publicKey) {
      throw new Error('LTI public key not configured');
    }
    // Import the PEM public key
    const pubKeyObj = crypto.createPublicKey(this.publicKey);

    // Export to JWK (supported in Node 15+)
    const jwk = pubKeyObj.export({ format: 'jwk' });
    return {
      keys: [{
        kty: 'RSA',
        use: 'sig',
        kid: this.keyId,
        alg: 'RS256',
        n: jwk.n,
        e: 'AQAB'
      }]
    };
  }

  /**
   * Generate OIDC login initiation
   * Step 1: Handle login initiation from Brightspace
   */
  async generateOIDCLogin(iss, loginHint, targetLinkUri, ltiMessageHint, clientId) {
    try {
      // Find platform by issuer
      const platform = await LtiPlatform.findOne({
        where: { 
          platformId: iss, 
          clientId: clientId, 
          isActive: true 
        }
      });

      if (!platform) {
        throw new Error(`Platform not found: ${iss}`);
      }

      // Generate state and nonce for security
      const state = this.generateState();
      const nonce = this.generateNonce();

      // Store launch session
      const session = await LtiLaunchSession.create({
        platformId: platform.id,
        sessionId: crypto.randomUUID(),
        state,
        nonce,
        expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
        launchData: {
          targetLinkUri,
          ltiMessageHint,
          loginHint
        }
      });

      // Build OIDC authorization request
      const authParams = new URLSearchParams({
        response_type: 'id_token',
        response_mode: 'form_post',
        scope: 'openid',
        client_id: platform.clientId,
        redirect_uri: `${this.toolUrl}/api/lti/oidc/callback`,
        login_hint: loginHint,
        state,
        nonce,
        prompt: 'none'
      });

      // Add LTI-specific parameters
      if (ltiMessageHint) {
        authParams.append('lti_message_hint', ltiMessageHint);
      }

      const authUrl = `${platform.authLoginUrl}?${authParams.toString()}`;
      
      logger.info(`OIDC login initiated for platform: ${platform.platformName}`);
      
      return {
        authUrl,
        sessionId: session.sessionId
      };

    } catch (error) {
      logger.error('OIDC login generation failed:', error);
      throw error;
    }
  }

  /**
   * Handle OIDC callback and exchange code for ID token
   * Step 2: Exchange authorization code for ID token
   */
  async handleOIDCCallback(code, state) {
    try {
      // Find launch session
      const session = await LtiLaunchSession.findOne({
        where: { 
          state, 
          isUsed: false,
          expiresAt: { [Op.gt]: new Date() }
        },
        include: [{ model: LtiPlatform, as: 'platform' }]
      });

      if (!session) {
        throw new Error('Invalid or expired launch session');
      }

      // Exchange code for ID token
      const tokenResponse = await this.exchangeCodeForToken(code, session.platform);
      
      // Verify and validate ID token
      const launchData = await this.verifyIDToken(tokenResponse.id_token, session.platform);
      
      // Validate nonce
      if (launchData.nonce !== session.nonce) {
        throw new Error('Nonce mismatch');
      }

      // Mark session as used and store ID token
      await session.update({ 
        isUsed: true, 
        idToken: tokenResponse.id_token,
        launchData: { ...session.launchData, ...launchData }
      });

      // Process launch data
      const processedData = await this.processLaunchData(launchData, session.platform);
      
      return {
        user: processedData.user,
        context: processedData.context,
        resourceLink: processedData.resourceLink,
        launchData,
        sessionId: session.sessionId
      };

    } catch (error) {
      logger.error('OIDC callback processing failed:', error);
      throw error;
    }
  }

  /**
   * Exchange authorization code for ID token
   */
  async exchangeCodeForToken(code, platform) {
    try {
      // Create client assertion JWT
      const clientAssertion = this.createClientAssertion(platform);
      
      const tokenParams = new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: `${this.toolUrl}/api/lti/oidc/callback`,
        client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
        client_assertion: clientAssertion
      });

      const response = await axios.post(platform.authTokenUrl, tokenParams, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      return response.data;

    } catch (error) {
      logger.error('Token exchange failed:', error);
      throw new Error('Failed to exchange code for token');
    }
  }

  /**
   * Create client assertion JWT for token exchange
   */
  createClientAssertion(platform) {
    if (!this.privateKey) {
      throw new Error('LTI private key not configured');
    }

    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: platform.clientId,
      sub: platform.clientId,
      aud: platform.authTokenUrl,
      iat: now,
      exp: now + 300, // 5 minutes
      jti: crypto.randomUUID()
    };

    return jwt.sign(payload, this.privateKey, {
      algorithm: 'RS256',
      keyid: this.keyId
    });
  }

  /**
   * Verify ID token from platform
   */
  async verifyIDToken(idToken, platform) {
    try {
      // Get platform's public keys
      const platformKeys = await this.getPlatformKeys(platform.keySetUrl);
      
      // Decode token header to get key ID
      const decodedHeader = jwt.decode(idToken, { complete: true });
      if (!decodedHeader) {
        throw new Error('Invalid token format');
      }

      const keyId = decodedHeader.header.kid;
      const publicKey = platformKeys.keys.find(key => key.kid === keyId);
      
      if (!publicKey) {
        throw new Error('No matching key found for token');
      }

      // Verify token
      const decoded = jwt.verify(idToken, publicKey, {
        algorithms: ['RS256'],
        issuer: platform.platformId,
        audience: platform.clientId
      });

      // Validate required claims
      this.validateIDTokenClaims(decoded);

      return decoded;

    } catch (error) {
      logger.error('ID token verification failed:', error);
      throw error;
    }
  }

  /**
   * Get platform's public keys from JWKS endpoint
   */
  async getPlatformKeys(keySetUrl) {
    try {
      const response = await axios.get(keySetUrl);
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch platform keys:', error);
      throw new Error('Failed to fetch platform public keys');
    }
  }

  /**
   * Validate required ID token claims
   */
  validateIDTokenClaims(payload) {
    const requiredClaims = [
      'iss', 'aud', 'exp', 'iat', 'nonce',
      'https://purl.imsglobal.org/spec/lti/claim/message_type',
      'https://purl.imsglobal.org/spec/lti/claim/version',
      'https://purl.imsglobal.org/spec/lti/claim/deployment_id'
    ];

    for (const claim of requiredClaims) {
      if (!payload[claim]) {
        throw new Error(`Missing required claim: ${claim}`);
      }
    }

    // Validate LTI version
    if (payload['https://purl.imsglobal.org/spec/lti/claim/version'] !== '1.3.0') {
      throw new Error('Unsupported LTI version');
    }

    // Validate message type
    const messageType = payload['https://purl.imsglobal.org/spec/lti/claim/message_type'];
    if (!['LtiResourceLinkRequest', 'LtiDeepLinkingRequest'].includes(messageType)) {
      throw new Error(`Unsupported message type: ${messageType}`);
    }
  }

  /**
   * Process launch data and sync with local database
   */
  async processLaunchData(launchData, platform) {
    try {
      // Extract user information
      const user = await this.syncUser(launchData, platform);
      
      // Extract context information
      const context = await this.syncContext(launchData, platform);
      
      // Extract resource link information
      const resourceLink = await this.syncResourceLink(launchData, platform, context);

      return {
        user,
        context,
        resourceLink
      };

    } catch (error) {
      logger.error('Launch data processing failed:', error);
      throw error;
    }
  }

  /**
   * Sync user information from LTI launch
   */
  async syncUser(launchData, platform) {
    const userData = {
      email: launchData.email,
      name: launchData.name,
      givenName: launchData.given_name,
      familyName: launchData.family_name,
      picture: launchData.picture,
      platformUserId: launchData.sub,
      platformId: platform.id
    };

    // Find or create user
    let user = await User.findOne({
      where: { 
        email: userData.email,
        platformId: platform.id
      }
    });

    if (user) {
      // Update existing user
      await user.update(userData);
    } else {
      // Create new user
      user = await User.create({
        ...userData,
        isActive: true,
        emailVerified: true
      });
    }

    // Map LTI roles to application roles
    await this.mapLTIRoles(user, launchData['https://purl.imsglobal.org/spec/lti/claim/roles']);

    return user;
  }

  /**
   * Map LTI roles to application roles
   */
  async mapLTIRoles(user, ltiRoles) {
    if (!ltiRoles || !Array.isArray(ltiRoles)) {
      return;
    }

    const roleMappings = {
      'http://purl.imsglobal.org/vocab/lis/v2/membership#Instructor': 'instructor',
      'http://purl.imsglobal.org/vocab/lis/v2/membership#TeachingAssistant': 'teaching_assistant',
      'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner': 'student',
      'http://purl.imsglobal.org/vocab/lis/v2/membership#ContentDeveloper': 'content_developer',
      'http://purl.imsglobal.org/vocab/lis/v2/membership#Administrator': 'administrator'
    };

    const mappedRoles = ltiRoles
      .map(role => roleMappings[role])
      .filter(role => role);

    // Update user roles (simplified - you might want to use a proper role system)
    await user.update({ roles: mappedRoles });
  }

  /**
   * Sync context information from LTI launch
   */
  async syncContext(launchData, platform) {
    const contextClaim = launchData['https://purl.imsglobal.org/spec/lti/claim/context'];
    if (!contextClaim) {
      return null;
    }

    const contextData = {
      contextId: contextClaim.id,
      label: contextClaim.label,
      title: contextClaim.title,
      type: contextClaim.type,
      platformId: platform.id
    };

    // Find or create context
    let context = await LtiContext.findOne({
      where: { 
        contextId: contextData.contextId,
        platformId: platform.id
      }
    });

    if (context) {
      await context.update(contextData);
    } else {
      context = await LtiContext.create(contextData);
    }

    return context;
  }

  /**
   * Sync resource link information from LTI launch
   */
  async syncResourceLink(launchData, platform, context) {
    const resourceLinkClaim = launchData['https://purl.imsglobal.org/spec/lti/claim/resource_link'];
    if (!resourceLinkClaim) {
      return null;
    }

    const resourceLinkData = {
      resourceLinkId: resourceLinkClaim.id,
      title: resourceLinkClaim.title,
      description: resourceLinkClaim.description,
      platformId: platform.id,
      contextId: context?.id
    };

    // Find or create resource link
    let resourceLink = await LtiResourceLink.findOne({
      where: { 
        resourceLinkId: resourceLinkData.resourceLinkId,
        platformId: platform.id
      }
    });

    if (resourceLink) {
      await resourceLink.update(resourceLinkData);
    } else {
      resourceLink = await LtiResourceLink.create(resourceLinkData);
    }

    return resourceLink;
  }

  /**
   * Get access token for LTI Advantage services (NRPS, AGS)
   */
  async getServiceAccessToken(platform, scopes) {
    try {
      const clientAssertion = this.createClientAssertion(platform);
      
      const tokenParams = new URLSearchParams({
        grant_type: 'client_credentials',
        client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
        client_assertion: clientAssertion,
        scope: scopes.join(' ')
      });

      const response = await axios.post(platform.authTokenUrl, tokenParams, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      return response.data.access_token;

    } catch (error) {
      logger.error('Service access token request failed:', error);
      throw error;
    }
  }

  /**
   * Get course roster via NRPS (Names and Roles Provisioning Service)
   */
  async getCourseRoster(context, platform) {
    try {
      const accessToken = await this.getServiceAccessToken(platform, [
        'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
      ]);

      const nrpsUrl = context.launchData['https://purl.imsglobal.org/spec/lti-nrps/claim/namesroleservice']?.context_memberships_url;
      
      if (!nrpsUrl) {
        throw new Error('NRPS endpoint not available');
      }

      const response = await axios.get(nrpsUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/vnd.ims.lti-nrps.v2.membershipcontainer+json'
        }
      });

      return response.data;

    } catch (error) {
      logger.error('NRPS roster fetch failed:', error);
      throw error;
    }
  }

  /**
   * Create or update grade via AGS (Assignments and Grades Service)
   */
  async submitGrade(resourceLink, user, score, platform) {
    try {
      const accessToken = await this.getServiceAccessToken(platform, [
        'https://purl.imsglobal.org/spec/lti-ags/scope/score'
      ]);

      // Get or create line item
      const lineItem = await this.getOrCreateLineItem(resourceLink, platform, accessToken);

      // Submit score
      const scoreUrl = `${lineItem.id}/scores`;
      const scoreData = {
        userId: user.platformUserId,
        scoreGiven: score,
        scoreMaximum: 100,
        activityProgress: 'Completed',
        gradingProgress: 'FullyGraded',
        timestamp: new Date().toISOString()
      };

      const response = await axios.post(scoreUrl, scoreData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/vnd.ims.lis.v1.score+json'
        }
      });

      return response.data;

    } catch (error) {
      logger.error('AGS grade submission failed:', error);
      throw error;
    }
  }

  /**
   * Get or create line item for AGS
   */
  async getOrCreateLineItem(resourceLink, platform, accessToken) {
    try {
      // Try to find existing line item
      let lineItem = await LtiLineItem.findOne({
        where: { 
          resourceLinkId: resourceLink.id,
          platformId: platform.id
        }
      });

      if (lineItem) {
        return lineItem;
      }

      // Create new line item
      const agsUrl = resourceLink.launchData['https://purl.imsglobal.org/spec/lti-ags/claim/assignmentsgradeservice']?.lineitems;
      
      if (!agsUrl) {
        throw new Error('AGS endpoint not available');
      }

      const lineItemData = {
        label: resourceLink.title || 'Project Submission',
        scoreMaximum: 100,
        resourceId: resourceLink.resourceLinkId,
        tag: 'project'
      };

      const response = await axios.post(agsUrl, lineItemData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/vnd.ims.lis.v2.lineitem+json'
        }
      });

      // Store line item
      lineItem = await LtiLineItem.create({
        lineItemId: response.data.id,
        label: response.data.label,
        scoreMaximum: response.data.scoreMaximum,
        resourceLinkId: resourceLink.id,
        platformId: platform.id
      });

      return lineItem;

    } catch (error) {
      logger.error('Line item creation failed:', error);
      throw error;
    }
  }

  /**
   * Create deep linking response for content selection
   */
  createDeepLinkingResponse(projects, deepLinkingSettings, platform) {
    const contentItems = projects.map(project => ({
      type: 'ltiResourceLink',
      title: project.title,
      text: project.description,
      url: `${this.toolUrl}/projects/${project.id}`,
      custom: {
        project_id: project.id.toString()
      }
    }));

    const payload = {
      iss: platform.clientId,
      aud: platform.platformId,
      exp: Math.floor(Date.now() / 1000) + 300, // 5 minutes
      iat: Math.floor(Date.now() / 1000),
      nonce: crypto.randomUUID(),
      'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiDeepLinkingResponse',
      'https://purl.imsglobal.org/spec/lti/claim/version': '1.3.0',
      'https://purl.imsglobal.org/spec/lti-dl/claim/content_items': contentItems,
      'https://purl.imsglobal.org/spec/lti-dl/claim/data': deepLinkingSettings.data
    };

    return jwt.sign(payload, this.privateKey, {
      algorithm: 'RS256',
      keyid: this.keyId
    });
  }

  /**
   * Generate state parameter for OIDC flow
   */
  generateState() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate nonce parameter for OIDC flow
   */
  generateNonce() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Clean up expired launch sessions
   */
  async cleanupExpiredSessions() {
    try {
      const deletedCount = await LtiLaunchSession.destroy({
        where: {
          expiresAt: { [Op.lt]: new Date() }
        }
      });

      logger.info(`Cleaned up ${deletedCount} expired LTI sessions`);
      return deletedCount;

    } catch (error) {
      logger.error('Session cleanup failed:', error);
      throw error;
    }
  }
}

export default new LtiService();