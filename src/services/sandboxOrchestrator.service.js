import jupyterhubAdminService from './jupyterhubAdmin.service.js';
import s3WorkspaceService from './s3Workspace.service.js';
import logger from '../config/logger.js';

class SandboxOrchestratorService {
  constructor() {
    this.jupyterhub = jupyterhubAdminService;
    this.workspace = s3WorkspaceService;
  }

  /**
   * Create a complete sandbox environment for a user
   */
  async createSandbox(userId, projectId, options = {}) {
    try {
      const {
        workspaceName = `Project-${projectId}`,
        courseId = null,
        template = 'default',
        jupyterhubUser = userId,
        autoStartServer = true
      } = options;

      logger.info(`Creating sandbox for user: ${userId}, project: ${projectId}`);

      // Step 1: Create S3 workspace
      const workspace = await this.workspace.createWorkspace(userId, workspaceName, {
        courseId: courseId,
        projectId: projectId,
        template: template
      });

      if (!workspace.success) {
        throw new Error(`Failed to create workspace: ${workspace.message}`);
      }

      // Step 2: Create JupyterHub user if needed
      let jupyterhubUserCreated = false;
      try {
        const existingUser = await this.jupyterhub.getUser(jupyterhubUser);
        if (!existingUser.success) {
          const userResult = await this.jupyterhub.createUser(jupyterhubUser, `${jupyterhubUser}@example.com`);
          jupyterhubUserCreated = userResult.success;
          logger.info(`JupyterHub user created: ${jupyterhubUser}`);
        }
      } catch (error) {
        logger.warn(`JupyterHub user creation failed: ${error.message}`);
      }

      // Step 3: Start JupyterHub server if requested
      let serverStarted = false;
      if (autoStartServer && jupyterhubUserCreated) {
        try {
          const serverResult = await this.jupyterhub.startServer(jupyterhubUser);
          serverStarted = serverResult.success;
          logger.info(`JupyterHub server started for user: ${jupyterhubUser}`);
        } catch (error) {
          logger.warn(`JupyterHub server start failed: ${error.message}`);
        }
      }

      logger.info(`Sandbox created successfully: ${workspace.workspaceId}`);

      return {
        success: true,
        sandboxId: workspace.workspaceId,
        workspace: workspace,
        jupyterhubUser: jupyterhubUser,
        jupyterhubUserCreated: jupyterhubUserCreated,
        serverStarted: serverStarted,
        message: `Sandbox created successfully for project ${projectId}`
      };

    } catch (error) {
      logger.error('Sandbox creation error:', error);
      throw new Error(`Failed to create sandbox: ${error.message}`);
    }
  }

  /**
   * Delete a sandbox environment
   */
  async deleteSandbox(sandboxId, userId) {
    try {
      logger.info(`Deleting sandbox: ${sandboxId} for user: ${userId}`);

      // Step 1: Get sandbox information
      const workspace = await this.workspace.getWorkspace(sandboxId);
      
      if (!workspace.success) {
        throw new Error(`Sandbox not found: ${sandboxId}`);
      }

      // Step 2: Stop JupyterHub server if running
      try {
        const serverStatus = await this.jupyterhub.getServerStatus(userId);
        if (serverStatus.success && serverStatus.status) {
          await this.jupyterhub.stopServer(userId);
          logger.info(`JupyterHub server stopped for user: ${userId}`);
        }
      } catch (error) {
        logger.warn(`JupyterHub server stop failed: ${error.message}`);
      }

      // Step 3: Delete S3 workspace
      const workspaceResult = await this.workspace.deleteWorkspace(sandboxId, userId);

      if (!workspaceResult.success) {
        throw new Error(`Failed to delete workspace: ${workspaceResult.message}`);
      }

      logger.info(`Sandbox deleted successfully: ${sandboxId}`);

      return {
        success: true,
        sandboxId: sandboxId,
        message: `Sandbox ${sandboxId} deleted successfully`
      };

    } catch (error) {
      logger.error('Sandbox deletion error:', error);
      throw new Error(`Failed to delete sandbox: ${error.message}`);
    }
  }

  /**
   * Get sandbox status
   */
  async getSandboxStatus(sandboxId, userId) {
    try {
      // Get workspace information
      const workspace = await this.workspace.getWorkspace(sandboxId);
      
      if (!workspace.success) {
        return {
          success: false,
          message: `Sandbox not found: ${sandboxId}`
        };
      }

      // Get JupyterHub server status
      let serverStatus = null;
      try {
        const jupyterStatus = await this.jupyterhub.getServerStatus(userId);
        serverStatus = jupyterStatus.success ? jupyterStatus.status : null;
      } catch (error) {
        logger.warn(`Failed to get JupyterHub server status: ${error.message}`);
      }

      return {
        success: true,
        sandboxId: sandboxId,
        workspace: workspace.workspace,
        serverStatus: serverStatus,
        isActive: workspace.workspace.status === 'active',
        fileCount: workspace.workspace.fileCount || 0
      };

    } catch (error) {
      logger.error('Sandbox status error:', error);
      throw new Error(`Failed to get sandbox status: ${error.message}`);
    }
  }

  /**
   * Start JupyterHub server for a sandbox
   */
  async startSandboxServer(sandboxId, userId) {
    try {
      // Verify sandbox exists
      const workspace = await this.workspace.getWorkspace(sandboxId);
      
      if (!workspace.success) {
        throw new Error(`Sandbox not found: ${sandboxId}`);
      }

      // Start JupyterHub server
      const serverResult = await this.jupyterhub.startServer(userId);

      if (!serverResult.success) {
        throw new Error(`Failed to start JupyterHub server: ${serverResult.message}`);
      }

      logger.info(`Sandbox server started: ${sandboxId} for user: ${userId}`);

      return {
        success: true,
        sandboxId: sandboxId,
        serverStatus: serverResult.server,
        message: `Server started for sandbox ${sandboxId}`
      };

    } catch (error) {
      logger.error('Sandbox server start error:', error);
      throw new Error(`Failed to start sandbox server: ${error.message}`);
    }
  }

  /**
   * Stop JupyterHub server for a sandbox
   */
  async stopSandboxServer(sandboxId, userId) {
    try {
      // Verify sandbox exists
      const workspace = await this.workspace.getWorkspace(sandboxId);
      
      if (!workspace.success) {
        throw new Error(`Sandbox not found: ${sandboxId}`);
      }

      // Stop JupyterHub server
      const serverResult = await this.jupyterhub.stopServer(userId);

      if (!serverResult.success) {
        throw new Error(`Failed to stop JupyterHub server: ${serverResult.message}`);
      }

      logger.info(`Sandbox server stopped: ${sandboxId} for user: ${userId}`);

      return {
        success: true,
        sandboxId: sandboxId,
        message: `Server stopped for sandbox ${sandboxId}`
      };

    } catch (error) {
      logger.error('Sandbox server stop error:', error);
      throw new Error(`Failed to stop sandbox server: ${error.message}`);
    }
  }

  /**
   * Upload file to sandbox
   */
  async uploadFileToSandbox(sandboxId, filePath, fileContent, options = {}) {
    try {
      const result = await this.workspace.uploadFile(sandboxId, filePath, fileContent, options);

      if (!result.success) {
        throw new Error(`Failed to upload file: ${result.message}`);
      }

      logger.info(`File uploaded to sandbox ${sandboxId}: ${filePath}`);

      return {
        success: true,
        sandboxId: sandboxId,
        filePath: filePath,
        url: result.url,
        message: `File ${filePath} uploaded to sandbox successfully`
      };

    } catch (error) {
      logger.error('Sandbox file upload error:', error);
      throw new Error(`Failed to upload file to sandbox: ${error.message}`);
    }
  }

  /**
   * Download file from sandbox
   */
  async downloadFileFromSandbox(sandboxId, filePath) {
    try {
      const result = await this.workspace.downloadFile(sandboxId, filePath);

      if (!result.success) {
        throw new Error(`Failed to download file: ${result.message}`);
      }

      return {
        success: true,
        sandboxId: sandboxId,
        filePath: filePath,
        content: result.content,
        contentType: result.contentType,
        size: result.size
      };

    } catch (error) {
      logger.error('Sandbox file download error:', error);
      throw new Error(`Failed to download file from sandbox: ${error.message}`);
    }
  }

  /**
   * List files in sandbox
   */
  async listSandboxFiles(sandboxId) {
    try {
      const files = await this.workspace.listWorkspaceFiles(sandboxId);

      return {
        success: true,
        sandboxId: sandboxId,
        files: files,
        fileCount: files.length
      };

    } catch (error) {
      logger.error('Sandbox file list error:', error);
      throw new Error(`Failed to list sandbox files: ${error.message}`);
    }
  }

  /**
   * Copy sandbox to new user
   */
  async copySandbox(sourceSandboxId, targetUserId, newWorkspaceName) {
    try {
      const result = await this.workspace.copyWorkspace(sourceSandboxId, targetUserId, newWorkspaceName);

      if (!result.success) {
        throw new Error(`Failed to copy sandbox: ${result.message}`);
      }

      logger.info(`Sandbox copied: ${sourceSandboxId} -> ${result.targetWorkspaceId}`);

      return {
        success: true,
        sourceSandboxId: sourceSandboxId,
        targetSandboxId: result.targetWorkspaceId,
        targetUserId: targetUserId,
        message: `Sandbox copied successfully`
      };

    } catch (error) {
      logger.error('Sandbox copy error:', error);
      throw new Error(`Failed to copy sandbox: ${error.message}`);
    }
  }

  /**
   * Archive sandbox
   */
  async archiveSandbox(sandboxId) {
    try {
      const result = await this.workspace.archiveWorkspace(sandboxId);

      if (!result.success) {
        throw new Error(`Failed to archive sandbox: ${result.message}`);
      }

      logger.info(`Sandbox archived: ${sandboxId} -> ${result.archivePath}`);

      return {
        success: true,
        sandboxId: sandboxId,
        archivePath: result.archivePath,
        message: `Sandbox ${sandboxId} archived successfully`
      };

    } catch (error) {
      logger.error('Sandbox archive error:', error);
      throw new Error(`Failed to archive sandbox: ${error.message}`);
    }
  }

  /**
   * Get all sandboxes for a user
   */
  async getUserSandboxes(userId) {
    try {
      const workspaces = await this.workspace.listUserWorkspaces(userId);

      if (!workspaces.success) {
        throw new Error(`Failed to get user workspaces: ${workspaces.message}`);
      }

      // Get server status for each sandbox
      const sandboxes = await Promise.all(
        workspaces.workspaces.map(async (workspace) => {
          let serverStatus = null;
          try {
            const status = await this.jupyterhub.getServerStatus(userId);
            serverStatus = status.success ? status.status : null;
          } catch (error) {
            logger.warn(`Failed to get server status for workspace ${workspace.workspaceId}: ${error.message}`);
          }

          return {
            ...workspace,
            serverStatus: serverStatus
          };
        })
      );

      return {
        success: true,
        userId: userId,
        sandboxes: sandboxes,
        count: sandboxes.length
      };

    } catch (error) {
      logger.error('Get user sandboxes error:', error);
      throw new Error(`Failed to get user sandboxes: ${error.message}`);
    }
  }

  /**
   * Health check for sandbox services
   */
  async healthCheck() {
    try {
      const results = {
        jupyterhub: false,
        workspace: false,
        overall: false
      };

      // Check JupyterHub health
      try {
        const jupyterHealth = await this.jupyterhub.healthCheck();
        results.jupyterhub = jupyterHealth.success;
      } catch (error) {
        logger.warn('JupyterHub health check failed:', error);
      }

      // Check workspace service (try to list workspaces)
      try {
        const workspaceTest = await this.workspace.listUserWorkspaces('test-user');
        results.workspace = true; // If no error, service is working
      } catch (error) {
        logger.warn('Workspace service health check failed:', error);
      }

      results.overall = results.jupyterhub && results.workspace;

      return {
        success: true,
        status: results.overall ? 'healthy' : 'degraded',
        services: results,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Sandbox orchestrator health check error:', error);
      return {
        success: false,
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

export default new SandboxOrchestratorService();
