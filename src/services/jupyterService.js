import WebSocket from 'ws';
import axios from 'axios';
import logger from '../config/logger.js';

class JupyterService {
  constructor() {
    this.baseURL = process.env.JUPYTER_SERVER_URL || 'http://localhost:8888';
    this.token = process.env.JUPYTER_AUTH_TOKEN;
    this.wsConnections = new Map(); // Store WebSocket connections by kernel ID
  }

  // Create axios instance with authentication
  createAxiosInstance() {
    return axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        Authorization: `token ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
  }

  // Test connection to Jupyter server
  async testConnection() {
    try {
      const api = this.createAxiosInstance();
      const response = await api.get('/api');
      console.log('Jupyter server response:', response.data);
      return {
        connected: true,
        version: response.data.version,
        server_url: this.baseURL
      };
    } catch (error) {
      logger.error('Jupyter connection test failed:', error.message);
      return {
        connected: false,
        error: error.message
      };
    }
  }

  // Create a new kernel
  async createKernel(kernelName = 'python3') {
    try {
      const api = this.createAxiosInstance();
      const response = await api.post('/api/kernels', { name: kernelName });
      logger.info(`Created kernel: ${response.data.id}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to create kernel:', error.message);
      throw error;
    }
  }

  // Get kernel information
  async getKernel(kernelId) {
    try {
      const api = this.createAxiosInstance();
      const response = await api.get(`/api/kernels/${kernelId}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to get kernel ${kernelId}:`, error.message);
      throw error;
    }
  }

  // Delete a kernel
  async deleteKernel(kernelId) {
    try {
      const api = this.createAxiosInstance();
      await api.delete(`/api/kernels/${kernelId}`);

      // Close WebSocket connection if exists
      if (this.wsConnections.has(kernelId)) {
        const ws = this.wsConnections.get(kernelId);
        ws.close();
        this.wsConnections.delete(kernelId);
      }

      logger.info(`Deleted kernel: ${kernelId}`);
    } catch (error) {
      logger.error(`Failed to delete kernel ${kernelId}:`, error.message);
      throw error;
    }
  }

  // Create WebSocket connection to kernel
  createKernelWebSocket(kernelId) {
    const wsUrl =
      this.baseURL.replace('http', 'ws') +
      `/api/kernels/${kernelId}/channels?token=${this.token}`;

    try {
      const ws = new WebSocket(wsUrl);
      this.wsConnections.set(kernelId, ws);

      ws.on('open', () => {
        logger.info(`WebSocket connected to kernel: ${kernelId}`);
      });

      ws.on('message', data => {
        try {
          const message = JSON.parse(data.toString());
          logger.debug(`Kernel ${kernelId} message:`, message.msg_type);
        } catch (error) {
          logger.error('Failed to parse WebSocket message:', error.message);
        }
      });

      ws.on('error', error => {
        logger.error(`WebSocket error for kernel ${kernelId}:`, error.message);
      });

      ws.on('close', () => {
        logger.info(`WebSocket closed for kernel: ${kernelId}`);
        this.wsConnections.delete(kernelId);
      });

      return ws;
    } catch (error) {
      logger.error(
        `Failed to create WebSocket for kernel ${kernelId}:`,
        error.message
      );
      throw error;
    }
  }

  // Execute code in kernel
  async executeCode(kernelId, code, options = {}) {
    return new Promise((resolve, reject) => {
      try {
        let ws = this.wsConnections.get(kernelId);

        if (!ws || ws.readyState !== WebSocket.OPEN) {
          ws = this.createKernelWebSocket(kernelId);
        }

        const msgId = `execute_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const message = {
          header: {
            msg_id: msgId,
            username: options.username || 'anonymous',
            session: options.session || `session_${Date.now()}`,
            date: new Date().toISOString(),
            msg_type: 'execute_request',
            version: '5.3'
          },
          parent_header: {},
          metadata: {},
          content: {
            code,
            silent: options.silent || false,
            store_history: options.store_history !== false,
            user_expressions: options.user_expressions || {},
            allow_stdin: options.allow_stdin || false,
            stop_on_error: options.stop_on_error !== false
          }
        };

        const results = {
          execution_count: null,
          outputs: [],
          status: 'pending'
        };

        const messageHandler = data => {
          try {
            const response = JSON.parse(data.toString());

            if (response.parent_header.msg_id === msgId) {
              switch (response.msg_type) {
                case 'execute_reply':
                  results.execution_count = response.content.execution_count;
                  results.status = response.content.status;
                  if (response.content.status === 'error') {
                    results.error = {
                      ename: response.content.ename,
                      evalue: response.content.evalue,
                      traceback: response.content.traceback
                    };
                  }
                  ws.removeListener('message', messageHandler);
                  resolve(results);
                  break;

                case 'stream':
                  results.outputs.push({
                    output_type: 'stream',
                    name: response.content.name,
                    text: response.content.text
                  });
                  break;

                case 'execute_result':
                  results.outputs.push({
                    output_type: 'execute_result',
                    execution_count: response.content.execution_count,
                    data: response.content.data,
                    metadata: response.content.metadata
                  });
                  break;

                case 'display_data':
                  results.outputs.push({
                    output_type: 'display_data',
                    data: response.content.data,
                    metadata: response.content.metadata
                  });
                  break;

                case 'error':
                  results.outputs.push({
                    output_type: 'error',
                    ename: response.content.ename,
                    evalue: response.content.evalue,
                    traceback: response.content.traceback
                  });
                  break;
              }
            }
          } catch (error) {
            logger.error('Failed to parse execution response:', error.message);
          }
        };

        ws.on('message', messageHandler);

        // Set timeout for execution
        const timeout = setTimeout(() => {
          ws.removeListener('message', messageHandler);
          reject(new Error('Code execution timeout'));
        }, options.timeout || 30000);

        ws.on('open', () => {
          ws.send(JSON.stringify(message));
        });

        // Clear timeout when resolved
        const originalResolve = resolve;
        resolve = result => {
          clearTimeout(timeout);
          originalResolve(result);
        };

        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify(message));
        }
      } catch (error) {
        logger.error(
          `Failed to execute code in kernel ${kernelId}:`,
          error.message
        );
        reject(error);
      }
    });
  }

  // Create a session
  async createSession(path, kernelName = 'python3', type = 'notebook') {
    try {
      const api = this.createAxiosInstance();
      const response = await api.post('/api/sessions', {
        path,
        type,
        kernel: { name: kernelName }
      });
      logger.info(`Created session: ${response.data.id}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to create session:', error.message);
      throw error;
    }
  }

  // Get session information
  async getSession(sessionId) {
    try {
      const api = this.createAxiosInstance();
      const response = await api.get(`/api/sessions/${sessionId}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to get session ${sessionId}:`, error.message);
      throw error;
    }
  }

  // Delete a session
  async deleteSession(sessionId) {
    try {
      const api = this.createAxiosInstance();
      await api.delete(`/api/sessions/${sessionId}`);
      logger.info(`Deleted session: ${sessionId}`);
    } catch (error) {
      logger.error(`Failed to delete session ${sessionId}:`, error.message);
      throw error;
    }
  }

  // List all sessions
  async listSessions() {
    try {
      const api = this.createAxiosInstance();
      const response = await api.get('/api/sessions');
      return response.data;
    } catch (error) {
      logger.error('Failed to list sessions:', error.message);
      throw error;
    }
  }

  // Clean up all WebSocket connections
  cleanup() {
    for (const [kernelId, ws] of this.wsConnections) {
      try {
        ws.close();
        logger.info(`Closed WebSocket for kernel: ${kernelId}`);
      } catch (error) {
        logger.error(
          `Failed to close WebSocket for kernel ${kernelId}:`,
          error.message
        );
      }
    }
    this.wsConnections.clear();
  }
}

// Create singleton instance
const jupyterService = new JupyterService();

export default jupyterService;
