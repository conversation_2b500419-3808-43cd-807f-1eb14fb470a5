import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt';
import { User, Role, Permission } from '../models/associations.js';
import logger from './logger.js';

// Google OAuth Strategy
passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: process.env.GOOGLE_CALLBACK_URL || '/api/auth/google/callback'
}, async (accessToken, refreshToken, profile, done) => {
  try {
    logger.info(`Google OAuth login attempt for: ${profile.emails[0].value}`);
    
    // Check if user exists
    let user = await User.findOne({
      where: { google_id: profile.id },
      include: [{
        model: Role,
        as: 'roles',
        include: [{
          model: Permission,
          as: 'permissions'
        }]
      }]
    });

    if (user) {
      // Update last login and profile picture if changed
      await user.update({
        last_login: new Date(),
        profile_picture: profile.photos[0]?.value || user.profile_picture,
        name: profile.displayName || user.name
      });
      
      logger.info(`Existing user logged in: ${user.email}`);
      return done(null, user);
    }

    // Check if user exists by email (for linking accounts)
    user = await User.findOne({
      where: { email: profile.emails[0].value },
      include: [{
        model: Role,
        as: 'roles',
        include: [{
          model: Permission,
          as: 'permissions'
        }]
      }]
    });

    if (user) {
      // Link Google account to existing user
      await user.update({
        google_id: profile.id,
        last_login: new Date(),
        profile_picture: profile.photos[0]?.value || user.profile_picture
      });
      
      logger.info(`Google account linked to existing user: ${user.email}`);
      return done(null, user);
    }

    // Create new user - but they need to exist in LMS first
    // For now, we'll create a basic user and sync from LMS later
    user = await User.create({
      google_id: profile.id,
      email: profile.emails[0].value,
      name: profile.displayName,
      profile_picture: profile.photos[0]?.value,
      last_login: new Date(),
      status: 'active'
    });

    // Assign default student role for new users
    const studentRole = await Role.findOne({ where: { name: 'student' } });
    if (studentRole) {
      await user.addRole(studentRole);
    }

    logger.info(`New user created via Google OAuth: ${user.email}`);
    return done(null, user);
    
  } catch (error) {
    logger.error('Google OAuth error:', error);
    return done(error, null);
  }
}));

// JWT Strategy
passport.use(new JwtStrategy({
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  secretOrKey: process.env.JWT_SECRET,
  passReqToCallback: true
}, async (req, payload, done) => {
  try {
    const user = await User.findByPk(payload.userId, {
      include: [{
        model: Role,
        as: 'roles',
        include: [{
          model: Permission,
          as: 'permissions'
        }]
      }]
    });

    if (user && user.status === 'active') {
      // Add user permissions to req for RBAC middleware
      req.userPermissions = [];
      user.roles?.forEach(role => {
        role.permissions?.forEach(permission => {
          if (!req.userPermissions.includes(permission.key)) {
            req.userPermissions.push(permission.key);
          }
        });
      });
      
      return done(null, user);
    }
    
    return done(null, false);
  } catch (error) {
    logger.error('JWT verification error:', error);
    return done(error, false);
  }
}));

// Serialize/Deserialize user for session management (if using sessions)
passport.serializeUser((user, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findByPk(id, {
      include: [{
        model: Role,
        as: 'roles',
        include: [{
          model: Permission,
          as: 'permissions'
        }]
      }]
    });
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

export default passport;