import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';
import logger from './logger.js';

dotenv.config();

const config = {
  development: {
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'bits_platform',
    host: process.env.DB_HOST || '127.0.0.1',
    port: Number(process.env.DB_PORT) || 5432,
    dialect: 'postgres',
    logging: (msg) => logger?.debug ? logger.debug(msg) : console.debug(msg),
    pool: { max: 10, min: 0, acquire: 30000, idle: 10000 },
    define: { timestamps: true, underscored: true, paranoid: true, freezeTableName: true },
    ssl: false,
  },
  test: {
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME_TEST || 'bits_datascience_test',
    host: process.env.DB_HOST || '127.0.0.1',
    port: Number(process.env.DB_PORT) || 5432,
    dialect: 'postgres',
    logging: false,
    pool: { max: 5, min: 0, acquire: 30000, idle: 10000 },
    define: { timestamps: true, underscored: true, paranoid: true, freezeTableName: true },
    ssl: false,
  },
  production: {
    // typically use DATABASE_URL in prod
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: Number(process.env.DB_PORT) || 5432,
    dialect: 'postgres',
    logging: false,
    pool: { max: 20, min: 5, acquire: 30000, idle: 10000 },
    ssl: process.env.DB_SSL === 'true' ? { require: true, rejectUnauthorized: false } : false,
    define: { timestamps: true, underscored: true, paranoid: true, freezeTableName: true },
  }
};

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

let sequelize;

// Prefer DATABASE_URL if provided
if (process.env.DATABASE_URL) {
  sequelize = new Sequelize(process.env.DATABASE_URL, {
    dialect: 'postgres',
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    define: dbConfig.define,
    dialectOptions: dbConfig.ssl ? { ssl: dbConfig.ssl } : {},
  });
} else {
  sequelize = new Sequelize(
    dbConfig.database,
    dbConfig.username,
    dbConfig.password,
    {
      host: dbConfig.host,
      port: dbConfig.port,
      dialect: dbConfig.dialect,
      logging: dbConfig.logging,
      pool: dbConfig.pool,
      define: dbConfig.define,
      dialectOptions: dbConfig.ssl ? { ssl: dbConfig.ssl } : {},
    }
  );
}

export { sequelize };

// Only auto-connect if not in test mode (tests will handle their own connections)
if (env !== 'test') {
  (async () => {
    try {
      await sequelize.authenticate();
      logger.info(`✅ Database connection established successfully (${env})`);
    } catch (err) {
      logger.error('❌ Unable to connect to the database:', err);
      // Re-throw so nodemon shows the reason and restarts on changes
      throw err;
    }
  })();
}

export default config;