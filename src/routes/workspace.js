import express from 'express';
import {
  forkExercise,
  getWorkspace<PERSON>tatus,
  listWorkspaceFiles,
  uploadWorkspaceFile,
  deleteWorkspaceFile,
  getUserWorkspaces
} from '../controllers/workspace.controller.js';
import { requirePermissions } from '../middlewares/rbac.js';
import { body, param, query } from 'express-validator';
import { validate } from '../middlewares/validation.js';
import multer from 'multer';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow specific file types for workspace
    const allowedMimes = [
      'text/plain',
      'application/json',
      'text/csv',
      'application/zip',
      'application/x-zip-compressed',
      'application/octet-stream',
      'image/png',
      'image/jpeg',
      'image/gif',
      'application/pdf',
      'text/x-python',
      'application/x-python-code',
      'text/x-script.python'
    ];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type for workspace'), false);
    }
  }
});

/**
 * @swagger
 * tags:
 *   name: Workspace
 *   description: User workspace management for projects
 */

/**
 * @swagger
 * /api/workspace/fork/{projectId}:
 *   post:
 *     summary: Fork a project exercise to create a personal workspace
 *     tags: [Workspace]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID to fork
 *     responses:
 *       201:
 *         description: Workspace forked successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Project not found
 *       403:
 *         description: Access denied
 */
router.post('/fork/:projectId', [
  requirePermissions(['create_workspace']),
  param('projectId').isUUID()
], validate, forkExercise);

/**
 * @swagger
 * /api/workspace/status/{projectId}:
 *   get:
 *     summary: Get workspace status for a project
 *     tags: [Workspace]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID
 *     responses:
 *       200:
 *         description: Workspace status retrieved successfully
 *       404:
 *         description: Workspace not found
 *       403:
 *         description: Access denied
 */
router.get('/status/:projectId', [
  requirePermissions(['view_workspace']),
  param('projectId').isUUID()
], validate, getWorkspaceStatus);

/**
 * @swagger
 * /api/workspace/files/{projectId}:
 *   get:
 *     summary: List files in workspace
 *     tags: [Workspace]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: prefix
 *         schema:
 *           type: string
 *         description: File prefix filter
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1000
 *         description: Maximum number of files to return
 *     responses:
 *       200:
 *         description: Files listed successfully
 *       404:
 *         description: Workspace not found
 *       403:
 *         description: Access denied
 */
router.get('/files/:projectId', [
  requirePermissions(['view_workspace']),
  param('projectId').isUUID(),
  query('prefix').optional().isString(),
  query('limit').optional().isInt({ min: 1, max: 1000 }).toInt()
], validate, listWorkspaceFiles);

/**
 * @swagger
 * /api/workspace/upload/{projectId}:
 *   post:
 *     summary: Upload file to workspace
 *     tags: [Workspace]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: File to upload
 *               path:
 *                 type: string
 *                 description: Path within workspace (optional)
 *     responses:
 *       200:
 *         description: File uploaded successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Workspace not found
 *       403:
 *         description: Access denied
 */
router.post('/upload/:projectId', [
  requirePermissions(['upload_workspace_file']),
  param('projectId').isUUID(),
  body('path').optional().isString()
], validate, upload.single('file'), uploadWorkspaceFile);

/**
 * @swagger
 * /api/workspace/delete/{projectId}:
 *   delete:
 *     summary: Delete file from workspace
 *     tags: [Workspace]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - filePath
 *             properties:
 *               filePath:
 *                 type: string
 *                 description: Path to file within workspace
 *     responses:
 *       200:
 *         description: File deleted successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: File or workspace not found
 *       403:
 *         description: Access denied
 */
router.delete('/delete/:projectId', [
  requirePermissions(['delete_workspace_file']),
  param('projectId').isUUID(),
  body('filePath').isString().notEmpty()
], validate, deleteWorkspaceFile);

/**
 * @swagger
 * /api/workspace/list:
 *   get:
 *     summary: Get all user workspaces
 *     tags: [Workspace]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [creating, ready, error, archived]
 *         description: Filter by workspace status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Workspaces retrieved successfully
 *       403:
 *         description: Access denied
 */
router.get('/list', [
  requirePermissions(['view_workspace']),
  query('status').optional().isIn(['creating', 'ready', 'error', 'archived']),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt()
], validate, getUserWorkspaces);

export default router;
