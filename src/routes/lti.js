import express from 'express';
import { body } from 'express-validator';
import { validate } from '../middlewares/validation.js';
import { authenticateLTI } from '../middlewares/ltiAuth.js';
import { requirePermissions } from '../middlewares/rbac.js';
import {
  oidcInit,
  oidcCallback,
  getJWKS,
  handleDeepLinking,
  getCourseRoster,
  submitGrade,
  registerPlatform,
  getLTIConfiguration,
  getLTISession,
  cleanupLTISessions
} from '../controllers/ltiController.js';

const router = express.Router();

// Public LTI endpoints (no authentication required)
router.get('/config', getLTIConfiguration);

/**
 * @swagger
 * /.well-known/jwks.json:
 *   get:
 *     summary: Get tool's public key set (JWKS)
 *     tags: [LTI]
 *     description: Provides public keys for JWT verification by LTI platforms
 *     responses:
 *       200:
 *         description: JWKS retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 keys:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       kty:
 *                         type: string
 *                         example: RSA
 *                       use:
 *                         type: string
 *                         example: sig
 *                       kid:
 *                         type: string
 *                         example: bits-datascience-key-1
 *                       alg:
 *                         type: string
 *                         example: RS256
 *                       n:
 *                         type: string
 *                         description: Public key modulus
 *                       e:
 *                         type: string
 *                         example: AQAB
 */
router.get('/.well-known/jwks.json', getJWKS);

/**
 * @swagger
 * /api/lti/oidc/init:
 *   get:
 *     summary: OIDC Login Initiation endpoint
 *     tags: [LTI]
 *     description: Initiates LTI 1.3 OIDC authentication flow (Step 1)
 *     parameters:
 *       - in: query
 *         name: iss
 *         required: true
 *         schema:
 *           type: string
 *         description: Platform issuer identifier
 *       - in: query
 *         name: login_hint
 *         required: true
 *         schema:
 *           type: string
 *         description: User identifier hint from platform
 *       - in: query
 *         name: target_link_uri
 *         required: true
 *         schema:
 *           type: string
 *         description: Target URI for the launch
 *       - in: query
 *         name: client_id
 *         required: true
 *         schema:
 *           type: string
 *         description: OAuth client ID
 *       - in: query
 *         name: lti_message_hint
 *         schema:
 *           type: string
 *         description: Optional message hint
 *     responses:
 *       302:
 *         description: Redirect to platform authentication
 *       400:
 *         description: Invalid parameters or platform not configured
 */
router.get('/oidc/init', oidcInit);

/**
 * @swagger
 * /api/lti/oidc/init:
 *   post:
 *     summary: OIDC Login Initiation endpoint (POST)
 *     tags: [LTI]
 *     description: Initiates LTI 1.3 OIDC authentication flow via POST
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - iss
 *               - login_hint
 *               - target_link_uri
 *               - client_id
 *             properties:
 *               iss:
 *                 type: string
 *                 description: Platform issuer identifier
 *               login_hint:
 *                 type: string
 *                 description: User identifier hint from platform
 *               target_link_uri:
 *                 type: string
 *                 description: Target URI for the launch
 *               client_id:
 *                 type: string
 *                 description: OAuth client ID
 *               lti_message_hint:
 *                 type: string
 *                 description: Optional message hint
 *     responses:
 *       302:
 *         description: Redirect to platform authentication
 *       400:
 *         description: Invalid parameters or platform not configured
 */
router.post('/oidc/init', [
  body('iss').isURL(),
  body('login_hint').isString(),
  body('target_link_uri').isURL(),
  body('client_id').isString(),
  body('lti_message_hint').optional().isString()
], validate, oidcInit);

/**
 * @swagger
 * /api/lti/oidc/callback:
 *   post:
 *     summary: OIDC Callback endpoint
 *     tags: [LTI]
 *     description: Handles OIDC callback and exchanges code for ID token (Step 2)
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - state
 *             properties:
 *               code:
 *                 type: string
 *                 description: Authorization code from platform
 *               state:
 *                 type: string
 *                 description: State parameter for security
 *     responses:
 *       302:
 *         description: Redirect to application page
 *       400:
 *         description: Invalid callback parameters or token exchange failed
 */
router.post('/oidc/callback', [
  body('code').isString(),
  body('state').isString()
], validate, oidcCallback);

/**
 * @swagger
 * /api/lti/deep-linking:
 *   post:
 *     summary: Deep linking endpoint
 *     tags: [LTI]
 *     description: Handles deep linking requests for content selection
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             required:
 *               - id_token
 *               - state
 *             properties:
 *               id_token:
 *                 type: string
 *                 description: JWT ID token from platform
 *               state:
 *                 type: string
 *                 description: State parameter for security
 *     responses:
 *       200:
 *         description: Content selection page
 *       400:
 *         description: Invalid deep linking request
 */
router.post('/deep-linking', [
  body('id_token').isJWT(),
  body('state').isString()
], validate, handleDeepLinking);



/**
 * @swagger
 * /api/lti/platforms:
 *   post:
 *     summary: Register new LTI platform
 *     tags: [LTI]
 *     description: Registers a new LTI platform for integration
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platformName
 *               - platformId
 *               - clientId
 *               - authLoginUrl
 *               - authTokenUrl
 *               - keySetUrl
 *             properties:
 *               platformName:
 *                 type: string
 *                 description: Human-readable platform name
 *               platformId:
 *                 type: string
 *                 description: Platform issuer identifier
 *               clientId:
 *                 type: string
 *                 description: OAuth client ID
 *               authLoginUrl:
 *                 type: string
 *                 format: uri
 *                 description: Platform authorization endpoint
 *               authTokenUrl:
 *                 type: string
 *                 format: uri
 *                 description: Platform token endpoint
 *               keySetUrl:
 *                 type: string
 *                 format: uri
 *                 description: Platform JWKS endpoint
 *               deploymentId:
 *                 type: string
 *                 description: Optional deployment ID
 *     responses:
 *       201:
 *         description: Platform registered successfully
 *       400:
 *         description: Missing required fields
 *       409:
 *         description: Platform already exists
 *       500:
 *         description: Registration failed
 */
router.post('/platforms', [
  body('platformName').isString().notEmpty(),
  body('platformId').isURL(),
  body('clientId').isString().notEmpty(),
  body('authLoginUrl').isURL(),
  body('authTokenUrl').isURL(),
  body('keySetUrl').isURL(),
  body('deploymentId').optional().isString()
], validate, registerPlatform);


// Admin endpoints
router.use(requirePermissions(['admin']));
/**
 * @swagger
 * /api/lti/cleanup:
 *   post:
 *     summary: Clean up expired LTI sessions
 *     tags: [LTI]
 *     description: Removes expired LTI launch sessions
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cleanup completed successfully
 *       500:
 *         description: Cleanup failed
 */
router.post('/cleanup', cleanupLTISessions);

// LTI authenticated endpoints
router.use(authenticateLTI);

/**
 * @swagger
 * /api/lti/session:
 *   get:
 *     summary: Get LTI session status
 *     tags: [LTI]
 *     description: Returns current LTI session information
 *     security:
 *       - ltiAuth: []
 *     responses:
 *       200:
 *         description: Session information retrieved successfully
 *       401:
 *         description: No active LTI session
 */
router.get('/session', getLTISession);

/**
 * @swagger
 * /api/lti/roster/{contextId}:
 *   get:
 *     summary: Get course roster via NRPS
 *     tags: [LTI]
 *     description: Retrieves course roster using Names and Roles Provisioning Service
 *     security:
 *       - ltiAuth: []
 *     parameters:
 *       - in: path
 *         name: contextId
 *         required: true
 *         schema:
 *           type: string
 *         description: LTI context ID
 *     responses:
 *       200:
 *         description: Course roster retrieved successfully
 *       403:
 *         description: Access denied to roster
 *       500:
 *         description: Failed to retrieve roster
 */
router.get('/roster/:contextId', getCourseRoster);

/**
 * @swagger
 * /api/lti/grades:
 *   post:
 *     summary: Submit grade via AGS
 *     tags: [LTI]
 *     description: Submits grade to platform using Assignments and Grades Service
 *     security:
 *       - ltiAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - resourceLinkId
 *               - userId
 *               - score
 *             properties:
 *               resourceLinkId:
 *                 type: string
 *                 description: LTI resource link ID
 *               userId:
 *                 type: string
 *                 description: User ID to grade
 *               score:
 *                 type: number
 *                 description: Score to submit
 *               maxScore:
 *                 type: number
 *                 default: 100
 *                 description: Maximum possible score
 *     responses:
 *       200:
 *         description: Grade submitted successfully
 *       403:
 *         description: Invalid resource link
 *       404:
 *         description: User not found
 *       500:
 *         description: Grade submission failed
 */
router.post('/grades', [
  body('resourceLinkId').isString(),
  body('userId').isUUID(),
  body('score').isFloat({ min: 0 }),
  body('maxScore').optional().isFloat({ min: 0 })
], validate, submitGrade);



export default router;