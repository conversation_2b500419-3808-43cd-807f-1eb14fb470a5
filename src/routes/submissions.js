import express from 'express';
import {
  getSubmissions,
  getSubmissionById,
  createOrUpdateSubmission,
  submitAssignment,
  downloadSubmissionNotebook,
  getSubmissionStatistics,
  autoSaveSubmission
} from '../controllers/submissionController.js';
import { requirePermissions, requireRoles } from '../middlewares/rbac.js';
import { body, param, query } from 'express-validator';
import { validate } from '../middlewares/validation.js';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Submissions
 *   description: Student submission management
 */

/**
 * @swagger
 * /api/submissions:
 *   get:
 *     summary: Get all submissions with pagination and filtering
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by project ID
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by user ID (admin/instructor only)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [in_progress, submitted, grading, graded, returned]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Submissions retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get('/', [
  requirePermissions(['view_submissions']),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('projectId').optional().isUUID(),
  query('userId').optional().isUUID(),
  query('status').optional().isIn(['in_progress', 'submitted', 'grading', 'graded', 'returned'])
], validate, getSubmissions);

/**
 * @swagger
 * /api/submissions/{id}:
 *   get:
 *     summary: Get submission by ID
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Submission retrieved successfully
 *       404:
 *         description: Submission not found
 *       403:
 *         description: Access denied
 */
router.get('/:id', [
  param('id').isUUID()
], validate, getSubmissionById);

/**
 * @swagger
 * /api/submissions:
 *   post:
 *     summary: Create or update submission
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *             properties:
 *               projectId:
 *                 type: string
 *                 format: uuid
 *               notebookContent:
 *                 type: string
 *                 description: Jupyter notebook content as JSON string
 *               metadata:
 *                 type: object
 *                 description: Additional metadata for the submission
 *     responses:
 *       200:
 *         description: Submission saved successfully
 *       400:
 *         description: Validation error
 *       403:
 *         description: Access denied
 *       404:
 *         description: Project not found
 */
router.post('/', [
  requirePermissions(['submit_assignments']),
  body('projectId').isUUID(),
  body('notebookContent').optional().isString(),
  body('metadata').optional().isObject()
], validate, createOrUpdateSubmission);

/**
 * @swagger
 * /api/submissions/autosave:
 *   post:
 *     summary: Auto-save submission (periodic saves)
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *               - notebookContent
 *             properties:
 *               projectId:
 *                 type: string
 *                 format: uuid
 *               notebookContent:
 *                 type: string
 *                 description: Jupyter notebook content as JSON string
 *               metadata:
 *                 type: object
 *                 description: Additional metadata for the submission
 *     responses:
 *       200:
 *         description: Auto-save successful
 *       400:
 *         description: Validation error
 *       403:
 *         description: Access denied
 */
router.post('/autosave', [
  requirePermissions(['submit_assignments']),
  body('projectId').isUUID(),
  body('notebookContent').isString(),
  body('metadata').optional().isObject()
], validate, autoSaveSubmission);

/**
 * @swagger
 * /api/submissions/{id}/submit:
 *   post:
 *     summary: Submit assignment (finalize submission)
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Assignment submitted successfully
 *       400:
 *         description: Validation error or already submitted
 *       403:
 *         description: Access denied
 *       404:
 *         description: Submission not found
 */
router.post('/:id/submit', [
  requirePermissions(['submit_assignments']),
  param('id').isUUID()
], validate, submitAssignment);

/**
 * @swagger
 * /api/submissions/{id}/download:
 *   get:
 *     summary: Download submission notebook
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Download URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 downloadUrl:
 *                   type: string
 *                   format: uri
 *                 fileName:
 *                   type: string
 *                 expiresIn:
 *                   type: integer
 *       403:
 *         description: Access denied
 *       404:
 *         description: Submission or notebook not found
 */
router.get('/:id/download', [
  param('id').isUUID()
], validate, downloadSubmissionNotebook);

/**
 * @swagger
 * /api/submissions/project/{projectId}/statistics:
 *   get:
 *     summary: Get submission statistics for a project
 *     tags: [Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Project not found
 */
router.get('/project/:projectId/statistics', [
  requirePermissions(['view_submissions']),
  param('projectId').isUUID()
], validate, getSubmissionStatistics);

export default router;