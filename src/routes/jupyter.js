import express from 'express';
import jwt from 'jsonwebtoken';
import {
  authenticateUser,
  getJupyterStatus,
  listKernelSpecs,
  listKernels,
  createKernel,
  getKernel,
  deleteKernel,
  interruptKernel,
  restartKernel,
  executeCode,
  listSessions,
  createSession,
  getSession,
  deleteSession,
  listContents,
  getContent,
  createContent,
  saveContent,
  deleteContent,
  createNotebook,
  saveNotebook,
  getNotebook,
  createWorkspace,
  getUserWorkspaces
} from '../controllers/jupyterController.js';

const router = express.Router();

// Add logging middleware to debug routing issues
router.use((req, res, next) => {
  console.log(
    `🔍 Jupyter Route: ${req.method} ${req.path} - ${new Date().toISOString()}`
  );
  next();
});

// Test endpoints for development/debugging (remove in production)
if (process.env.NODE_ENV === 'development') {
  router.get('/test', (req, res) => {
    res.json({
      message: 'Jupyter routes are working!',
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method
    });
  });

  // Test Jupyter connection without authentication (for debugging)
  router.get('/test-connection', getJupyterStatus);
}

// Simplified JWT middleware for Jupyter endpoints (doesn't require database lookup)
const simpleJwtAuth = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'No token provided or invalid format'
      });
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Attach minimal user info to request (no database lookup)
    req.user = {
      id: decoded.userId,
      email: decoded.email,
      name: decoded.name
    };

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired',
        message: 'Please login again'
      });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token',
        message: 'Token is malformed'
      });
    } else {
      return res.status(500).json({
        error: 'Authentication error',
        message: 'Internal server error during authentication'
      });
    }
  }
};

// Apply authentication middleware to all routes except test endpoints (in development)
router.use((req, res, next) => {
  if (
    process.env.NODE_ENV === 'development' &&
    (req.path === '/test' || req.path === '/test-connection')
  ) {
    return next();
  }
  return simpleJwtAuth(req, res, next);
});

/**
 * @swagger
 * components:
 *   schemas:
 *     JupyterStatus:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: "connected"
 *         jupyter_version:
 *           type: string
 *           example: "2.8.0"
 *         server_url:
 *           type: string
 *           example: "http://localhost:8888"
 *
 *     KernelSpec:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *         spec:
 *           type: object
 *
 *     Kernel:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         last_activity:
 *           type: string
 *         execution_state:
 *           type: string
 *         connections:
 *           type: integer
 *
 *     Session:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         path:
 *           type: string
 *         name:
 *           type: string
 *         type:
 *           type: string
 *         kernel:
 *           $ref: '#/components/schemas/Kernel'
 */

/**
 * @swagger
 * /api/jupyter/authenticate:
 *   post:
 *     summary: Authenticate user and start their Jupyter instance
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *                 example: "user123"
 *               password:
 *                 type: string
 *                 example: "dummy_password"
 *     responses:
 *       200:
 *         description: User authenticated and Jupyter instance started
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 serverURL:
 *                   type: string
 *                 username:
 *                   type: string
 *                 userId:
 *                   type: string
 *       500:
 *         description: Authentication failed
 */
router.post('/authenticate', authenticateUser);

/**
 * @swagger
 * /api/jupyter/status:
 *   get:
 *     summary: Check Jupyter server status for authenticated user
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Jupyter server status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/JupyterStatus'
 *       500:
 *         description: Connection error
 */
router.get('/status', getJupyterStatus);

/**
 * @swagger
 * /api/jupyter/kernelspecs:
 *   get:
 *     summary: List available kernel specifications
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of kernel specifications
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 kernelspecs:
 *                   type: object
 */
router.get('/kernelspecs', listKernelSpecs);

/**
 * @swagger
 * /api/jupyter/kernels:
 *   get:
 *     summary: List running kernels
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of running kernels
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Kernel'
 *   post:
 *     summary: Create a new kernel
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "python3"
 *     responses:
 *       201:
 *         description: Kernel created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Kernel'
 */
router.get('/kernels', listKernels);
router.post('/kernels', createKernel);

/**
 * @swagger
 * /api/jupyter/kernels/{kernelId}:
 *   get:
 *     summary: Get kernel information
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kernelId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Kernel information
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Kernel'
 *   delete:
 *     summary: Delete a kernel
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kernelId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       204:
 *         description: Kernel deleted successfully
 */
router.get('/kernels/:kernelId', getKernel);
router.delete('/kernels/:kernelId', deleteKernel);

/**
 * @swagger
 * /api/jupyter/kernels/{kernelId}/interrupt:
 *   post:
 *     summary: Interrupt a kernel
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kernelId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       204:
 *         description: Kernel interrupted successfully
 */
router.post('/kernels/:kernelId/interrupt', interruptKernel);

/**
 * @swagger
 * /api/jupyter/kernels/{kernelId}/restart:
 *   post:
 *     summary: Restart a kernel
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kernelId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Kernel restarted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Kernel'
 */
router.post('/kernels/:kernelId/restart', restartKernel);

/**
 * @swagger
 * /api/jupyter/kernels/{kernelId}/execute:
 *   post:
 *     summary: Execute code in a kernel
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kernelId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 example: "print('Hello, World!')"
 *     responses:
 *       200:
 *         description: Code execution result
 */
router.post('/kernels/:kernelId/execute', executeCode);

/**
 * @swagger
 * /api/jupyter/sessions:
 *   get:
 *     summary: List active sessions
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of active sessions
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Session'
 *   post:
 *     summary: Create a new session
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               path:
 *                 type: string
 *                 example: "notebook.ipynb"
 *               type:
 *                 type: string
 *                 example: "notebook"
 *               kernel:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     example: "python3"
 *     responses:
 *       201:
 *         description: Session created successfully
 */
router.get('/sessions', listSessions);
router.post('/sessions', createSession);

/**
 * @swagger
 * /api/jupyter/sessions/{sessionId}:
 *   get:
 *     summary: Get session information
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Session information
 *   delete:
 *     summary: Delete a session
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       204:
 *         description: Session deleted successfully
 */
router.get('/sessions/:sessionId', getSession);
router.delete('/sessions/:sessionId', deleteSession);

// Content management routes
router.get('/contents', listContents);
router.get('/contents/*', getContent);
router.post('/contents/*', createContent);
router.put('/contents/*', saveContent);
router.delete('/contents/*', deleteContent);

// Notebook specific routes
router.post('/notebooks', createNotebook);
router.put('/notebooks/*', saveNotebook);
router.get('/notebooks/*', getNotebook);

// Workspace management routes
/**
 * @swagger
 * /api/jupyter/workspace:
 *   post:
 *     summary: Create a complete Jupyter workspace for a user
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectId:
 *                 type: string
 *                 example: "project-123"
 *               kernelName:
 *                 type: string
 *                 example: "python3"
 *                 default: "python3"
 *     responses:
 *       201:
 *         description: Workspace created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 session:
 *                   $ref: '#/components/schemas/Session'
 *                 notebook_path:
 *                   type: string
 *                 kernel_id:
 *                   type: string
 *                 workspace_url:
 *                   type: string
 */
router.post('/workspace', createWorkspace);

/**
 * @swagger
 * /api/jupyter/workspaces:
 *   get:
 *     summary: Get user's active workspaces
 *     tags: [Jupyter]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of user's workspaces
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 workspaces:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Session'
 *                 count:
 *                   type: integer
 */
router.get('/workspaces', getUserWorkspaces);

export default router;
