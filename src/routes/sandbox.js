import express from 'express';
import {
  getSandboxSettings,
  createSandboxSettings,
  updateSandboxSettings,
  deleteSandboxSettings,
  getSandboxSettingsHistory
} from '../controllers/sandboxSettings.controller.js';
import { requirePermissions, requireRoles } from '../middlewares/rbac.js';
import { body, param, query } from 'express-validator';
import { validate } from '../middlewares/validation.js';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Sandbox
 *   description: Sandbox settings and configuration management
 */

/**
 * @swagger
 * /api/sandbox/settings/{projectId}:
 *   get:
 *     summary: Get sandbox settings for a project
 *     tags: [Sandbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID
 *     responses:
 *       200:
 *         description: Sandbox settings retrieved successfully
 *       404:
 *         description: Settings not found
 *       403:
 *         description: Access denied
 */
router.get('/settings/:projectId', [
  requirePermissions(['view_sandbox_settings']),
  param('projectId').isUUID()
], validate, getSandboxSettings);

/**
 * @swagger
 * /api/sandbox/settings:
 *   post:
 *     summary: Create sandbox settings for a project
 *     tags: [Sandbox]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *               - mode
 *             properties:
 *               projectId:
 *                 type: string
 *                 format: uuid
 *               mode:
 *                 type: string
 *                 enum: [uv, docker, kubernetes, local]
 *               cpuLimit:
 *                 type: string
 *                 example: "0.5"
 *               memLimit:
 *                 type: string
 *                 example: "1g"
 *               timeout:
 *                 type: integer
 *                 minimum: 60
 *                 maximum: 7200
 *               maxConcurrentUsers:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *               allowedPackages:
 *                 type: array
 *                 items:
 *                   type: string
 *               environmentVariables:
 *                 type: object
 *               volumeMounts:
 *                 type: array
 *                 items:
 *                   type: object
 *               networkPolicy:
 *                 type: string
 *                 enum: [restricted, open, custom]
 *     responses:
 *       201:
 *         description: Sandbox settings created successfully
 *       400:
 *         description: Validation error
 *       403:
 *         description: Access denied
 */
router.post('/settings', [
  requirePermissions(['create_sandbox_settings']),
  body('projectId').isUUID(),
  body('mode').isIn(['uv', 'docker', 'kubernetes', 'local']),
  body('cpuLimit').optional().isString(),
  body('memLimit').optional().isString(),
  body('timeout').optional().isInt({ min: 60, max: 7200 }),
  body('maxConcurrentUsers').optional().isInt({ min: 1, max: 100 }),
  body('allowedPackages').optional().isArray(),
  body('environmentVariables').optional().isObject(),
  body('volumeMounts').optional().isArray(),
  body('networkPolicy').optional().isIn(['restricted', 'open', 'custom'])
], validate, createSandboxSettings);

/**
 * @swagger
 * /api/sandbox/settings/{id}:
 *   put:
 *     summary: Update sandbox settings
 *     tags: [Sandbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mode:
 *                 type: string
 *                 enum: [uv, docker, kubernetes, local]
 *               cpuLimit:
 *                 type: string
 *               memLimit:
 *                 type: string
 *               timeout:
 *                 type: integer
 *                 minimum: 60
 *                 maximum: 7200
 *               maxConcurrentUsers:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *               allowedPackages:
 *                 type: array
 *                 items:
 *                   type: string
 *               environmentVariables:
 *                 type: object
 *               volumeMounts:
 *                 type: array
 *                 items:
 *                   type: object
 *               networkPolicy:
 *                 type: string
 *                 enum: [restricted, open, custom]
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Sandbox settings updated successfully
 *       404:
 *         description: Settings not found
 *       403:
 *         description: Access denied
 */
router.put('/settings/:id', [
  requirePermissions(['update_sandbox_settings']),
  param('id').isUUID(),
  body('mode').optional().isIn(['uv', 'docker', 'kubernetes', 'local']),
  body('cpuLimit').optional().isString(),
  body('memLimit').optional().isString(),
  body('timeout').optional().isInt({ min: 60, max: 7200 }),
  body('maxConcurrentUsers').optional().isInt({ min: 1, max: 100 }),
  body('allowedPackages').optional().isArray(),
  body('environmentVariables').optional().isObject(),
  body('volumeMounts').optional().isArray(),
  body('networkPolicy').optional().isIn(['restricted', 'open', 'custom']),
  body('isActive').optional().isBoolean()
], validate, updateSandboxSettings);

/**
 * @swagger
 * /api/sandbox/settings/{id}:
 *   delete:
 *     summary: Delete sandbox settings
 *     tags: [Sandbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Sandbox settings deleted successfully
 *       404:
 *         description: Settings not found
 *       403:
 *         description: Access denied
 */
router.delete('/settings/:id', [
  requirePermissions(['delete_sandbox_settings']),
  param('id').isUUID()
], validate, deleteSandboxSettings);

/**
 * @swagger
 * /api/sandbox/settings/{projectId}/history:
 *   get:
 *     summary: Get sandbox settings history for a project
 *     tags: [Sandbox]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *     responses:
 *       200:
 *         description: Settings history retrieved successfully
 *       403:
 *         description: Access denied
 */
router.get('/settings/:projectId/history', [
  requirePermissions(['view_sandbox_settings']),
  param('projectId').isUUID(),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt()
], validate, getSandboxSettingsHistory);

export default router;
