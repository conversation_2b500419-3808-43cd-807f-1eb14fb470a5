import express from 'express';
import {
  getProjects,
  getProjectById,
  createProject,
  updateProject,
  duplicateProject,
  deleteProject,
  publishProject,
  getProjectStatistics
} from '../controllers/projectController.js';
import { requirePermissions, requireRoles } from '../middlewares/rbac.js';
import { body, param, query } from 'express-validator';
import { validate } from '../middlewares/validation.js';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Projects
 *   description: Project management operations
 */

/**
 * @swagger
 * /api/projects:
 *   get:
 *     summary: Get all projects with pagination and filtering
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for title or description
 *       - in: query
 *         name: courseId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by course ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, published, archived]
 *         description: Filter by status
 *       - in: query
 *         name: difficultyLevel
 *         schema:
 *           type: string
 *           enum: [beginner, intermediate, advanced]
 *         description: Filter by difficulty level
 *     responses:
 *       200:
 *         description: Projects retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get('/', [
  requirePermissions(['view_projects']),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('search').optional().trim(),
  query('courseId').optional().isUUID(),
  query('status').optional().isIn(['draft', 'published', 'archived']),
  query('difficultyLevel').optional().isIn(['beginner', 'intermediate', 'advanced'])
], validate, getProjects);

/**
 * @swagger
 * /api/projects/{id}:
 *   get:
 *     summary: Get project by ID
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Project retrieved successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Access denied
 */
router.get('/:id', [
  param('id').isUUID()
], validate, getProjectById);

/**
 * @swagger
 * /api/projects:
 *   post:
 *     summary: Create new project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - courseId
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 200
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 5000
 *               courseId:
 *                 type: string
 *                 format: uuid
 *               difficultyLevel:
 *                 type: string
 *                 enum: [beginner, intermediate, advanced]
 *                 default: intermediate
 *               estimatedHours:
 *                 type: number
 *                 minimum: 0
 *               dueDate:
 *                 type: string
 *                 format: date-time
 *               instructions:
 *                 type: string
 *               requirements:
 *                 type: object
 *               resources:
 *                 type: object
 *               settings:
 *                 type: object
 *               rubrics:
 *                 type: array
 *                 items:
 *                   type: object
 *     responses:
 *       201:
 *         description: Project created successfully
 *       400:
 *         description: Validation error
 *       403:
 *         description: Insufficient permissions
 */
router.post('/', [
  requirePermissions(['create_projects']),
  body('title').isLength({ min: 2, max: 200 }).trim(),
  body('description').isLength({ min: 10, max: 5000 }).trim(),
  body('courseId').isUUID(),
  body('difficultyLevel').optional().isIn(['beginner', 'intermediate', 'advanced']),
  body('estimatedHours').optional().isFloat({ min: 0 }),
  body('dueDate').optional().isISO8601().toDate(),
  body('instructions').optional().isString(),
  body('requirements').optional().isObject(),
  body('resources').optional().isObject(),
  body('settings').optional().isObject(),
  body('rubrics').optional().isArray()
], validate, createProject);

/**
 * @swagger
 * /api/projects/{id}:
 *   put:
 *     summary: Update project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 200
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 5000
 *               difficultyLevel:
 *                 type: string
 *                 enum: [beginner, intermediate, advanced]
 *               estimatedHours:
 *                 type: number
 *                 minimum: 0
 *               dueDate:
 *                 type: string
 *                 format: date-time
 *               instructions:
 *                 type: string
 *               requirements:
 *                 type: object
 *               resources:
 *                 type: object
 *               settings:
 *                 type: object
 *               status:
 *                 type: string
 *                 enum: [draft, published, archived]
 *     responses:
 *       200:
 *         description: Project updated successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Insufficient permissions
 */
router.put('/:id', [
  requirePermissions(['edit_projects']),
  param('id').isUUID(),
  body('title').optional().isLength({ min: 2, max: 200 }).trim(),
  body('description').optional().isLength({ min: 10, max: 5000 }).trim(),
  body('difficultyLevel').optional().isIn(['beginner', 'intermediate', 'advanced']),
  body('estimatedHours').optional().isFloat({ min: 0 }),
  body('dueDate').optional().isISO8601().toDate(),
  body('instructions').optional().isString(),
  body('requirements').optional().isObject(),
  body('resources').optional().isObject(),
  body('settings').optional().isObject(),
  body('status').optional().isIn(['draft', 'published', 'archived'])
], validate, updateProject);

/**
 * @swagger
 * /api/projects/{id}/duplicate:
 *   post:
 *     summary: Duplicate project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - courseId
 *             properties:
 *               courseId:
 *                 type: string
 *                 format: uuid
 *               title:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 200
 *     responses:
 *       201:
 *         description: Project duplicated successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Insufficient permissions
 */
router.post('/:id/duplicate', [
  requirePermissions(['create_projects']),
  param('id').isUUID(),
  body('courseId').isUUID(),
  body('title').optional().isLength({ min: 2, max: 200 }).trim()
], validate, duplicateProject);

/**
 * @swagger
 * /api/projects/{id}/publish:
 *   post:
 *     summary: Publish project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Project published successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Insufficient permissions
 */
router.post('/:id/publish', [
  requirePermissions(['edit_projects']),
  param('id').isUUID()
], validate, publishProject);

/**
 * @swagger
 * /api/projects/{id}/statistics:
 *   get:
 *     summary: Get project statistics
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Insufficient permissions
 */
router.get('/:id/statistics', [
  requirePermissions(['view_projects']),
  param('id').isUUID()
], validate, getProjectStatistics);

/**
 * @swagger
 * /api/projects/{id}:
 *   delete:
 *     summary: Delete project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Project deleted successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Insufficient permissions
 *       409:
 *         description: Cannot delete project with submissions
 */
router.delete('/:id', [
  requirePermissions(['delete_projects']),
  param('id').isUUID()
], validate, deleteProject);

export default router;