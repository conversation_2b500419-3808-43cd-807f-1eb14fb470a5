'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    // LTI Platform Registrations table
    await queryInterface.createTable('lti_platforms', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      platform_id: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        comment: 'Platform identifier (iss claim)'
      },
      platform_name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      client_id: {
        type: Sequelize.STRING,
        allowNull: false
      },
      auth_login_url: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Platform authentication URL'
      },
      auth_token_url: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Platform token endpoint URL'
      },
      key_set_url: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Platform JWKS URL'
      },
      private_key: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Tool private key for this platform'
      },
      public_key: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Tool public key for this platform'
      },
      key_id: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Key identifier for JWKS'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      settings: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // LTI Deployments table
    await queryInterface.createTable('lti_deployments', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      platform_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'lti_platforms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      deployment_id: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'LTI deployment identifier'
      },
      deployment_name: {
        type: Sequelize.STRING,
        allowNull: true
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      settings: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // LTI Contexts table (course mappings)
    await queryInterface.createTable('lti_contexts', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      platform_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'lti_platforms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      deployment_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'lti_deployments',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      course_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'courses',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      context_id: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'LTI context identifier (course ID from LMS)'
      },
      context_type: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Type of context (Course, Group, etc.)'
      },
      context_title: {
        type: Sequelize.STRING,
        allowNull: true
      },
      context_label: {
        type: Sequelize.STRING,
        allowNull: true
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // LTI Resource Links table
    await queryInterface.createTable('lti_resource_links', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      platform_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'lti_platforms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      context_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'lti_contexts',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'projects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      resource_link_id: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'LTI resource link identifier'
      },
      resource_link_title: {
        type: Sequelize.STRING,
        allowNull: true
      },
      resource_link_description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      custom_parameters: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // LTI Line Items table (for grade passback)
    await queryInterface.createTable('lti_line_items', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      platform_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'lti_platforms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      context_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'lti_contexts',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      resource_link_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'lti_resource_links',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'projects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      line_item_id: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        comment: 'LTI line item identifier from AGS'
      },
      score_maximum: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 100.0
      },
      label: {
        type: Sequelize.STRING,
        allowNull: true
      },
      tag: {
        type: Sequelize.STRING,
        allowNull: true
      },
      resource_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      start_date_time: {
        type: Sequelize.DATE,
        allowNull: true
      },
      end_date_time: {
        type: Sequelize.DATE,
        allowNull: true
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // LTI Launch Sessions table
    await queryInterface.createTable('lti_launch_sessions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      platform_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'lti_platforms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      session_id: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      state: {
        type: Sequelize.STRING,
        allowNull: false
      },
      nonce: {
        type: Sequelize.STRING,
        allowNull: false
      },
      id_token: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      launch_data: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: false
      },
      is_used: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes for performance
    await queryInterface.addIndex('lti_platforms', ['platform_id']);
    await queryInterface.addIndex('lti_platforms', ['client_id']);

    await queryInterface.addIndex('lti_deployments', ['platform_id']);
    await queryInterface.addIndex('lti_deployments', ['deployment_id']);
    await queryInterface.addIndex(
      'lti_deployments',
      ['platform_id', 'deployment_id'],
      { unique: true }
    );

    await queryInterface.addIndex('lti_contexts', ['platform_id']);
    await queryInterface.addIndex('lti_contexts', ['context_id']);
    await queryInterface.addIndex('lti_contexts', ['course_id']);

    await queryInterface.addIndex('lti_resource_links', ['platform_id']);
    await queryInterface.addIndex('lti_resource_links', ['resource_link_id']);
    await queryInterface.addIndex('lti_resource_links', ['project_id']);

    await queryInterface.addIndex('lti_line_items', ['line_item_id']);
    await queryInterface.addIndex('lti_line_items', ['project_id']);
    await queryInterface.addIndex('lti_line_items', ['context_id']);

    await queryInterface.addIndex('lti_launch_sessions', ['session_id']);
    await queryInterface.addIndex('lti_launch_sessions', ['state']);
    await queryInterface.addIndex('lti_launch_sessions', ['expires_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('lti_launch_sessions');
    await queryInterface.dropTable('lti_line_items');
    await queryInterface.dropTable('lti_resource_links');
    await queryInterface.dropTable('lti_contexts');
    await queryInterface.dropTable('lti_deployments');
    await queryInterface.dropTable('lti_platforms');
  }
};
