import jwt from 'jsonwebtoken';
import { LtiLaunchSession, LtiPlatform } from '../models/ltiAssociations.js';
import { User } from '../models/associations.js';
import { asyncHandler } from './errorHandler.js';
import logger from '../config/logger.js';

/**
 * Middleware to authenticate LTI requests
 */
export const authenticateLTI = asyncHandler(async (req, res, next) => {
  let token;

  // Check for token in Authorization header
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  // Check for token in session (after successful LTI launch)
  else if (req.session?.ltiLaunchData) {
    token = req.session.ltiLaunchData.id_token;
  }
  // Check for token in body (for LTI launch requests)
  else if (req.body.id_token) {
    token = req.body.id_token;
  }

  if (!token) {
    return res.status(401).json({
      error: 'Access Denied',
      message: 'No valid LTI token provided'
    });
  }

  try {
    // For session-based authentication, user is already verified
    if (req.session?.user && req.session?.ltiLaunchData) {
      req.user = req.session.user;
      req.ltiContext = req.session.ltiContext;
      req.ltiResourceLink = req.session.ltiResourceLink;
      req.ltiLaunchData = req.session.ltiLaunchData;
      return next();
    }

    // For token-based authentication, verify the JWT
    // Note: This is a simplified verification - full implementation would
    // require proper platform key validation
    const decoded = jwt.decode(token, { complete: true });
    
    if (!decoded) {
      throw new Error('Invalid token format');
    }

    // Find the platform and verify the token
    const platform = await LtiPlatform.findOne({
      where: { platformId: decoded.payload.iss }
    });

    if (!platform) {
      throw new Error('Unknown platform');
    }

    // In a full implementation, verify the JWT signature here
    // const verified = jwt.verify(token, platform.publicKey);

    // For now, use the decoded payload
    const launchData = decoded.payload;

    // Find or create user based on LTI data
    const user = await findOrCreateLTIUser(launchData);

    // Set request context
    req.user = user;
    req.ltiLaunchData = launchData;
    req.ltiPlatform = platform;

    next();
  } catch (error) {
    logger.error('LTI authentication failed:', error);
    return res.status(401).json({
      error: 'Authentication Failed',
      message: 'Invalid or expired LTI token'
    });
  }
});

/**
 * Middleware to require specific LTI roles
 */
export const requireLTIRole = (allowedRoles) => {
  return asyncHandler(async (req, res, next) => {
    if (!req.ltiLaunchData) {
      return res.status(401).json({
        error: 'Access Denied',
        message: 'LTI context required'
      });
    }

    const userRoles = req.ltiLaunchData['https://purl.imsglobal.org/spec/lti/claim/roles'] || [];
    
    const hasRole = allowedRoles.some(role => 
      userRoles.some(userRole => userRole.includes(role))
    );

    if (!hasRole) {
      return res.status(403).json({
        error: 'Insufficient Permissions',
        message: 'Required LTI role not found'
      });
    }

    next();
  });
};

/**
 * Middleware to require LTI context (course)
 */
export const requireLTIContext = asyncHandler(async (req, res, next) => {
  if (!req.ltiContext) {
    return res.status(400).json({
      error: 'Missing Context',
      message: 'LTI context (course) required for this operation'
    });
  }

  next();
});

/**
 * Middleware to validate LTI message type
 */
export const validateLTIMessageType = (allowedTypes) => {
  return asyncHandler(async (req, res, next) => {
    if (!req.ltiLaunchData) {
      return res.status(400).json({
        error: 'Missing Launch Data',
        message: 'LTI launch data required'
      });
    }

    const messageType = req.ltiLaunchData['https://purl.imsglobal.org/spec/lti/claim/message_type'];
    
    if (!allowedTypes.includes(messageType)) {
      return res.status(400).json({
        error: 'Invalid Message Type',
        message: `Expected message type: ${allowedTypes.join(' or ')}`
      });
    }

    next();
  });
};

/**
 * Middleware to extract LTI custom parameters
 */
export const extractLTICustomParams = asyncHandler(async (req, res, next) => {
  if (req.ltiLaunchData) {
    const customClaim = req.ltiLaunchData['https://purl.imsglobal.org/spec/lti/claim/custom'];
    req.ltiCustomParams = customClaim || {};
  } else {
    req.ltiCustomParams = {};
  }

  next();
});

/**
 * Middleware to ensure grade passback capability
 */
export const requireGradePassback = asyncHandler(async (req, res, next) => {
  if (!req.ltiLaunchData) {
    return res.status(400).json({
      error: 'Missing Launch Data',
      message: 'LTI launch data required for grade passback'
    });
  }

  const agsClaim = req.ltiLaunchData['https://purl.imsglobal.org/spec/lti-ags/claim/endpoint'];
  
  if (!agsClaim || !agsClaim.scope || !agsClaim.scope.includes('https://purl.imsglobal.org/spec/lti-ags/scope/score')) {
    return res.status(400).json({
      error: 'Grade Passback Not Available',
      message: 'This LTI launch does not support grade passback'
    });
  }

  req.ltiAGSEndpoint = agsClaim;
  next();
});

/**
 * Helper function to find or create user from LTI data
 */
async function findOrCreateLTIUser(launchData) {
  const lmsUserId = launchData.sub;
  const email = launchData.email;
  const name = launchData.name || `${launchData.given_name || ''} ${launchData.family_name || ''}`.trim();

  const [user, created] = await User.findOrCreate({
    where: { lmsUserId },
    defaults: {
      name: name || email,
      email: email,
      lmsUserId: lmsUserId,
      status: 'active',
      profileData: {
        firstName: launchData.given_name,
        lastName: launchData.family_name,
        ltiRoles: launchData['https://purl.imsglobal.org/spec/lti/claim/roles']
      }
    }
  });

  if (!created && user.email !== email) {
    // Update email if it has changed
    await user.update({ 
      email: email,
      name: name || user.name
    });
  }

  return user;
}

/**
 * Middleware to validate LTI platform registration
 */
export const validatePlatform = asyncHandler(async (req, res, next) => {
  const { iss, client_id } = req.body;

  if (!iss || !client_id) {
    return res.status(400).json({
      error: 'Missing Parameters',
      message: 'Platform issuer (iss) and client_id are required'
    });
  }

  const platform = await LtiPlatform.findOne({
    where: { 
      platformId: iss, 
      clientId: client_id,
      isActive: true 
    }
  });

  if (!platform) {
    return res.status(400).json({
      error: 'Invalid Platform',
      message: 'Platform not registered or inactive'
    });
  }

  req.ltiPlatform = platform;
  next();
});

/**
 * Middleware to rate limit LTI requests
 */
export const rateLimitLTI = asyncHandler(async (req, res, next) => {
  // Simple rate limiting based on IP and platform
  const key = `lti_rate_limit:${req.ip}:${req.body.iss || 'unknown'}`;
  
  // In production, use Redis or similar for rate limiting
  // For now, this is a placeholder that always allows requests
  
  next();
});

/**
 * Middleware to log LTI activity
 */
export const logLTIActivity = asyncHandler(async (req, res, next) => {
  const activity = {
    method: req.method,
    path: req.path,
    platform: req.body.iss || req.ltiPlatform?.platformId,
    user: req.body.sub || req.user?.lmsUserId,
    timestamp: new Date(),
    ip: req.ip,
    userAgent: req.get('User-Agent')
  };

  logger.info('LTI Activity:', activity);
  
  next();
});