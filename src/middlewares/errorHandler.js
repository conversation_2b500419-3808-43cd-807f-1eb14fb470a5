import logger from '../config/logger.js';

export const errorHandler = (error, req, res, next) => {
  // Log the error
  logger.error('API Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    user: req.user?.email || 'anonymous'
  });

  // Default error response
  let statusCode = 500;
  let message = 'Internal server error';
  let details = null;

  // Handle different types of errors
  if (error.name === 'ValidationError') {
    // Sequelize validation errors
    statusCode = 400;
    message = 'Validation error';
    details = error.errors?.map(err => ({
      field: err.path,
      message: err.message,
      value: err.value
    }));
  } else if (error.name === 'SequelizeUniqueConstraintError') {
    // Unique constraint violations
    statusCode = 409;
    message = 'Resource already exists';
    details = error.errors?.map(err => ({
      field: err.path,
      message: `${err.path} must be unique`,
      value: err.value
    }));
  } else if (error.name === 'SequelizeForeignKeyConstraintError') {
    // Foreign key constraint violations
    statusCode = 400;
    message = 'Invalid reference to related resource';
  } else if (error.name === 'SequelizeConnectionError') {
    // Database connection errors
    statusCode = 503;
    message = 'Service temporarily unavailable';
    details = 'Database connection error';
  } else if (error.name === 'JsonWebTokenError') {
    // JWT errors
    statusCode = 401;
    message = 'Invalid authentication token';
  } else if (error.name === 'TokenExpiredError') {
    // JWT expiration
    statusCode = 401;
    message = 'Authentication token expired';
  } else if (error.name === 'MulterError') {
    // File upload errors
    statusCode = 400;
    if (error.code === 'LIMIT_FILE_SIZE') {
      message = 'File size too large';
    } else if (error.code === 'LIMIT_FILE_COUNT') {
      message = 'Too many files';
    } else {
      message = 'File upload error';
    }
  } else if (error.statusCode || error.status) {
    // Custom errors with status codes
    statusCode = error.statusCode || error.status;
    message = error.message;
  } else if (error.message) {
    // Other errors with messages
    message = error.message;
  }

  // Don't leak sensitive information in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const errorResponse = {
    error: message,
    status: statusCode,
    timestamp: new Date().toISOString(),
    path: req.url,
    method: req.method
  };

  // Add additional details in development or for specific error types
  if (isDevelopment || statusCode < 500) {
    if (details) {
      errorResponse.details = details;
    }
    
    if (isDevelopment && error.stack) {
      errorResponse.stack = error.stack;
    }
  }

  // Send error response
  res.status(statusCode).json(errorResponse);
};

// Custom error classes
export class ValidationError extends Error {
  constructor(message, details = null) {
    super(message);
    this.name = 'ValidationError';
    this.statusCode = 400;
    this.details = details;
  }
}

export class NotFoundError extends Error {
  constructor(message = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
    this.statusCode = 404;
  }
}

export class UnauthorizedError extends Error {
  constructor(message = 'Unauthorized access') {
    super(message);
    this.name = 'UnauthorizedError';
    this.statusCode = 401;
  }
}

export class ForbiddenError extends Error {
  constructor(message = 'Access forbidden') {
    super(message);
    this.name = 'ForbiddenError';
    this.statusCode = 403;
  }
}

export class ConflictError extends Error {
  constructor(message = 'Resource conflict') {
    super(message);
    this.name = 'ConflictError';
    this.statusCode = 409;
  }
}

export class BadRequestError extends Error {
  constructor(message = 'Bad request') {
    super(message);
    this.name = 'BadRequestError';
    this.statusCode = 400;
  }
}

// Express async error wrapper
export const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};