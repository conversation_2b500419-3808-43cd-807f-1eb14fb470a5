import logger from '../config/logger.js';

/**
 * RBAC Middleware - Checks if user has required permissions
 * @param {string|string[]} requiredPermissions - Permission key(s) required for access
 * @param {object} options - Additional options for permission checking
 * @returns {Function} Express middleware function
 */
export const requirePermissions = (requiredPermissions, options = {}) => {
  return (req, res, next) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User must be authenticated to access this resource'
        });
      }

      // Convert single permission to array
      const permissions = Array.isArray(requiredPermissions) 
        ? requiredPermissions 
        : [requiredPermissions];

      // Get user permissions from request (set by auth middleware)
      const userPermissions = req.userPermissions || [];
      const userRoles = req.userRoles || [];

      // Check for super admin role (bypass permission checks)
      if (userRoles.includes('super_admin') && !options.excludeSuperAdmin) {
        logger.debug(`Super admin ${req.user.email} bypassing permission check`);
        return next();
      }

      // Determine if we need ALL permissions or ANY permission
      const requireAll = options.requireAll !== false; // Default to true

      let hasAccess = false;

      if (requireAll) {
        // User must have ALL required permissions
        hasAccess = permissions.every(permission => 
          userPermissions.includes(permission)
        );
      } else {
        // User needs ANY of the required permissions
        hasAccess = permissions.some(permission => 
          userPermissions.includes(permission)
        );
      }

      if (!hasAccess) {
        logger.warn(`Access denied for user ${req.user.email}. Required: ${permissions.join(', ')}, Has: ${userPermissions.join(', ')}`);
        
        return res.status(403).json({
          error: 'Insufficient permissions',
          message: 'You do not have the required permissions to access this resource',
          required: permissions,
          userPermissions: userPermissions
        });
      }

      // Log successful permission check
      logger.debug(`Permission granted for user ${req.user.email}. Required: ${permissions.join(', ')}`);
      
      next();
    } catch (error) {
      logger.error('RBAC middleware error:', error);
      return res.status(500).json({
        error: 'Authorization error',
        message: 'Internal server error during authorization'
      });
    }
  };
};

/**
 * Check if user has specific role(s)
 * @param {string|string[]} requiredRoles - Role(s) required for access
 * @param {object} options - Additional options
 */
export const requireRoles = (requiredRoles, options = {}) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User must be authenticated to access this resource'
        });
      }

      const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
      const userRoles = req.userRoles || [];

      // Check for super admin role (bypass role checks)
      if (userRoles.includes('super_admin') && !options.excludeSuperAdmin) {
        return next();
      }

      const requireAll = options.requireAll !== false;
      let hasAccess = false;

      if (requireAll) {
        hasAccess = roles.every(role => userRoles.includes(role));
      } else {
        hasAccess = roles.some(role => userRoles.includes(role));
      }

      if (!hasAccess) {
        logger.warn(`Role access denied for user ${req.user.email}. Required: ${roles.join(', ')}, Has: ${userRoles.join(', ')}`);
        
        return res.status(403).json({
          error: 'Insufficient role privileges',
          message: 'You do not have the required role to access this resource',
          required: roles,
          userRoles: userRoles
        });
      }

      next();
    } catch (error) {
      logger.error('Role middleware error:', error);
      return res.status(500).json({
        error: 'Authorization error',
        message: 'Internal server error during role authorization'
      });
    }
  };
};

/**
 * Resource ownership middleware - checks if user owns or can access specific resource
 * @param {string} resourceType - Type of resource (e.g., 'submission', 'project')
 * @param {string} idParam - Request parameter containing resource ID
 * @param {object} options - Additional options
 */
export const requireResourceAccess = (resourceType, idParam = 'id', options = {}) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User must be authenticated to access this resource'
        });
      }

      const resourceId = req.params[idParam];
      const userId = req.user.id;
      const userRoles = req.userRoles || [];

      // Super admin and admin can access any resource
      if (userRoles.includes('super_admin') || userRoles.includes('admin')) {
        return next();
      }

      // Import models dynamically to avoid circular dependencies
      const { Submission, Project, Course, CourseEnrollment } = await import('../models/associations.js');

      let hasAccess = false;

      switch (resourceType) {
        case 'submission':
          // Check if user owns the submission OR is instructor of the course
          const submission = await Submission.findByPk(resourceId, {
            include: [{
              model: Project,
              as: 'project',
              include: [{
                model: Course,
                as: 'course'
              }]
            }]
          });

          if (!submission) {
            return res.status(404).json({
              error: 'Resource not found',
              message: 'Submission not found'
            });
          }

          // User owns the submission
          if (submission.user_id === userId) {
            hasAccess = true;
          }
          // User is instructor of the course
          else if (submission.project?.course?.instructor_id === userId) {
            hasAccess = true;
          }
          // User is enrolled as instructor/TA in the course
          else {
            const enrollment = await CourseEnrollment.findOne({
              where: {
                user_id: userId,
                course_id: submission.project?.course?.id,
                role_in_course: ['instructor', 'ta']
              }
            });
            hasAccess = !!enrollment;
          }
          break;

        case 'project':
          // Check if user created the project OR is enrolled in the course
          const project = await Project.findByPk(resourceId, {
            include: [{
              model: Course,
              as: 'course'
            }]
          });

          if (!project) {
            return res.status(404).json({
              error: 'Resource not found',
              message: 'Project not found'
            });
          }

          // User created the project
          if (project.created_by === userId) {
            hasAccess = true;
          }
          // User is instructor of the course
          else if (project.course?.instructor_id === userId) {
            hasAccess = true;
          }
          // User is enrolled in the course
          else {
            const enrollment = await CourseEnrollment.findOne({
              where: {
                user_id: userId,
                course_id: project.course_id
              }
            });
            hasAccess = !!enrollment;
          }
          break;

        case 'course':
          // Check if user is instructor or enrolled in the course
          const course = await Course.findByPk(resourceId);
          
          if (!course) {
            return res.status(404).json({
              error: 'Resource not found',
              message: 'Course not found'
            });
          }

          // User is instructor of the course
          if (course.instructor_id === userId) {
            hasAccess = true;
          }
          // User is enrolled in the course
          else {
            const enrollment = await CourseEnrollment.findOne({
              where: {
                user_id: userId,
                course_id: resourceId
              }
            });
            hasAccess = !!enrollment;
          }
          break;

        default:
          logger.error(`Unknown resource type: ${resourceType}`);
          return res.status(500).json({
            error: 'Invalid resource type',
            message: 'Unknown resource type for access control'
          });
      }

      if (!hasAccess) {
        logger.warn(`Resource access denied for user ${req.user.email} on ${resourceType} ${resourceId}`);
        return res.status(403).json({
          error: 'Access denied',
          message: 'You do not have permission to access this resource'
        });
      }

      next();
    } catch (error) {
      logger.error('Resource access middleware error:', error);
      return res.status(500).json({
        error: 'Authorization error',
        message: 'Internal server error during resource access check'
      });
    }
  };
};

/**
 * Utility function to check permissions in controllers
 */
export const hasPermission = (userPermissions, requiredPermission) => {
  return userPermissions.includes(requiredPermission) || userPermissions.includes('super_admin');
};

/**
 * Utility function to check roles in controllers
 */
export const hasRole = (userRoles, requiredRole) => {
  return userRoles.includes(requiredRole) || userRoles.includes('super_admin');
};