import jwt from 'jsonwebtoken';
import { User, Role, Permission } from '../models/associations.js';
import logger from '../config/logger.js';
import tokenBlacklistService from '../services/tokenBlacklistService.js';

// JWT Authentication Middleware
export const jwtMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'No token provided or invalid format'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
      // Check if token is blacklisted
      const isBlacklisted =
        await tokenBlacklistService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        return res.status(401).json({
          error: 'Token invalidated',
          message: 'This token has been revoked. Please login again.'
        });
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Fetch user with roles and permissions
      const user = await User.findByPk(decoded.userId, {
        include: [
          {
            model: Role,
            as: 'roles',
            include: [
              {
                model: Permission,
                as: 'permissions'
              }
            ]
          }
        ],
        attributes: { exclude: ['password_hash'] }
      });

      if (!user) {
        return res.status(401).json({
          error: 'Access denied',
          message: 'User not found'
        });
      }

      if (user.status !== 'active') {
        return res.status(401).json({
          error: 'Access denied',
          message: 'User account is not active'
        });
      }

      // Extract user permissions for RBAC
      const userPermissions = [];
      const userRoles = [];

      user.roles?.forEach(role => {
        userRoles.push(role.name);
        role.permissions?.forEach(permission => {
          if (!userPermissions.includes(permission.key)) {
            userPermissions.push(permission.key);
          }
        });
      });

      // Attach user info to request
      req.user = user;
      req.userPermissions = userPermissions;
      req.userRoles = userRoles;
      req.primaryRole = userRoles[0] || 'student'; // Assume first role is primary

      // Update last login timestamp (optional, might impact performance)
      // user.updateLastLogin();

      next();
    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          error: 'Token expired',
          message: 'Please login again'
        });
      } else if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({
          error: 'Invalid token',
          message: 'Token is malformed'
        });
      } else {
        throw jwtError;
      }
    }
  } catch (error) {
    logger.error('JWT middleware error:', error);
    return res.status(500).json({
      error: 'Authentication error',
      message: 'Internal server error during authentication'
    });
  }
};

// Optional middleware for endpoints that don't require authentication
export const optionalJwtMiddleware = async (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    req.user = null;
    req.userPermissions = [];
    req.userRoles = [];
    return next();
  }

  // If token is provided, validate it
  return jwtMiddleware(req, res, next);
};

// Generate JWT token
export const generateToken = user => {
  const payload = {
    userId: user.id,
    email: user.email,
    name: user.name
  };

  const options = {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    issuer: process.env.JWT_ISSUER || 'bits-dataScience-platform',
    audience: process.env.JWT_AUDIENCE || 'bits-platform-users'
  };

  return jwt.sign(payload, process.env.JWT_SECRET, options);
};

// Verify token utility
export const verifyTokenUtil = token => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    return null;
  }
};

// Export jwtMiddleware as verifyToken for backward compatibility
export const verifyToken = jwtMiddleware;
