import axios from 'axios';
import logger from '../config/logger.js';
import jupyterService from '../services/jupyterService.js';

// Jupyter server configuration
const JUPYTER_BASE_URL =
  process.env.JUPYTER_SERVER_URL || 'http://localhost:8888';
const JUPYTER_TOKEN = process.env.JUPYTER_AUTH_TOKEN;

// Create axios instance with default configuration
const jupyterApi = axios.create({
  baseURL: JUPYTER_BASE_URL,
  timeout: 30000,
  headers: {
    Authorization: `token ${JUPYTER_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for logging
jupyterApi.interceptors.request.use(
  config => {
    logger.info(
      `Jupyter API Request: ${config.method?.toUpperCase()} ${config.url}`
    );
    return config;
  },
  error => {
    logger.error('Jupyter API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging and error handling
jupyterApi.interceptors.response.use(
  response => {
    logger.info(
      `Jupyter API Response: ${response.status} ${response.config.url}`
    );
    return response;
  },
  error => {
    logger.error('Jupyter API Response Error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method
    });
    return Promise.reject(error);
  }
);

// Helper function to handle Jupyter API errors
const handleJupyterError = (error, res, operation) => {
  logger.error(`Jupyter ${operation} error:`, {
    message: error.message,
    status: error.response?.status,
    data: error.response?.data
  });

  if (error.response) {
    // Jupyter server responded with an error
    return res.status(error.response.status).json({
      error: `Jupyter ${operation} failed`,
      message: error.response.data?.message || error.message,
      details: error.response.data
    });
  } else if (error.request) {
    // Request was made but no response received
    return res.status(503).json({
      error: 'Jupyter server unavailable',
      message: 'Unable to connect to Jupyter server',
      operation
    });
  } else {
    // Something else happened
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      operation
    });
  }
};

// Check Jupyter server status
export const getJupyterStatus = async (req, res) => {
  try {
    const status = await jupyterService.testConnection();

    if (status.connected) {
      res.json({
        status: 'connected',
        jupyter_version: status.version,
        server_url: status.server_url,
        authenticated: true
      });
    } else {
      res.status(503).json({
        status: 'disconnected',
        error: status.error,
        server_url: JUPYTER_BASE_URL
      });
    }
  } catch (error) {
    handleJupyterError(error, res, 'status check');
  }
};

// List available kernel specifications
export const listKernelSpecs = async (req, res) => {
  try {
    const response = await jupyterApi.get('/api/kernelspecs');
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'kernelspecs listing');
  }
};

// List running kernels
export const listKernels = async (req, res) => {
  try {
    const response = await jupyterApi.get('/api/kernels');
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'kernels listing');
  }
};

// Create a new kernel
export const createKernel = async (req, res) => {
  try {
    const { name = 'python3' } = req.body;
    const response = await jupyterApi.post('/api/kernels', { name });
    res.status(201).json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'kernel creation');
  }
};

// Get kernel information
export const getKernel = async (req, res) => {
  try {
    const { kernelId } = req.params;
    const response = await jupyterApi.get(`/api/kernels/${kernelId}`);
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'kernel retrieval');
  }
};

// Delete a kernel
export const deleteKernel = async (req, res) => {
  try {
    const { kernelId } = req.params;
    await jupyterApi.delete(`/api/kernels/${kernelId}`);
    res.status(204).send();
  } catch (error) {
    handleJupyterError(error, res, 'kernel deletion');
  }
};

// Interrupt a kernel
export const interruptKernel = async (req, res) => {
  try {
    const { kernelId } = req.params;
    await jupyterApi.post(`/api/kernels/${kernelId}/interrupt`);
    res.status(204).send();
  } catch (error) {
    handleJupyterError(error, res, 'kernel interruption');
  }
};

// Restart a kernel
export const restartKernel = async (req, res) => {
  try {
    const { kernelId } = req.params;
    const response = await jupyterApi.post(`/api/kernels/${kernelId}/restart`);
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'kernel restart');
  }
};

// Execute code in a kernel with real-time WebSocket support
export const executeCode = async (req, res) => {
  try {
    const { kernelId } = req.params;
    const { code, timeout = 30000 } = req.body;

    if (!code) {
      return res.status(400).json({
        error: 'Bad request',
        message: 'Code is required'
      });
    }

    // Use the Jupyter service for real-time code execution
    const options = {
      username: req.user?.username || 'anonymous',
      session: req.user?.id ? `user_${req.user.id}` : `session_${Date.now()}`,
      timeout,
      silent: req.body.silent || false,
      store_history: req.body.store_history !== false,
      user_expressions: req.body.user_expressions || {},
      allow_stdin: req.body.allow_stdin || false,
      stop_on_error: req.body.stop_on_error !== false
    };

    const result = await jupyterService.executeCode(kernelId, code, options);

    res.json({
      success: true,
      kernel_id: kernelId,
      execution_count: result.execution_count,
      status: result.status,
      outputs: result.outputs,
      error: result.error || null
    });
  } catch (error) {
    handleJupyterError(error, res, 'code execution');
  }
};

// List active sessions
export const listSessions = async (req, res) => {
  try {
    const response = await jupyterApi.get('/api/sessions');
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'sessions listing');
  }
};

// Create a new session
export const createSession = async (req, res) => {
  try {
    const { path, type = 'notebook', kernel = { name: 'python3' } } = req.body;

    if (!path) {
      return res.status(400).json({
        error: 'Bad request',
        message: 'Path is required'
      });
    }

    const response = await jupyterApi.post('/api/sessions', {
      path,
      type,
      kernel
    });

    res.status(201).json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'session creation');
  }
};

// Get session information
export const getSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const response = await jupyterApi.get(`/api/sessions/${sessionId}`);
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'session retrieval');
  }
};

// Delete a session
export const deleteSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    await jupyterApi.delete(`/api/sessions/${sessionId}`);
    res.status(204).send();
  } catch (error) {
    handleJupyterError(error, res, 'session deletion');
  }
};

// List contents
export const listContents = async (req, res) => {
  try {
    const { path = '' } = req.query;
    const response = await jupyterApi.get(`/api/contents/${path}`);
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'contents listing');
  }
};

// Get content
export const getContent = async (req, res) => {
  try {
    const path = req.params[0] || '';
    const response = await jupyterApi.get(`/api/contents/${path}`);
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'content retrieval');
  }
};

// Create content
export const createContent = async (req, res) => {
  try {
    const path = req.params[0] || '';
    const response = await jupyterApi.post(`/api/contents/${path}`, req.body);
    res.status(201).json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'content creation');
  }
};

// Save content
export const saveContent = async (req, res) => {
  try {
    const path = req.params[0] || '';
    const response = await jupyterApi.put(`/api/contents/${path}`, req.body);
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'content saving');
  }
};

// Delete content
export const deleteContent = async (req, res) => {
  try {
    const path = req.params[0] || '';
    await jupyterApi.delete(`/api/contents/${path}`);
    res.status(204).send();
  } catch (error) {
    handleJupyterError(error, res, 'content deletion');
  }
};

// Create notebook
export const createNotebook = async (req, res) => {
  try {
    const { path, name } = req.body;

    if (!path || !name) {
      return res.status(400).json({
        error: 'Bad request',
        message: 'Path and name are required'
      });
    }

    const notebookContent = {
      type: 'notebook',
      content: {
        cells: [],
        metadata: {
          kernelspec: {
            display_name: 'Python 3',
            language: 'python',
            name: 'python3'
          },
          language_info: {
            name: 'python',
            version: '3.8.0'
          }
        },
        nbformat: 4,
        nbformat_minor: 4
      }
    };

    const response = await jupyterApi.put(
      `/api/contents/${path}/${name}`,
      notebookContent
    );
    res.status(201).json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'notebook creation');
  }
};

// Save notebook
export const saveNotebook = async (req, res) => {
  try {
    const path = req.params[0] || '';
    const response = await jupyterApi.put(`/api/contents/${path}`, req.body);
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'notebook saving');
  }
};

// Get notebook
export const getNotebook = async (req, res) => {
  try {
    const path = req.params[0] || '';
    const response = await jupyterApi.get(`/api/contents/${path}`);
    res.json(response.data);
  } catch (error) {
    handleJupyterError(error, res, 'notebook retrieval');
  }
};

// Create a complete Jupyter workspace for a user
export const createWorkspace = async (req, res) => {
  try {
    const { projectId, kernelName = 'python3' } = req.body;
    const userId = req.user?.id;

    if (!userId || !projectId) {
      return res.status(400).json({
        error: 'Bad request',
        message: 'User ID and Project ID are required'
      });
    }

    // Create a session for the user's project
    const sessionPath = `projects/${projectId}/workspace_${userId}.ipynb`;
    const session = await jupyterService.createSession(sessionPath, kernelName);

    // Create a default notebook if it doesn't exist
    const notebookContent = {
      type: 'notebook',
      content: {
        cells: [
          {
            cell_type: 'markdown',
            metadata: {},
            source: [
              `# Project ${projectId} Workspace\n`,
              `Welcome to your Jupyter workspace!`
            ]
          },
          {
            cell_type: 'code',
            execution_count: null,
            metadata: {},
            outputs: [],
            source: ['# Your code here\nprint("Hello, World!")']
          }
        ],
        metadata: {
          kernelspec: {
            display_name: 'Python 3',
            language: 'python',
            name: 'python3'
          },
          language_info: {
            name: 'python',
            version: '3.8.0'
          }
        },
        nbformat: 4,
        nbformat_minor: 4
      }
    };

    try {
      await jupyterApi.put(`/api/contents/${sessionPath}`, notebookContent);
    } catch (notebookError) {
      // Notebook might already exist, which is fine
      logger.info(`Notebook ${sessionPath} might already exist`);
    }

    res.status(201).json({
      success: true,
      session,
      notebook_path: sessionPath,
      kernel_id: session.kernel.id,
      workspace_url: `${JUPYTER_BASE_URL}/notebooks/${sessionPath}?token=${JUPYTER_TOKEN}`
    });
  } catch (error) {
    handleJupyterError(error, res, 'workspace creation');
  }
};

// Get user's active workspaces
export const getUserWorkspaces = async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({
        error: 'Bad request',
        message: 'User ID is required'
      });
    }

    // Get all sessions
    const sessions = await jupyterService.listSessions();

    // Filter sessions for this user
    const userSessions = sessions.filter(session =>
      session.path.includes(`workspace_${userId}`)
    );

    res.json({
      success: true,
      workspaces: userSessions,
      count: userSessions.length
    });
  } catch (error) {
    handleJupyterError(error, res, 'workspace listing');
  }
};
