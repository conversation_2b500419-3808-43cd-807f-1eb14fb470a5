import { Course, User, CourseEnrollment, Project } from '../models/associations.js';
import { asyncHandler } from '../middlewares/errorHandler.js';
import { Op, fn, col } from 'sequelize';
import logger from '../config/logger.js';

/**
 * @desc    Get all courses for current user
 * @route   GET /api/courses
 * @access  Private
 */
export const getCourses = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    search,
    term,
    status,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};
  
  // Add search functionality
  if (search) {
    whereClause[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { code: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Filter by term
  if (term) {
    whereClause.term = term;
  }

  // Filter by status
  if (status) {
    whereClause.status = status;
  }

  let courseQuery = {
    where: whereClause,
    include: [
      {
        model: User,
        as: 'instructor',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Project,
        as: 'projects',
        attributes: ['id', 'title', 'status'],
        separate: true,
        limit: 5,
        order: [['created_at', 'DESC']]
      }
    ],
    limit: parseInt(limit),
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]]
  };

  // For students and TAs, only show courses they're enrolled in
  if (!req.userRoles.includes('admin') && !req.userRoles.includes('instructor')) {
    courseQuery.include.push({
      model: CourseEnrollment,
      as: 'enrollments',
      where: { user_id: req.user.id },
      required: true
    });
  }

  const { count, rows: courses } = await Course.findAndCountAll(courseQuery);

  // Transform course data for response
  const transformedCourses = await Promise.all(courses.map(async course => {
    // Get enrollment count
    const enrollmentCount = await CourseEnrollment.count({
      where: { course_id: course.id, enrollment_status: 'active' }
    });

    // Get user's enrollment if exists
    let userEnrollment = null;
    if (!req.userRoles.includes('admin')) {
      userEnrollment = await CourseEnrollment.findOne({
        where: { course_id: course.id, user_id: req.user.id }
      });
    }

    return {
      id: course.id,
      name: course.name,
      code: course.code,
      description: course.description,
      term: course.term,
      academicYear: course.academic_year,
      status: course.status,
      startDate: course.start_date,
      endDate: course.end_date,
      enrollmentCount,
      instructor: course.instructor ? {
        id: course.instructor.id,
        name: course.instructor.name,
        email: course.instructor.email
      } : null,
      recentProjects: course.projects?.map(project => ({
        id: project.id,
        title: project.title,
        status: project.status
      })) || [],
      userEnrollment: userEnrollment ? {
        roleInCourse: userEnrollment.role_in_course,
        enrollmentStatus: userEnrollment.enrollment_status,
        enrolledAt: userEnrollment.enrolled_at
      } : null,
      createdAt: course.created_at
    };
  }));

  res.json({
    success: true,
    data: {
      courses: transformedCourses,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / parseInt(limit)),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * @desc    Get course by ID
 * @route   GET /api/courses/:id
 * @access  Private
 */
export const getCourseById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const course = await Course.findByPk(id, {
    include: [
      {
        model: User,
        as: 'instructor',
        attributes: ['id', 'name', 'email', 'profile_picture']
      },
      {
        model: Project,
        as: 'projects',
        include: [{
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'email']
        }]
      },
      {
        model: CourseEnrollment,
        as: 'enrollments',
        include: [{
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'profile_picture']
        }]
      }
    ]
  });

  if (!course) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Course not found'
    });
  }

  // Check if user has access to this course
  const hasAccess = req.userRoles.includes('admin') || 
                   course.instructor_id === req.user.id ||
                   course.enrollments.some(enrollment => enrollment.user_id === req.user.id);

  if (!hasAccess) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view this course'
    });
  }

  // Get user's enrollment if exists
  const userEnrollment = course.enrollments.find(enrollment => 
    enrollment.user_id === req.user.id
  );

  const courseResponse = {
    id: course.id,
    name: course.name,
    code: course.code,
    description: course.description,
    term: course.term,
    academicYear: course.academic_year,
    status: course.status,
    startDate: course.start_date,
    endDate: course.end_date,
    settings: course.settings,
    instructor: course.instructor ? {
      id: course.instructor.id,
      name: course.instructor.name,
      email: course.instructor.email,
      profilePicture: course.instructor.profile_picture
    } : null,
    projects: course.projects?.map(project => ({
      id: project.id,
      title: project.title,
      description: project.description,
      status: project.status,
      dueDate: project.due_date,
      difficultyLevel: project.difficulty_level,
      estimatedHours: project.estimated_hours,
      creator: {
        id: project.creator.id,
        name: project.creator.name,
        email: project.creator.email
      },
      createdAt: project.created_at
    })) || [],
    enrollments: course.enrollments?.map(enrollment => ({
      id: enrollment.id,
      roleInCourse: enrollment.role_in_course,
      enrollmentStatus: enrollment.enrollment_status,
      enrolledAt: enrollment.enrolled_at,
      user: {
        id: enrollment.user.id,
        name: enrollment.user.name,
        email: enrollment.user.email,
        profilePicture: enrollment.user.profile_picture
      }
    })) || [],
    userEnrollment: userEnrollment ? {
      roleInCourse: userEnrollment.role_in_course,
      enrollmentStatus: userEnrollment.enrollment_status,
      enrolledAt: userEnrollment.enrolled_at
    } : null,
    createdAt: course.created_at,
    updatedAt: course.updated_at
  };

  res.json({
    success: true,
    course: courseResponse
  });
});

/**
 * @desc    Get course enrollments
 * @route   GET /api/courses/:id/enrollments
 * @access  Private (Instructor, Admin)
 */
export const getCourseEnrollments = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    page = 1,
    limit = 20,
    search,
    role,
    status,
    sortBy = 'enrolled_at',
    sortOrder = 'desc'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);

  const course = await Course.findByPk(id);
  if (!course) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Course not found'
    });
  }

  // Check if user has access to view enrollments
  const hasAccess = req.userRoles.includes('admin') || 
                   course.instructor_id === req.user.id;

  if (!hasAccess) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view course enrollments'
    });
  }

  const whereClause = { course_id: id };
  
  // Filter by role in course
  if (role) {
    whereClause.role_in_course = role;
  }

  // Filter by enrollment status
  if (status) {
    whereClause.enrollment_status = status;
  }

  const includeClause = [{
    model: User,
    as: 'user',
    attributes: ['id', 'name', 'email', 'profile_picture', 'last_login']
  }];

  // Add search functionality
  if (search) {
    includeClause[0].where = {
      [Op.or]: [
        { name: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ]
    };
  }

  const { count, rows: enrollments } = await CourseEnrollment.findAndCountAll({
    where: whereClause,
    include: includeClause,
    limit: parseInt(limit),
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]]
  });

  const transformedEnrollments = enrollments.map(enrollment => ({
    id: enrollment.id,
    roleInCourse: enrollment.role_in_course,
    enrollmentStatus: enrollment.enrollment_status,
    enrolledAt: enrollment.enrolled_at,
    droppedAt: enrollment.dropped_at,
    finalGrade: enrollment.final_grade,
    user: {
      id: enrollment.user.id,
      name: enrollment.user.name,
      email: enrollment.user.email,
      profilePicture: enrollment.user.profile_picture,
      lastLogin: enrollment.user.last_login
    }
  }));

  res.json({
    success: true,
    data: {
      enrollments: transformedEnrollments,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / parseInt(limit)),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * @desc    Enroll user in course (Admin only)
 * @route   POST /api/courses/:id/enrollments
 * @access  Private (Admin only)
 */
export const enrollUserInCourse = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { userId, roleInCourse = 'student' } = req.body;

  if (!userId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'User ID is required'
    });
  }

  const course = await Course.findByPk(id);
  const user = await User.findByPk(userId);

  if (!course) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Course not found'
    });
  }

  if (!user) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'User not found'
    });
  }

  // Check if user is already enrolled
  const existingEnrollment = await CourseEnrollment.findOne({
    where: { course_id: id, user_id: userId }
  });

  if (existingEnrollment) {
    return res.status(409).json({
      error: 'Conflict',
      message: 'User is already enrolled in this course'
    });
  }

  // Create enrollment
  const enrollment = await CourseEnrollment.create({
    course_id: id,
    user_id: userId,
    role_in_course: roleInCourse,
    enrollment_status: 'active'
  });

  logger.info(`User enrolled: ${user.email} in course ${course.name} by ${req.user.email}`);

  res.status(201).json({
    success: true,
    message: 'User enrolled successfully',
    enrollment: {
      id: enrollment.id,
      roleInCourse: enrollment.role_in_course,
      enrollmentStatus: enrollment.enrollment_status,
      enrolledAt: enrollment.enrolled_at
    }
  });
});

/**
 * @desc    Update course enrollment
 * @route   PUT /api/courses/:id/enrollments/:enrollmentId
 * @access  Private (Admin, Instructor)
 */
export const updateCourseEnrollment = asyncHandler(async (req, res) => {
  const { id, enrollmentId } = req.params;
  const { roleInCourse, enrollmentStatus, finalGrade } = req.body;

  const enrollment = await CourseEnrollment.findOne({
    where: { id: enrollmentId, course_id: id },
    include: [
      { model: User, as: 'user', attributes: ['id', 'name', 'email'] },
      { model: Course, as: 'course', attributes: ['id', 'name', 'instructor_id'] }
    ]
  });

  if (!enrollment) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Enrollment not found'
    });
  }

  // Check if user has permission to update enrollment
  const hasAccess = req.userRoles.includes('admin') || 
                   enrollment.course.instructor_id === req.user.id;

  if (!hasAccess) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to update this enrollment'
    });
  }

  // Update enrollment
  const updateData = {};
  if (roleInCourse) updateData.role_in_course = roleInCourse;
  if (enrollmentStatus) updateData.enrollment_status = enrollmentStatus;
  if (finalGrade !== undefined) updateData.final_grade = finalGrade;

  await enrollment.update(updateData);

  logger.info(`Enrollment updated: ${enrollment.user.email} in course ${enrollment.course.name} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Enrollment updated successfully',
    enrollment: {
      id: enrollment.id,
      roleInCourse: enrollment.role_in_course,
      enrollmentStatus: enrollment.enrollment_status,
      finalGrade: enrollment.final_grade,
      enrolledAt: enrollment.enrolled_at
    }
  });
});

/**
 * @desc    Get course statistics
 * @route   GET /api/courses/:id/statistics
 * @access  Private (Instructor, Admin)
 */
export const getCourseStatistics = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const course = await Course.findByPk(id);
  if (!course) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Course not found'
    });
  }

  // Check if user has access
  const hasAccess = req.userRoles.includes('admin') || 
                   course.instructor_id === req.user.id;

  if (!hasAccess) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view course statistics'
    });
  }

  // Get enrollment statistics
  const totalEnrollments = await CourseEnrollment.count({
    where: { course_id: id }
  });

  const activeEnrollments = await CourseEnrollment.count({
    where: { course_id: id, enrollment_status: 'active' }
  });

  const enrollmentsByRole = await CourseEnrollment.findAll({
    where: { course_id: id },
    attributes: [
      'role_in_course',
      [fn('COUNT', col('id')), 'count']
    ],
    group: ['role_in_course']
  });

  // Get project statistics
  const totalProjects = await Project.count({
    where: { course_id: id }
  });

  const publishedProjects = await Project.count({
    where: { course_id: id, status: 'published' }
  });

  res.json({
    success: true,
    statistics: {
      enrollments: {
        total: totalEnrollments,
        active: activeEnrollments,
        byRole: enrollmentsByRole.map(item => ({
          role: item.role_in_course,
          count: parseInt(item.getDataValue('count'))
        }))
      },
      projects: {
        total: totalProjects,
        published: publishedProjects
      }
    }
  });
});