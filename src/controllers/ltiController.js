import { asyncHand<PERSON> } from '../middlewares/errorHandler.js';
import ltiService from '../services/ltiService.js';
import {
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem
} from '../models/ltiAssociations.js';
import { User, Course, Project, Grade, Submission } from '../models/associations.js';
import logger from '../config/logger.js';

/**
 * @desc    OIDC Login Initiation endpoint (Step 1)
 * @route   GET/POST /api/lti/oidc/init
 * @access  Public
 */
export const oidcInit = asyncHandler(async (req, res) => {
  const {
    iss,
    login_hint,
    target_link_uri,
    lti_message_hint,
    client_id
  } = req.method === 'GET' ? req.query : req.body;

  // Validate required parameters
  if (!iss || !login_hint || !target_link_uri || !client_id) {
    return res.status(400).json({
      error: 'Missing Required Parameters',
      message: 'iss, login_hint, target_link_uri, and client_id are required',
      required: ['iss', 'login_hint', 'target_link_uri', 'client_id'],
      received: { iss, login_hint, target_link_uri, client_id }
    });
  }

  try {
    // Generate OIDC login request
    const loginResult = await ltiService.generateOIDCLogin(
      iss,
      login_hint,
      target_link_uri,
      lti_message_hint,
      client_id
    );

    logger.info(`OIDC login initiated for issuer: ${iss}, client: ${client_id}`);

    // Redirect to platform's authorization endpoint
    res.redirect(loginResult.authUrl);

  } catch (error) {
    logger.error('OIDC login initiation failed:', error);
    
    // Return user-friendly error page
    res.status(400).send(`
      <html>
        <head><title>LTI Setup Error</title></head>
        <body>
          <h1>LTI Configuration Error</h1>
          <p>The LTI tool is not properly configured for this platform.</p>
          <p>Error: ${error.message}</p>
          <p>Please contact your system administrator.</p>
        </body>
      </html>
    `);
  }
});

/**
 * @desc    OIDC Callback endpoint (Step 2)
 * @route   POST /api/lti/oidc/callback
 * @access  Public
 */
export const oidcCallback = asyncHandler(async (req, res) => {
  const { code, state } = req.body;

  if (!code || !state) {
    return res.status(400).json({
      error: 'Missing Parameters',
      message: 'code and state are required'
    });
  }

  try {
    // Exchange code for ID token and process launch
    const launchResult = await ltiService.handleOIDCCallback(code, state);
    
    // Create session for the user
    req.session.user = launchResult.user;
    req.session.ltiContext = launchResult.context;
    req.session.ltiResourceLink = launchResult.resourceLink;
    req.session.ltiLaunchData = launchResult.launchData;
    req.session.ltiSessionId = launchResult.sessionId;

    logger.info(`OIDC callback successful for user: ${launchResult.user.email}`);

    // Determine the target page based on launch data
    const targetPage = determineLaunchTarget(launchResult.launchData, launchResult.resourceLink);
    
    // Redirect to the appropriate page in the application
    const redirectUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/${targetPage}`;
    
    res.redirect(redirectUrl);
    
  } catch (error) {
    logger.error('OIDC callback failed:', error);
    
    res.status(400).send(`
      <html>
        <head><title>LTI Launch Error</title></head>
        <body>
          <h1>LTI Launch Failed</h1>
          <p>There was an error processing your LTI launch request.</p>
          <p>Error: ${error.message}</p>
          <p>Please try again or contact your instructor.</p>
        </body>
      </html>
    `);
  }
});

/**
 * @desc    JWKS endpoint for tool public keys
 * @route   GET /.well-known/jwks.json
 * @access  Public
 */
export const getJWKS = asyncHandler(async (req, res) => {
  try {
    const jwks = ltiService.generateJWKS();
    res.json(jwks);
  } catch (error) {
    logger.error('JWKS generation failed:', error);
    res.status(500).json({
      error: 'JWKS Generation Failed',
      message: 'Could not generate public key set'
    });
  }
});

/**
 * @desc    Deep linking endpoint for content selection
 * @route   POST /api/lti/deep-linking
 * @access  Public
 */
export const handleDeepLinking = asyncHandler(async (req, res) => {
  const { id_token, state } = req.body;

  if (!id_token || !state) {
    return res.status(400).json({
      error: 'Missing Parameters',
      message: 'id_token and state are required'
    });
  }

  try {
    // Process launch to get platform and user info
    const launchResult = await ltiService.handleOIDCCallback(id_token, state);
    const deepLinkingClaim = launchResult.launchData['https://purl.imsglobal.org/spec/lti-dl/claim/deep_linking_settings'];
    
    if (!deepLinkingClaim) {
      throw new Error('Invalid deep linking request');
    }

    // Get available projects for the context
    const projects = await Project.findAll({
      where: { status: 'published' },
      include: [{
        model: Course,
        as: 'course',
        where: launchResult.context?.courseId ? { id: launchResult.context.courseId } : undefined,
        required: false
      }]
    });

    // Create deep linking response
    const deepLinkingJWT = ltiService.createDeepLinkingResponse(
      projects,
      deepLinkingClaim,
      launchResult.platform
    );

    // Return form that will post back to platform
    const returnUrl = deepLinkingClaim.deep_link_return_url;
    
    res.send(`
      <html>
        <head>
          <title>Content Selection - BITS DataScience Platform</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .project { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .project h3 { margin-top: 0; color: #333; }
            .project p { color: #666; }
            .select-btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; }
            .select-btn:hover { background: #0056b3; }
          </style>
        </head>
        <body>
          <h1>Select a Data Science Project</h1>
          <p>Choose a project to add to your course:</p>
          
          ${projects.map(project => `
            <div class="project">
              <h3>${project.title}</h3>
              <p>${project.description}</p>
              <p><strong>Difficulty:</strong> ${project.difficultyLevel}</p>
              <p><strong>Duration:</strong> ${project.estimatedDuration} hours</p>
              <button class="select-btn" onclick="selectProject('${project.id}')">Select Project</button>
            </div>
          `).join('')}
          
          <script>
            function selectProject(projectId) {
              const form = document.createElement('form');
              form.method = 'POST';
              form.action = '${returnUrl}';
              
              const input = document.createElement('input');
              input.type = 'hidden';
              input.name = 'JWT';
              input.value = '${deepLinkingJWT}';
              
              form.appendChild(input);
              document.body.appendChild(form);
              form.submit();
            }
          </script>
        </body>
      </html>
    `);

  } catch (error) {
    logger.error('Deep linking failed:', error);
    res.status(400).send(`
      <html>
        <head><title>Deep Linking Error</title></head>
        <body>
          <h1>Content Selection Failed</h1>
          <p>There was an error loading available content.</p>
          <p>Error: ${error.message}</p>
        </body>
      </html>
    `);
  }
});

/**
 * @desc    Get course roster via NRPS
 * @route   GET /api/lti/roster/:contextId
 * @access  Private (LTI authenticated)
 */
export const getCourseRoster = asyncHandler(async (req, res) => {
  const { contextId } = req.params;

  if (!req.session.ltiContext || req.session.ltiContext.contextId !== contextId) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You can only access roster for your current context'
    });
  }

  try {
    const platform = await LtiPlatform.findByPk(req.session.ltiContext.platformId);
    const roster = await ltiService.getCourseRoster(req.session.ltiContext, platform);
    
    res.json({
      success: true,
      roster: roster.members
    });

  } catch (error) {
    logger.error('Roster fetch failed:', error);
    res.status(500).json({
      error: 'Roster Fetch Failed',
      message: 'Could not retrieve course roster'
    });
  }
});

/**
 * @desc    Submit grade via AGS
 * @route   POST /api/lti/grades
 * @access  Private (LTI authenticated)
 */
export const submitGrade = asyncHandler(async (req, res) => {
  const { resourceLinkId, userId, score, maxScore = 100 } = req.body;

  if (!req.session.ltiResourceLink || req.session.ltiResourceLink.resourceLinkId !== resourceLinkId) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'Invalid resource link'
    });
  }

  try {
    const platform = await LtiPlatform.findByPk(req.session.ltiResourceLink.platformId);
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({
        error: 'User Not Found',
        message: 'User not found in the system'
      });
    }

    const result = await ltiService.submitGrade(
      req.session.ltiResourceLink,
      user,
      score,
      platform
    );

    res.json({
      success: true,
      message: 'Grade submitted successfully',
      result
    });

  } catch (error) {
    logger.error('Grade submission failed:', error);
    res.status(500).json({
      error: 'Grade Submission Failed',
      message: 'Could not submit grade to platform'
    });
  }
});

/**
 * @desc    Register new LTI platform
 * @route   POST /api/lti/platforms
 * @access  Private (Admin)
 */
export const registerPlatform = asyncHandler(async (req, res) => {
  const {
    platformName,
    platformId,
    clientId,
    authLoginUrl,
    authTokenUrl,
    keySetUrl,
    deploymentId
  } = req.body;

  // Validate required fields
  if (!platformName || !platformId || !clientId || !authLoginUrl || !authTokenUrl || !keySetUrl) {
    return res.status(400).json({
      error: 'Missing Required Fields',
      message: 'platformName, platformId, clientId, authLoginUrl, authTokenUrl, and keySetUrl are required'
    });
  }

  try {
    // Check if platform already exists
    const existingPlatform = await LtiPlatform.findOne({
      where: { platformId, clientId }
    });

    if (existingPlatform) {
      return res.status(409).json({
        error: 'Platform Already Exists',
        message: 'A platform with this issuer and client ID already exists'
      });
    }

    // Create platform
    const platform = await LtiPlatform.create({
      platformName,
      platformId,
      clientId,
      authLoginUrl,
      authTokenUrl,
      keySetUrl,
      isActive: true
    });

    // Create deployment if provided
    if (deploymentId) {
      await LtiDeployment.create({
        platformId: platform.id,
        deploymentId,
        deploymentName: `Deployment ${deploymentId}`
      });
    }

    logger.info(`LTI platform registered: ${platformName} (${platformId})`);

    res.status(201).json({
      success: true,
      message: 'Platform registered successfully',
      platform: {
        id: platform.id,
        platformName: platform.platformName,
        platformId: platform.platformId,
        clientId: platform.clientId,
        isActive: platform.isActive
      }
    });

  } catch (error) {
    logger.error('Platform registration failed:', error);
    res.status(500).json({
      error: 'Registration Failed',
      message: 'Could not register LTI platform'
    });
  }
});

/**
 * @desc    Get all registered platforms
 * @route   GET /api/lti/platforms
 * @access  Private (Admin)
 */
export const getPlatforms = asyncHandler(async (req, res) => {
  const platforms = await LtiPlatform.findAll({
    attributes: [
      'id', 'platformId', 'platformName', 'clientId', 
      'isActive', 'createdAt', 'updatedAt'
    ],
    include: [{
      model: LtiDeployment,
      as: 'deployments',
      attributes: ['id', 'deploymentId', 'deploymentName', 'isActive']
    }]
  });

  res.json({
    success: true,
    platforms: platforms.map(platform => ({
      id: platform.id,
      platformId: platform.platformId,
      platformName: platform.platformName,
      clientId: platform.clientId,
      isActive: platform.isActive,
      deploymentCount: platform.deployments?.length || 0,
      deployments: platform.deployments || [],
      createdAt: platform.createdAt,
      updatedAt: platform.updatedAt
    }))
  });
});

/**
 * @desc    Update platform configuration
 * @route   PUT /api/lti/platforms/:id
 * @access  Private (Admin)
 */
export const updatePlatform = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    platformName,
    authLoginUrl,
    authTokenUrl,
    keySetUrl,
    isActive,
    settings
  } = req.body;

  const platform = await LtiPlatform.findByPk(id);

  if (!platform) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Platform not found'
    });
  }

  const updateData = {};
  if (platformName) updateData.platformName = platformName;
  if (authLoginUrl) updateData.authLoginUrl = authLoginUrl;
  if (authTokenUrl) updateData.authTokenUrl = authTokenUrl;
  if (keySetUrl) updateData.keySetUrl = keySetUrl;
  if (isActive !== undefined) updateData.isActive = isActive;
  if (settings) updateData.settings = { ...platform.settings, ...settings };

  await platform.update(updateData);

  logger.info(`LTI Platform updated: ${platform.platformName}`);

  res.json({
    success: true,
    message: 'Platform updated successfully',
    platform: {
      id: platform.id,
      platformId: platform.platformId,
      platformName: platform.platformName,
      isActive: platform.isActive,
      updatedAt: platform.updatedAt
    }
  });
});

/**
 * @desc    Delete platform
 * @route   DELETE /api/lti/platforms/:id
 * @access  Private (Admin)
 */
export const deletePlatform = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const platform = await LtiPlatform.findByPk(id);

  if (!platform) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Platform not found'
    });
  }

  // Check if platform has active contexts
  const activeContexts = await LtiContext.count({
    where: { platformId: id, isActive: true }
  });

  if (activeContexts > 0) {
    return res.status(409).json({
      error: 'Platform In Use',
      message: 'Cannot delete platform with active contexts'
    });
  }

  await platform.destroy();

  logger.info(`LTI Platform deleted: ${platform.platformName}`);

  res.json({
    success: true,
    message: 'Platform deleted successfully'
  });
});

/**
 * @desc    Get LTI configuration for platform registration
 * @route   GET /api/lti/config
 * @access  Public
 */
export const getLTIConfiguration = asyncHandler(async (req, res) => {
  const toolUrl = process.env.LTI_TOOL_URL || `${req.protocol}://${req.get('host')}`;
  
  const configuration = {
    title: 'BITS DataScience Projects Platform',
    description: 'Interactive data science projects and assignments platform for BITS Pilani',
    target_link_uri: `${toolUrl}/api/lti/oidc/init`,
    oidc_initiation_url: `${toolUrl}/api/lti/oidc/init`,
    public_jwk_url: `${toolUrl}/.well-known/jwks.json`,
    scopes: [
      'openid',
      'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
      'https://purl.imsglobal.org/spec/lti-ags/scope/score',
      'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
    ],
    extensions: [{
      domain: new URL(toolUrl).hostname,
      tool_id: 'bits-datascience-platform',
      platform: 'bits.edu',
      settings: {
        text: 'BITS DataScience Platform',
        icon_url: `${toolUrl}/assets/bits-logo.png`,
        selection_width: 800,
        selection_height: 600
      },
      privacy_level: 'public'
    }],
    custom_fields: {
      project_id: '$ResourceLink.id',
      context_id: '$Context.id',
      user_id: '$User.id'
    },
    claims: [
      'iss',
      'aud', 
      'exp',
      'iat',
      'nonce',
      'https://purl.imsglobal.org/spec/lti/claim/deployment_id',
      'https://purl.imsglobal.org/spec/lti/claim/message_type',
      'https://purl.imsglobal.org/spec/lti/claim/version',
      'https://purl.imsglobal.org/spec/lti/claim/resource_link',
      'https://purl.imsglobal.org/spec/lti/claim/context',
      'https://purl.imsglobal.org/spec/lti/claim/tool_platform',
      'https://purl.imsglobal.org/spec/lti/claim/roles',
      'https://purl.imsglobal.org/spec/lti/claim/custom',
      'https://purl.imsglobal.org/spec/lti-ags/claim/assignmentsgradeservice',
      'https://purl.imsglobal.org/spec/lti-nrps/claim/namesroleservice',
      'https://purl.imsglobal.org/spec/lti-dl/claim/deep_linking_settings'
    ]
  };

  res.json(configuration);
});

/**
 * @desc    Get LTI session status
 * @route   GET /api/lti/session
 * @access  Private (LTI authenticated)
 */
export const getLTISession = asyncHandler(async (req, res) => {
  if (!req.session.ltiLaunchData) {
    return res.status(401).json({
      error: 'No LTI Session',
      message: 'No active LTI session found'
    });
  }

  res.json({
    success: true,
    session: {
      user: {
        id: req.session.user.id,
        email: req.session.user.email,
        name: req.session.user.name,
        roles: req.session.user.roles
      },
      context: req.session.ltiContext ? {
        id: req.session.ltiContext.id,
        contextId: req.session.ltiContext.contextId,
        title: req.session.ltiContext.title,
        label: req.session.ltiContext.label
      } : null,
      resourceLink: req.session.ltiResourceLink ? {
        id: req.session.ltiResourceLink.id,
        resourceLinkId: req.session.ltiResourceLink.resourceLinkId,
        title: req.session.ltiResourceLink.title
      } : null,
      launchData: {
        messageType: req.session.ltiLaunchData['https://purl.imsglobal.org/spec/lti/claim/message_type'],
        version: req.session.ltiLaunchData['https://purl.imsglobal.org/spec/lti/claim/version'],
        deploymentId: req.session.ltiLaunchData['https://purl.imsglobal.org/spec/lti/claim/deployment_id']
      }
    }
  });
});

/**
 * @desc    Clean up expired LTI sessions
 * @route   POST /api/lti/cleanup
 * @access  Private (Admin)
 */
export const cleanupLTISessions = asyncHandler(async (req, res) => {
  try {
    const deletedCount = await ltiService.cleanupExpiredSessions();
    
    res.json({
      success: true,
      message: `Cleaned up ${deletedCount} expired sessions`,
      deletedCount
    });

  } catch (error) {
    logger.error('LTI session cleanup failed:', error);
    res.status(500).json({
      error: 'Cleanup Failed',
      message: 'Could not clean up expired sessions'
    });
  }
});

/**
 * Determine the target page based on launch data
 */
function determineLaunchTarget(launchData, resourceLink) {
  const messageType = launchData['https://purl.imsglobal.org/spec/lti/claim/message_type'];
  
  if (messageType === 'LtiDeepLinkingRequest') {
    return 'lti/deep-linking';
  }

  // For resource link launches, determine based on user role and context
  const roles = launchData['https://purl.imsglobal.org/spec/lti/claim/roles'] || [];
  const isInstructor = roles.some(role => 
    role.includes('Instructor') || 
    role.includes('TeachingAssistant') || 
    role.includes('ContentDeveloper')
  );

  if (isInstructor) {
    return 'instructor/dashboard';
  } else {
    return 'student/dashboard';
  }
}