import { Op } from 'sequelize';
import { asyncHandler } from '../middlewares/errorHandler.js';
import { Course, CourseEnrollment, Grade, Project, Submission, User } from '../models/associations.js';
// import { uploadToS3, generatePresignedUrl, deleteFromS3 } from '../services/s3Service.js';
import logger from '../config/logger.js';
import S3Service from '../services/s3Service.js';

/**
 * @desc    Get all submissions with pagination and filtering
 * @route   GET /api/submissions
 * @access  Private
 */
export const getSubmissions = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    projectId,
    userId,
    status,
    sortBy = 'submitted_at',
    sortOrder = 'desc'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};

  // Filter by project
  if (projectId) {
    whereClause.project_id = projectId;
  }

  // Filter by user (for admin/instructor viewing specific student)
  if (userId) {
    whereClause.user_id = userId;
  }

  // Filter by status
  if (status) {
    whereClause.status = status;
  }

  // For students, only show their own submissions
  if (req.user.role === 'student') {
    whereClause.user_id = req.user.id;
  }

  const { count, rows: submissions } = await Submission.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'profile_picture']
      },
      {
        model: Project,
        as: 'project',
        attributes: ['id', 'title', 'due_date', 'difficulty_level'],
        include: [{
          model: Course,
          as: 'course',
          attributes: ['id', 'name', 'code']
        }]
      },
      {
        model: Grade,
        as: 'grade',
        include: [{
          model: User,
          as: 'evaluator',
          attributes: ['id', 'name', 'email']
        }]
      }
    ],
    limit: parseInt(limit),
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]]
  });

  // Transform submission data for response
  const transformedSubmissions = submissions.map(submission => ({
    id: submission.id,
    status: submission.status,
    submittedAt: submission.submitted_at,
    notebookS3Url: submission.notebook_s3_url,
    executionTime: submission.execution_time,
    metadata: submission.metadata,
    user: {
      id: submission.user.id,
      name: submission.user.name,
      email: submission.user.email,
      profilePicture: submission.user.profile_picture
    },
    project: {
      id: submission.project.id,
      title: submission.project.title,
      dueDate: submission.project.due_date,
      difficultyLevel: submission.project.difficulty_level,
      course: {
        id: submission.project.course.id,
        name: submission.project.course.name,
        code: submission.project.course.code
      }
    },
    grade: submission.grade ? {
      id: submission.grade.id,
      totalScore: submission.grade.total_score,
      maxScore: submission.grade.max_score,
      percentage: submission.grade.percentage,
      letterGrade: submission.grade.letter_grade,
      feedback: submission.grade.feedback,
      evaluator: submission.grade.evaluator
    } : null,
    createdAt: submission.created_at,
    updatedAt: submission.updated_at
  }));

  res.json({
    success: true,
    data: {
      submissions: transformedSubmissions,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / parseInt(limit)),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * @desc    Get submission by ID
 * @route   GET /api/submissions/:id
 * @access  Private
 */
export const getSubmissionById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const submission = await Submission.findByPk(id, {
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'profile_picture']
      },
      {
        model: Project,
        as: 'project',
        include: [{
          model: Course,
          as: 'course',
          include: [{
            model: User,
            as: 'instructor',
            attributes: ['id', 'name', 'email']
          }]
        }]
      },
      {
        model: Grade,
        as: 'grade',
        include: [{
          model: User,
          as: 'evaluator',
          attributes: ['id', 'name', 'email']
        }]
      }
    ]
  });

  if (!submission) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Submission not found'
    });
  }

  // Check if user has access to this submission
  const hasAccess = req.userRoles.includes('admin') || 
                   submission.user_id === req.user.id ||
                   submission.project.course.instructor_id === req.user.id;

  if (!hasAccess) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view this submission'
    });
  }

  // Generate presigned URL for notebook if it exists
  let notebookPresignedUrl = null;
  if (submission.notebook_s3_url) {
    try {
      notebookPresignedUrl = await S3Service.generateFilePath(submission.notebook_s3_url);
    } catch (error) {
      logger.error('Error generating presigned URL:', error);
    }
  }

  const submissionResponse = {
    id: submission.id,
    status: submission.status,
    submittedAt: submission.submitted_at,
    notebookS3Url: submission.notebook_s3_url,
    notebookPresignedUrl,
    executionTime: submission.execution_time,
    metadata: submission.metadata,
    user: {
      id: submission.user.id,
      name: submission.user.name,
      email: submission.user.email,
      profilePicture: submission.user.profile_picture
    },
    project: {
      id: submission.project.id,
      title: submission.project.title,
      description: submission.project.description,
      dueDate: submission.project.due_date,
      instructions: submission.project.instructions,
      course: {
        id: submission.project.course.id,
        name: submission.project.course.name,
        code: submission.project.course.code,
        instructor: submission.project.course.instructor
      }
    },
    grade: submission.grade ? {
      id: submission.grade.id,
      totalScore: submission.grade.total_score,
      maxScore: submission.grade.max_score,
      percentage: submission.grade.percentage,
      letterGrade: submission.grade.letter_grade,
      feedback: submission.grade.feedback,
      rubricScores: submission.grade.rubric_scores,
      evaluator: submission.grade.evaluator,
      gradedAt: submission.grade.graded_at
    } : null,
    createdAt: submission.created_at,
    updatedAt: submission.updated_at
  };

  res.json({
    success: true,
    submission: submissionResponse
  });
});

/**
 * @desc    Create or update submission
 * @route   POST /api/submissions
 * @access  Private (Student)
 */
export const createOrUpdateSubmission = asyncHandler(async (req, res) => {
  const { projectId, notebookContent, metadata = {} } = req.body;

  if (!projectId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Project ID is required'
    });
  }

  // Check if project exists and user has access
  const project = await Project.findByPk(projectId, {
    include: [{
      model: Course,
      as: 'course',
      include: [{
        model: CourseEnrollment,
        as: 'enrollments',
        where: { user_id: req.user.id },
        required: false
      }]
    }]
  });

  if (!project) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Project not found'
    });
  }

  // Check if user is enrolled in the course
  const isEnrolled = project.course.enrollments && project.course.enrollments.length > 0;
  if (!isEnrolled && !req.userRoles.includes('admin')) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You are not enrolled in this course'
    });
  }

  // Check if project is published
  if (project.status !== 'published') {
    return res.status(400).json({
      error: 'Project Not Available',
      message: 'This project is not yet published'
    });
  }

  // Check if submission deadline has passed
  if (project.due_date && new Date() > new Date(project.due_date)) {
    return res.status(400).json({
      error: 'Deadline Passed',
      message: 'The submission deadline has passed'
    });
  }

  // Find existing submission or create new one
  let submission = await Submission.findOne({
    where: { project_id: projectId, user_id: req.user.id }
  });

  let s3Url = null;
  
  // Upload notebook content to S3 if provided
  if (notebookContent) {
    try {
      const fileName = `submissions/${req.user.id}/${projectId}/notebook_${Date.now()}.ipynb`;
      s3Url = await S3Service.uploadFile(Buffer.from(notebookContent), fileName, 'application/json');
    } catch (error) {
      logger.error('Error uploading notebook to S3:', error);
      return res.status(500).json({
        error: 'Upload Error',
        message: 'Failed to upload notebook file'
      });
    }
  }

  if (submission) {
    // Update existing submission
    const updateData = {
      status: 'in_progress',
      metadata: { ...submission.metadata, ...metadata },
      updated_at: new Date()
    };

    if (s3Url) {
      // Delete old notebook file if it exists
      if (submission.notebook_s3_url) {
        try {
          await S3Service.deleteFile(submission.notebook_s3_url);
        } catch (error) {
          logger.error('Error deleting old notebook from S3:', error);
        }
      }
      updateData.notebook_s3_url = s3Url;
    }

    await submission.update(updateData);
  } else {
    // Create new submission
    submission = await Submission.create({
      project_id: projectId,
      user_id: req.user.id,
      status: 'in_progress',
      notebook_s3_url: s3Url,
      metadata
    });
  }

  logger.info(`Submission created/updated: Project ${projectId} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Submission saved successfully',
    submission: {
      id: submission.id,
      status: submission.status,
      projectId: submission.project_id,
      notebookS3Url: submission.notebook_s3_url,
      updatedAt: submission.updated_at
    }
  });
});

/**
 * @desc    Submit assignment (finalize submission)
 * @route   POST /api/submissions/:id/submit
 * @access  Private (Student)
 */
export const submitAssignment = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const submission = await Submission.findByPk(id, {
    include: [{
      model: Project,
      as: 'project',
      include: [{
        model: Course,
        as: 'course'
      }]
    }]
  });

  if (!submission) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Submission not found'
    });
  }

  // Check if user owns this submission
  if (submission.user_id !== req.user.id) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to submit this assignment'
    });
  }

  // Check if submission is already submitted
  if (submission.status === 'submitted') {
    return res.status(400).json({
      error: 'Already Submitted',
      message: 'This assignment has already been submitted'
    });
  }

  // Check if submission deadline has passed
  if (submission.project.due_date && new Date() > new Date(submission.project.due_date)) {
    return res.status(400).json({
      error: 'Deadline Passed',
      message: 'The submission deadline has passed'
    });
  }

  // Check if notebook exists
  if (!submission.notebook_s3_url) {
    return res.status(400).json({
      error: 'Incomplete Submission',
      message: 'Please upload your notebook before submitting'
    });
  }

  // Update submission status
  await submission.update({
    status: 'submitted',
    submitted_at: new Date()
  });

  logger.info(`Assignment submitted: ${submission.project.title} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Assignment submitted successfully',
    submission: {
      id: submission.id,
      status: submission.status,
      submittedAt: submission.submitted_at
    }
  });
});

/**
 * @desc    Download submission notebook
 * @route   GET /api/submissions/:id/download
 * @access  Private
 */
export const downloadSubmissionNotebook = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const submission = await Submission.findByPk(id, {
    include: [{
      model: Project,
      as: 'project',
      include: [{
        model: Course,
        as: 'course'
      }]
    }, {
      model: User,
      as: 'user',
      attributes: ['id', 'name', 'email']
    }]
  });

  if (!submission) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Submission not found'
    });
  }

  // Check if user has access to download this submission
  const hasAccess = req.userRoles.includes('admin') || 
                   submission.user_id === req.user.id ||
                   submission.project.course.instructor_id === req.user.id;

  if (!hasAccess) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to download this submission'
    });
  }

  if (!submission.notebook_s3_url) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'No notebook file found for this submission'
    });
  }

  try {
    // Generate presigned URL for download
    const downloadUrl = await S3Service.generatePresignedDownloadUrl(submission.notebook_s3_url, 3600); // 1 hour expiry

    res.json({
      success: true,
      downloadUrl,
      fileName: `${submission.user.name}_${submission.project.title}_submission.ipynb`,
      expiresIn: 3600
    });
  } catch (error) {
    logger.error('Error generating download URL:', error);
    res.status(500).json({
      error: 'Download Error',
      message: 'Failed to generate download URL'
    });
  }
});

/**
 * @desc    Get submission statistics for a project
 * @route   GET /api/submissions/project/:projectId/statistics
 * @access  Private (Instructor, Admin)
 */
export const getSubmissionStatistics = asyncHandler(async (req, res) => {
  const { projectId } = req.params;

  const project = await Project.findByPk(projectId, {
    include: [{
      model: Course,
      as: 'course'
    }]
  });

  if (!project) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Project not found'
    });
  }

  // Check permissions
  const hasPermission = req.userRoles.includes('admin') || 
                       project.course.instructor_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view submission statistics'
    });
  }

  // Get total enrolled students
  const totalEnrolled = await CourseEnrollment.count({
    where: { 
      course_id: project.course_id, 
      role_in_course: 'student',
      enrollment_status: 'active'
    }
  });

  // Get submission counts by status
  const submissionCounts = await Submission.findAll({
    where: { project_id: projectId },
    attributes: [
      'status',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    group: ['status']
  });

  // Get total submissions
  const totalSubmissions = await Submission.count({
    where: { project_id: projectId }
  });

  // Get on-time vs late submissions
  const onTimeSubmissions = await Submission.count({
    where: {
      project_id: projectId,
      status: 'submitted',
      submitted_at: {
        [Op.lte]: project.due_date
      }
    }
  });

  const lateSubmissions = await Submission.count({
    where: {
      project_id: projectId,
      status: 'submitted',
      submitted_at: {
        [Op.gt]: project.due_date
      }
    }
  });

  // Get average execution time
  const avgExecutionTime = await Submission.findOne({
    where: { 
      project_id: projectId,
      execution_time: { [Op.not]: null }
    },
    attributes: [
      [sequelize.fn('AVG', sequelize.col('execution_time')), 'average']
    ]
  });

  res.json({
    success: true,
    statistics: {
      overview: {
        totalEnrolled,
        totalSubmissions,
        submissionRate: totalEnrolled > 0 ? (totalSubmissions / totalEnrolled * 100).toFixed(1) : 0
      },
      submissionStatus: submissionCounts.map(item => ({
        status: item.status,
        count: parseInt(item.getDataValue('count'))
      })),
      timeliness: {
        onTime: onTimeSubmissions,
        late: lateSubmissions,
        notSubmitted: totalEnrolled - totalSubmissions
      },
      performance: {
        averageExecutionTime: avgExecutionTime ? 
          parseFloat(avgExecutionTime.getDataValue('average')) : null
      }
    }
  });
});

/**
 * @desc    Auto-save submission (for periodic saves)
 * @route   POST /api/submissions/autosave
 * @access  Private (Student)
 */
export const autoSaveSubmission = asyncHandler(async (req, res) => {
  const { projectId, notebookContent, metadata = {} } = req.body;

  if (!projectId || !notebookContent) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Project ID and notebook content are required'
    });
  }

  // Find existing submission
  let submission = await Submission.findOne({
    where: { project_id: projectId, user_id: req.user.id }
  });

  if (!submission) {
    // Create new submission if doesn't exist
    submission = await Submission.create({
      project_id: projectId,
      user_id: req.user.id,
      status: 'in_progress',
      metadata
    });
  }

  // Only auto-save if submission is still in progress
  if (submission.status !== 'in_progress') {
    return res.status(400).json({
      error: 'Invalid Status',
      message: 'Cannot auto-save submitted assignment'
    });
  }

  try {
    // Upload notebook content to S3
    const fileName = `submissions/${req.user.id}/${projectId}/autosave_${Date.now()}.ipynb`;
    const s3Url = await S3Service.uploadFile(Buffer.from(notebookContent), fileName, 'application/json');

    // Delete old autosave file if it exists
    if (submission.notebook_s3_url) {
      try {
        await S3Service.deleteFile(submission.notebook_s3_url);
      } catch (error) {
        logger.error('Error deleting old autosave from S3:', error);
      }
    }

    // Update submission with new file
    await submission.update({
      notebook_s3_url: s3Url,
      metadata: { ...submission.metadata, ...metadata, lastAutoSave: new Date() }
    });

    res.json({
      success: true,
      message: 'Auto-save successful',
      lastSaved: new Date()
    });
  } catch (error) {
    logger.error('Auto-save error:', error);
    res.status(500).json({
      error: 'Auto-save Error',
      message: 'Failed to auto-save submission'
    });
  }
});