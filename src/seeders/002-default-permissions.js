'use strict';
import { v4 as uuidv4 } from 'uuid';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    const permissions = [
      // User management permissions
      {
        id: uuidv4(),
        key: 'view_users',
        name: 'View Users',
        description: 'View user profiles and information',
        category: 'user',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'manage_users',
        name: 'Manage Users',
        description: 'Create, update, and manage user accounts',
        category: 'user',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'delete_users',
        name: 'Delete Users',
        description: 'Delete user accounts',
        category: 'user',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },

      // Course management permissions
      {
        id: uuidv4(),
        key: 'view_courses',
        name: 'View Courses',
        description: 'View course information',
        category: 'course',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'manage_courses',
        name: 'Manage Courses',
        description: 'Create and update courses',
        category: 'course',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'delete_courses',
        name: 'Delete Courses',
        description: 'Delete courses',
        category: 'course',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'manage_enrollments',
        name: 'Manage Enrollments',
        description: 'Enroll and unenroll students',
        category: 'course',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },

      // Project management permissions
      {
        id: uuidv4(),
        key: 'view_projects',
        name: 'View Projects',
        description: 'View project assignments',
        category: 'project',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'create_projects',
        name: 'Create Projects',
        description: 'Create new project assignments',
        category: 'project',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'edit_projects',
        name: 'Edit Projects',
        description: 'Edit existing project assignments',
        category: 'project',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'delete_projects',
        name: 'Delete Projects',
        description: 'Delete project assignments',
        category: 'project',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },

      // Submission management permissions
      {
        id: uuidv4(),
        key: 'view_submissions',
        name: 'View Submissions',
        description: 'View student submissions',
        category: 'submission',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'submit_assignments',
        name: 'Submit Assignments',
        description: 'Submit assignment solutions',
        category: 'submission',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'view_own_submissions',
        name: 'View Own Submissions',
        description: 'View own submissions and grades',
        category: 'submission',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },

      // Grading permissions
      {
        id: uuidv4(),
        key: 'grade_submissions',
        name: 'Grade Submissions',
        description: 'Grade student submissions',
        category: 'grade',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'view_grades',
        name: 'View Grades',
        description: 'View grading information',
        category: 'grade',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'manage_rubrics',
        name: 'Manage Rubrics',
        description: 'Create and edit grading rubrics',
        category: 'grade',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },

      // System administration permissions
      {
        id: uuidv4(),
        key: 'system_admin',
        name: 'System Administration',
        description: 'Full system administration access',
        category: 'system',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'view_audit_logs',
        name: 'View Audit Logs',
        description: 'View system audit logs',
        category: 'system',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'manage_settings',
        name: 'Manage Settings',
        description: 'Manage system settings',
        category: 'system',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },

      // Admin permissions
      {
        id: uuidv4(),
        key: 'view_analytics',
        name: 'View Analytics',
        description: 'View platform analytics and reports',
        category: 'admin',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'manage_roles',
        name: 'Manage Roles',
        description: 'Assign and manage user roles',
        category: 'admin',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        key: 'manage_permissions',
        name: 'Manage Permissions',
        description: 'Assign permissions to roles',
        category: 'admin',
        is_system_permission: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('permissions', permissions);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('permissions', {});
  }
};
