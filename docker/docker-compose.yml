version: '3.8'

services:
  jupyterhub:
    build:
      context: ./jupyterhub
      dockerfile: Dockerfile
    container_name: jupyterhub-multiuser
    ports:
      - "8000:8000"
    volumes:
      - jupyterhub_data:/srv/jupyterhub
      - user_notebooks:/home
    environment:
      - DOCKER_NOTEBOOK_IMAGE=jupyter/datascience-notebook:latest
    restart: unless-stopped
    networks:
      - jupyter-network

  # Optional: Add a database for production use
  # postgres:
  #   image: postgres:13
  #   container_name: jupyterhub-db
  #   environment:
  #     POSTGRES_DB: jupyterhub
  #     POSTGRES_USER: jupyterhub
  #     POSTGRES_PASSWORD: jupyterhub_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - jupyter-network

volumes:
  jupyterhub_data:
  user_notebooks:
  # postgres_data:

networks:
  jupyter-network:
    driver: bridge
