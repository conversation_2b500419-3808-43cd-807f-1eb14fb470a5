# JupyterHub with dummy authenticator for testing
FROM jupyterhub/jupyterhub:latest

# Install additional packages
RUN pip install --no-cache-dir \
    jupyterhub-dummyauthenticator \
    dockerspawner \
    notebook \
    jupyterlab \
    pandas \
    numpy \
    matplotlib \
    seaborn \
    scikit-learn

# Create jupyterhub config directory
RUN mkdir -p /srv/jupyterhub

# Copy configuration
COPY jupyterhub_config.py /srv/jupyterhub/

# Set working directory
WORKDIR /srv/jupyterhub

# Expose port
EXPOSE 8000

# Start JupyterHub
CMD ["jupyterhub", "-f", "/srv/jupyterhub/jupyterhub_config.py"]
