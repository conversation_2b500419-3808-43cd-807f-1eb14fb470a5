# JupyterHub configuration for multi-user testing

import os

# Basic configuration
c.JupyterHub.ip = '0.0.0.0'
c.JupyterHub.port = 8000

# Use dummy authenticator for testing (allows any username/password)
c.JupyterHub.authenticator_class = 'dummyauthenticator.DummyAuthenticator'
c.DummyAuthenticator.password = "dummy_password"

# Use local process spawner for simplicity
c.JupyterHub.spawner_class = 'jupyterhub.spawner.LocalProcessSpawner'

# Configure spawner
c.LocalProcessSpawner.create_system_users = True
c.LocalProcessSpawner.shell_cmd = ['bash', '-l', '-c']

# Set default URL to lab
c.Spawner.default_url = '/lab'

# Configure notebook directory
c.Spawner.notebook_dir = '~/notebooks'

# API token for backend integration
c.JupyterHub.api_tokens = {
    'your-jupyterhub-api-token': 'backend-service'
}

# Enable API access
c.JupyterHub.load_roles = [
    {
        "name": "backend-service",
        "scopes": [
            "admin:users",
            "admin:servers",
            "list:users",
            "read:users",
            "read:servers",
            "servers",
            "admin-ui"
        ],
        "services": ["backend-service"],
    }
]

# Database configuration (use SQLite for simplicity)
c.JupyterHub.db_url = 'sqlite:///jupyterhub.sqlite'

# Logging
c.JupyterHub.log_level = 'INFO'
c.Application.log_level = 'INFO'

# Security
c.JupyterHub.cookie_secret_file = '/srv/jupyterhub/cookie_secret'
c.ConfigurableHTTPProxy.auth_token = os.urandom(32).hex()

# Allow named servers (multiple servers per user)
c.JupyterHub.allow_named_servers = True
c.JupyterHub.named_server_limit_per_user = 5

# Timeout settings
c.Spawner.start_timeout = 60
c.Spawner.http_timeout = 30

# Environment variables for spawned notebooks
c.Spawner.environment = {
    'JUPYTER_ENABLE_LAB': '1',
}

# Custom user directory structure
def pre_spawn_hook(spawner):
    """Create user-specific directory structure"""
    username = spawner.user.name
    user_dir = f"/home/<USER>/notebooks"
    
    # Create user directory if it doesn't exist
    os.makedirs(user_dir, exist_ok=True)
    
    # Set notebook directory
    spawner.notebook_dir = user_dir

c.Spawner.pre_spawn_hook = pre_spawn_hook

print("JupyterHub configuration loaded successfully!")
print("- Dummy authenticator enabled (any username/password works)")
print("- API token configured for backend integration")
print("- Local process spawner configured")
print("- User isolation enabled")
