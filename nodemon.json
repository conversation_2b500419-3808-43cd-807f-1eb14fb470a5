{"watch": ["src/"], "ext": "js,json,env", "ignore": ["node_modules/", "tests/", "logs/", "uploads/", "temp/", "coverage/", "dist/"], "exec": "node src/server.js", "env": {"NODE_ENV": "development", "DEBUG": "true"}, "delay": 1000, "verbose": true, "signal": "SIGTERM", "colours": true, "events": {"restart": "echo 'BITS DataScience Backend restarted due to changes'", "crash": "echo 'BITS DataScience Backend crashed - waiting for file changes before starting'", "start": "echo 'BITS DataScience Backend started'", "quit": "echo 'BITS DataScience Backend has quit'"}}