# Use official lightweight Node.js image
FROM node:18-alpine

# Install system dependencies
RUN apk update && apk add --no-cache \
    git \
    curl \
    vim \
    postgresql-client \
    redis \
    python3 \
    py3-pip \
    make \
    g++

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json (or yarn.lock)
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy entire project
COPY . .

# Ensure runtime directories exist
RUN mkdir -p logs uploads temp

# Expose backend port
EXPOSE 5000

# Add healthcheck for Docker Compose (optional)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Default command to run your app
CMD ["npm", "run", "dev"]
