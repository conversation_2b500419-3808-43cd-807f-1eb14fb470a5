# Environment Configuration
NODE_ENV=development
VITE_APP_NAME=BITS Pilani Digital Data Science Platform
VITE_APP_VERSION=1.0.0
VITE_APP_URL=http://localhost:3000

# API Configuration (if needed in future)
# VITE_API_BASE_URL=https://api.example.com
# VITE_API_KEY=your_api_key_here

# Authentication Configuration (if needed in future)
# VITE_AUTH_DOMAIN=your-auth-domain.com
# VITE_AUTH_CLIENT_ID=your_client_id

# Analytics Configuration (if needed in future)
# VITE_ANALYTICS_ID=your_analytics_id

# Feature Flags (if needed in future)
# VITE_ENABLE_ANALYTICS=true
# VITE_ENABLE_NOTIFICATIONS=true
# VITE_ENABLE_DARK_MODE=true

# Development Configuration
VITE_DEV_TOOLS=true
VITE_MOCK_API=true

DB_HOST=postgres_db   ✅
