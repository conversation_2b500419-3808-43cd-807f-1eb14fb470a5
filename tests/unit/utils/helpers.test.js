// Test utility helper functions
describe('Utility Helper Functions', () => {
  describe('String Utilities', () => {
    test('should capitalize first letter', () => {
      const capitalize = (str) => {
        if (!str || typeof str !== 'string') return str;
        return str.charAt(0).toUpperCase() + str.slice(1);
      };

      expect(capitalize('hello')).toBe('Hello');
      expect(capitalize('world')).toBe('World');
      expect(capitalize('')).toBe('');
      expect(capitalize(null)).toBe(null);
      expect(capitalize(undefined)).toBe(undefined);
    });

    test('should generate random string', () => {
      const generateRandomString = (length = 8) => {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
      };

      const random1 = generateRandomString(10);
      const random2 = generateRandomString(10);

      expect(random1).toHaveLength(10);
      expect(random2).toHaveLength(10);
      expect(typeof random1).toBe('string');
      expect(typeof random2).toBe('string');
      // They should be different (very unlikely to be the same)
      expect(random1).not.toBe(random2);
    });

    test('should slugify strings', () => {
      const slugify = (str) => {
        return str
          .toLowerCase()
          .replace(/[^a-z0-9 -]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim('-');
      };

      expect(slugify('Hello World')).toBe('hello-world');
      expect(slugify('Test String 123')).toBe('test-string-123');
      expect(slugify('Special@#$%Characters')).toBe('specialcharacters');
      expect(slugify('Multiple   Spaces')).toBe('multiple-spaces');
    });

    test('should truncate strings', () => {
      const truncate = (str, length = 50, suffix = '...') => {
        if (!str || str.length <= length) return str;
        return str.substring(0, length - suffix.length) + suffix;
      };

      expect(truncate('Short string')).toBe('Short string');
      expect(truncate('This is a very long string that should be truncated', 20)).toBe('This is a very lo...');
      expect(truncate('Long string', 10, '***')).toBe('Long st***');
    });
  });

  describe('Array Utilities', () => {
    test('should remove duplicates from array', () => {
      const removeDuplicates = (arr) => {
        return [...new Set(arr)];
      };

      expect(removeDuplicates([1, 2, 2, 3, 3, 4])).toEqual([1, 2, 3, 4]);
      expect(removeDuplicates(['a', 'b', 'a', 'c'])).toEqual(['a', 'b', 'c']);
      expect(removeDuplicates([])).toEqual([]);
    });

    test('should chunk array into smaller arrays', () => {
      const chunk = (arr, size) => {
        const chunks = [];
        for (let i = 0; i < arr.length; i += size) {
          chunks.push(arr.slice(i, i + size));
        }
        return chunks;
      };

      expect(chunk([1, 2, 3, 4, 5, 6], 2)).toEqual([[1, 2], [3, 4], [5, 6]]);
      expect(chunk([1, 2, 3, 4], 3)).toEqual([[1, 2, 3], [4]]);
      expect(chunk([], 2)).toEqual([]);
    });

    test('should shuffle array', () => {
      const shuffle = (arr) => {
        const shuffled = [...arr];
        for (let i = shuffled.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
      };

      const original = [1, 2, 3, 4, 5];
      const shuffled = shuffle(original);

      expect(shuffled).toHaveLength(original.length);
      expect(shuffled.sort()).toEqual(original.sort());
    });

    test('should group array by key', () => {
      const groupBy = (arr, key) => {
        return arr.reduce((groups, item) => {
          const group = item[key];
          groups[group] = groups[group] || [];
          groups[group].push(item);
          return groups;
        }, {});
      };

      const data = [
        { id: 1, category: 'A', value: 10 },
        { id: 2, category: 'B', value: 20 },
        { id: 3, category: 'A', value: 30 },
        { id: 4, category: 'B', value: 40 }
      ];

      const grouped = groupBy(data, 'category');

      expect(grouped.A).toHaveLength(2);
      expect(grouped.B).toHaveLength(2);
      expect(grouped.A[0].id).toBe(1);
      expect(grouped.A[1].id).toBe(3);
    });
  });

  describe('Object Utilities', () => {
    test('should deep clone object', () => {
      const deepClone = (obj) => {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (Array.isArray(obj)) return obj.map(item => deepClone(item));
        
        const cloned = {};
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            cloned[key] = deepClone(obj[key]);
          }
        }
        return cloned;
      };

      const original = {
        name: 'test',
        nested: { value: 123 },
        array: [1, 2, { nested: true }]
      };

      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.nested).not.toBe(original.nested);
    });

    test('should pick specific keys from object', () => {
      const pick = (obj, keys) => {
        const picked = {};
        keys.forEach(key => {
          if (obj.hasOwnProperty(key)) {
            picked[key] = obj[key];
          }
        });
        return picked;
      };

      const obj = { a: 1, b: 2, c: 3, d: 4 };
      const picked = pick(obj, ['a', 'c']);

      expect(picked).toEqual({ a: 1, c: 3 });
      expect(picked).not.toHaveProperty('b');
      expect(picked).not.toHaveProperty('d');
    });

    test('should omit specific keys from object', () => {
      const omit = (obj, keys) => {
        const omitted = {};
        for (const key in obj) {
          if (obj.hasOwnProperty(key) && !keys.includes(key)) {
            omitted[key] = obj[key];
          }
        }
        return omitted;
      };

      const obj = { a: 1, b: 2, c: 3, d: 4 };
      const omitted = omit(obj, ['b', 'd']);

      expect(omitted).toEqual({ a: 1, c: 3 });
      expect(omitted).not.toHaveProperty('b');
      expect(omitted).not.toHaveProperty('d');
    });

    test('should check if object is empty', () => {
      const isEmpty = (obj) => {
        if (obj == null) return true;
        if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
        return Object.keys(obj).length === 0;
      };

      expect(isEmpty({})).toBe(true);
      expect(isEmpty({ key: 'value' })).toBe(false);
      expect(isEmpty([])).toBe(true);
      expect(isEmpty([1, 2, 3])).toBe(false);
      expect(isEmpty('')).toBe(true);
      expect(isEmpty('hello')).toBe(false);
      expect(isEmpty(null)).toBe(true);
      expect(isEmpty(undefined)).toBe(true);
    });
  });

  describe('Date Utilities', () => {
    test('should format date', () => {
      const formatDate = (date, format = 'YYYY-MM-DD') => {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        
        return format
          .replace('YYYY', year)
          .replace('MM', month)
          .replace('DD', day);
      };

      const testDate = new Date('2023-12-25');
      expect(formatDate(testDate)).toBe('2023-12-25');
      expect(formatDate(testDate, 'MM/DD/YYYY')).toBe('12/25/2023');
    });

    test('should check if date is valid', () => {
      const isValidDate = (date) => {
        const d = new Date(date);
        return d instanceof Date && !isNaN(d);
      };

      expect(isValidDate('2023-12-25')).toBe(true);
      expect(isValidDate('invalid-date')).toBe(false);
      expect(isValidDate('2023-13-45')).toBe(false);
      expect(isValidDate(new Date())).toBe(true);
    });

    test('should get relative time', () => {
      const getRelativeTime = (date) => {
        const now = new Date();
        const diff = now - new Date(date);
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        return 'Just now';
      };

      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      expect(getRelativeTime(oneHourAgo)).toBe('1 hour ago');
      expect(getRelativeTime(oneDayAgo)).toBe('1 day ago');
    });
  });

  describe('Number Utilities', () => {
    test('should format number with commas', () => {
      const formatNumber = (num) => {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      };

      expect(formatNumber(1234)).toBe('1,234');
      expect(formatNumber(1234567)).toBe('1,234,567');
      expect(formatNumber(100)).toBe('100');
      expect(formatNumber(0)).toBe('0');
    });

    test('should round to decimal places', () => {
      const roundTo = (num, decimals = 2) => {
        return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
      };

      expect(roundTo(3.14159, 2)).toBe(3.14);
      expect(roundTo(3.14159, 3)).toBe(3.142);
      expect(roundTo(3.14159, 0)).toBe(3);
      expect(roundTo(3.5, 0)).toBe(4);
    });

    test('should check if number is in range', () => {
      const isInRange = (num, min, max) => {
        return num >= min && num <= max;
      };

      expect(isInRange(5, 1, 10)).toBe(true);
      expect(isInRange(1, 1, 10)).toBe(true);
      expect(isInRange(10, 1, 10)).toBe(true);
      expect(isInRange(0, 1, 10)).toBe(false);
      expect(isInRange(11, 1, 10)).toBe(false);
    });

    test('should generate random number in range', () => {
      const randomInRange = (min, max) => {
        return Math.floor(Math.random() * (max - min + 1)) + min;
      };

      const result = randomInRange(1, 10);
      expect(result).toBeGreaterThanOrEqual(1);
      expect(result).toBeLessThanOrEqual(10);
      expect(Number.isInteger(result)).toBe(true);
    });
  });

  describe('Validation Utilities', () => {
    test('should validate email format', () => {
      const isValidEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      };

      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
    });

    test('should validate URL format', () => {
      const isValidUrl = (url) => {
        try {
          new URL(url);
          return true;
        } catch {
          return false;
        }
      };

      expect(isValidUrl('https://example.com')).toBe(true);
      expect(isValidUrl('http://localhost:3000')).toBe(true);
      expect(isValidUrl('not-a-url')).toBe(false);
      expect(isValidUrl('')).toBe(false);
    });

    test('should validate phone number format', () => {
      const isValidPhone = (phone) => {
        const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
        return phoneRegex.test(phone);
      };

      expect(isValidPhone('1234567890')).toBe(true);
      expect(isValidPhone('******-567-8900')).toBe(true);
      expect(isValidPhone('(*************')).toBe(true);
      expect(isValidPhone('123')).toBe(false);
      expect(isValidPhone('invalid')).toBe(false);
    });
  });

  describe('File Utilities', () => {
    test('should get file extension', () => {
      const getFileExtension = (filename) => {
        return filename.split('.').pop().toLowerCase();
      };

      expect(getFileExtension('document.pdf')).toBe('pdf');
      expect(getFileExtension('image.JPG')).toBe('jpg');
      expect(getFileExtension('script.js')).toBe('js');
      expect(getFileExtension('noextension')).toBe('noextension');
    });

    test('should format file size', () => {
      const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      };

      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
      expect(formatFileSize(500)).toBe('500 Bytes');
    });

    test('should validate file type', () => {
      const isValidFileType = (filename, allowedTypes) => {
        const extension = filename.split('.').pop().toLowerCase();
        return allowedTypes.includes(extension);
      };

      const allowedTypes = ['jpg', 'png', 'pdf', 'doc'];
      expect(isValidFileType('image.jpg', allowedTypes)).toBe(true);
      expect(isValidFileType('document.pdf', allowedTypes)).toBe(true);
      expect(isValidFileType('script.js', allowedTypes)).toBe(false);
      expect(isValidFileType('virus.exe', allowedTypes)).toBe(false);
    });
  });
});
