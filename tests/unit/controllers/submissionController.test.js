import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/models/associations.js', () => ({
  Submission: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    findAndCountAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    findByPk: jest.fn(),
    count: jest.fn()
  },
  User: {
    findOne: jest.fn(),
    findByPk: jest.fn()
  },
  Project: {
    findByPk: jest.fn()
  },
  Course: {
    findByPk: jest.fn()
  },
  CourseEnrollment: {
    findOne: jest.fn()
  },
  Grade: {
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    findAll: jest.fn()
  }
}));

jest.mock('../../../src/config/logger.js', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../../src/middlewares/errorHandler.js', () => ({
  asyncHandler: jest.fn((fn) => fn)
}));

jest.mock('../../../src/services/s3Service.js', () => ({
  default: {
    uploadFile: jest.fn().mockResolvedValue('https://s3.example.com/new-autosave.ipynb'),
    deleteFile: jest.fn().mockResolvedValue(true)
  }
}));

// Import the controller functions
import {
  getSubmissions,
  getSubmissionById,
  createOrUpdateSubmission,
  submitAssignment,
  downloadSubmissionNotebook,
  getSubmissionStatistics,
  autoSaveSubmission
} from '../../../src/controllers/submissionController.js';

import { Submission, User, Project, Grade } from '../../../src/models/associations.js';
import logger from '../../../src/config/logger.js';

describe('Submission Controller', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      params: {},
      query: {},
      body: {},
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        roles: [{ name: 'student' }]
      },
      userRoles: ['student']
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      download: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getSubmissions', () => {
    test('should get all submissions with pagination', async () => {
      const mockSubmissions = [
        { 
          id: 'submission-1', 
          status: 'submitted',
          submitted_at: new Date(),
          notebook_s3_url: 'https://s3.example.com/notebook1.ipynb',
          execution_time: 120,
          metadata: {},
          project: {
            id: 'project-1',
            title: 'Data Analysis Project',
            due_date: new Date(),
            difficulty_level: 'intermediate',
            course: {
              id: 'course-1',
              name: 'Data Science 101',
              code: 'DS101'
            }
          },
          user: {
            id: 'user-1',
            name: 'John Doe',
            email: '<EMAIL>',
            profile_picture: null
          },
          grade: null
        },
        { 
          id: 'submission-2', 
          status: 'graded',
          submitted_at: new Date(),
          notebook_s3_url: 'https://s3.example.com/notebook2.ipynb',
          execution_time: 180,
          metadata: {},
          project: {
            id: 'project-2',
            title: 'ML Project',
            due_date: new Date(),
            difficulty_level: 'advanced',
            course: {
              id: 'course-2',
              name: 'Machine Learning',
              code: 'ML101'
            }
          },
          user: {
            id: 'user-2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            profile_picture: null
          },
          grade: {
            id: 'grade-1',
            percentage: 85,
            evaluator: {
              id: 'instructor-1',
              name: 'Dr. Smith',
              email: '<EMAIL>'
            }
          }
        }
      ];

      mockRequest.query = { page: 1, limit: 10 };
      Submission.findAndCountAll.mockResolvedValue({
        count: 2,
        rows: mockSubmissions
      });

      await getSubmissions(mockRequest, mockResponse, mockNext);

      expect(Submission.findAndCountAll).toHaveBeenCalledWith(expect.objectContaining({
        where: {},
        limit: 10,
        offset: 0,
        order: [['submitted_at', 'DESC']]
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          submissions: mockSubmissions,
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 2,
            itemsPerPage: 10
          }
        }
      });
    });

    test('should filter submissions by project ID', async () => {
      mockRequest.query = { projectId: 'project-123' };
      Submission.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getSubmissions(mockRequest, mockResponse, mockNext);

      expect(Submission.findAndCountAll).toHaveBeenCalledWith(expect.objectContaining({
        where: { project_id: 'project-123' },
        limit: 10,
        offset: 0,
        order: [['submitted_at', 'DESC']]
      }));
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      Submission.findAndCountAll.mockRejectedValue(error);

      try {
        await getSubmissions(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });

  describe('getSubmissionById', () => {
    test('should get submission by ID', async () => {
      const mockSubmission = {
        id: 'submission-123',
        status: 'submitted',
        user_id: 'user-123',
        project: {
          id: 'project-123',
          title: 'Test Project',
          course: {
            id: 'course-123',
            instructor_id: 'instructor-123'
          }
        },
        user: {
          id: 'user-123',
          name: 'Test User'
        },
        grade: {
          id: 'grade-123',
          percentage: 85
        }
      };

      mockRequest.params = { id: 'submission-123' };
      Submission.findByPk.mockResolvedValue(mockSubmission);

      await getSubmissionById(mockRequest, mockResponse, mockNext);

      expect(Submission.findByPk).toHaveBeenCalledWith('submission-123', expect.any(Object));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockSubmission
      });
    });

    test('should return 404 when submission not found', async () => {
      mockRequest.params = { id: 'submission-123' };
      Submission.findByPk.mockResolvedValue(null);

      await getSubmissionById(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Submission not found'
      });
    });
  });

  describe('createOrUpdateSubmission', () => {
    test('should create new submission', async () => {
      const submissionData = {
        projectId: 'project-123',
        content: 'Test content',
        notebookPath: '/path/to/notebook.ipynb'
      };

      const createdSubmission = {
        id: 'submission-123',
        ...submissionData
      };

      mockRequest.body = submissionData;
      Project.findByPk.mockResolvedValue({ 
        id: 'project-123',
        course: {
          id: 'course-123',
          instructor_id: 'instructor-123'
        }
      });
      Submission.findOne.mockResolvedValue(null);
      Submission.create.mockResolvedValue(createdSubmission);

      await createOrUpdateSubmission(mockRequest, mockResponse, mockNext);

      expect(Project.findByPk).toHaveBeenCalledWith('project-123');
      expect(Submission.findOne).toHaveBeenCalledWith({
        where: { project_id: 'project-123', user_id: 'user-123' }
      });
      expect(Submission.create).toHaveBeenCalledWith(expect.objectContaining({
        project_id: submissionData.projectId,
        user_id: 'user-123',
        content: submissionData.content,
        notebook_path: submissionData.notebookPath
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Submission created successfully',
        data: createdSubmission
      });
    });

    test('should update existing submission', async () => {
      const submissionData = {
        projectId: 'project-123',
        content: 'Updated content'
      };

      const existingSubmission = {
        id: 'submission-123',
        update: jest.fn().mockResolvedValue(true)
      };

      mockRequest.body = submissionData;
      Project.findByPk.mockResolvedValue({ 
        id: 'project-123',
        course: {
          id: 'course-123',
          instructor_id: 'instructor-123'
        }
      });
      Submission.findOne.mockResolvedValue(existingSubmission);

      await createOrUpdateSubmission(mockRequest, mockResponse, mockNext);

      expect(existingSubmission.update).toHaveBeenCalledWith(expect.objectContaining({
        content: submissionData.content
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Submission updated successfully',
        data: existingSubmission
      });
    });

    test('should return 404 when project not found', async () => {
      mockRequest.body = { projectId: 'project-123' };
      Project.findByPk.mockResolvedValue(null);

      await createOrUpdateSubmission(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Project not found'
      });
    });
  });

  describe('submitAssignment', () => {
    test('should submit assignment successfully', async () => {
      const mockSubmission = {
        id: 'submission-123',
        user_id: 'user-123',
        status: 'in_progress',
        notebook_s3_url: 'https://s3.example.com/notebook.ipynb',
        update: jest.fn().mockResolvedValue(true),
        project: {
          id: 'project-123',
          title: 'Test Project',
          due_date: new Date(Date.now() + 86400000), // Tomorrow
          course: {
            id: 'course-123',
            instructor_id: 'instructor-123'
          }
        }
      };

      mockRequest.params = { id: 'submission-123' };
      Submission.findByPk.mockResolvedValue(mockSubmission);

      await submitAssignment(mockRequest, mockResponse, mockNext);

      expect(Submission.findByPk).toHaveBeenCalledWith('submission-123', expect.any(Object));
      expect(mockSubmission.update).toHaveBeenCalledWith({
        status: 'submitted',
        submitted_at: expect.any(Date)
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Assignment submitted successfully',
        submission: expect.any(Object)
      });
    });

    test('should return 404 when submission not found', async () => {
      mockRequest.params = { id: 'submission-123' };
      Submission.findByPk.mockResolvedValue(null);

      await submitAssignment(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Submission not found'
      });
    });
  });

  describe('downloadSubmissionNotebook', () => {
    test('should download submission notebook', async () => {
      const mockSubmission = {
        id: 'submission-123',
        notebook_path: '/path/to/notebook.ipynb',
        user_id: 'user-123',
        user: {
          id: 'user-123',
          name: 'Test User'
        },
        project: {
          id: 'project-123',
          title: 'Test Project',
          course: {
            id: 'course-123',
            instructor_id: 'instructor-123'
          }
        }
      };

      mockRequest.params = { id: 'submission-123' };
      Submission.findByPk.mockResolvedValue(mockSubmission);

      await downloadSubmissionNotebook(mockRequest, mockResponse, mockNext);

      expect(Submission.findByPk).toHaveBeenCalledWith('submission-123', expect.any(Object));
      expect(mockResponse.download).toHaveBeenCalledWith(
        mockSubmission.notebook_path,
        expect.any(String)
      );
    });

    test('should return 404 when submission not found', async () => {
      mockRequest.params = { id: 'submission-123' };
      Submission.findByPk.mockResolvedValue(null);

      await downloadSubmissionNotebook(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Submission not found'
      });
    });
  });

  describe('getSubmissionStatistics', () => {
    test('should get submission statistics successfully', async () => {
      const mockStats = {
        totalSubmissions: 10,
        submittedCount: 8,
        gradedCount: 5,
        averageGrade: 85.5
      };

      Submission.count = jest.fn().mockResolvedValue(10);
      Submission.findAll = jest.fn().mockResolvedValue([
        { status: 'submitted' },
        { status: 'graded' }
      ]);
      Grade.findAll = jest.fn().mockResolvedValue([
        { percentage: 85 },
        { percentage: 90 }
      ]);

      await getSubmissionStatistics(mockRequest, mockResponse, mockNext);

      expect(Submission.count).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          totalSubmissions: expect.any(Number),
          submittedCount: expect.any(Number),
          gradedCount: expect.any(Number),
          averageGrade: expect.any(Number)
        })
      });
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      Submission.count.mockRejectedValue(error);

      try {
        await getSubmissionStatistics(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });

  describe('autoSaveSubmission', () => {
    test('should auto-save submission successfully', async () => {
      const submissionData = {
        projectId: 'project-123',
        notebookContent: 'Auto-saved content',
        metadata: { test: 'data' }
      };

      const mockSubmission = {
        id: 'submission-123',
        status: 'in_progress',
        notebook_s3_url: 'https://s3.example.com/old-autosave.ipynb',
        update: jest.fn().mockResolvedValue(true)
      };

      mockRequest.body = submissionData;
      Submission.findOne.mockResolvedValue(mockSubmission);

      await autoSaveSubmission(mockRequest, mockResponse, mockNext);

      expect(Submission.findOne).toHaveBeenCalledWith({
        where: { project_id: 'project-123', user_id: 'user-123' }
      });
      expect(mockSubmission.update).toHaveBeenCalledWith({
        notebook_s3_url: expect.any(String),
        metadata: expect.any(Object)
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Auto-save successful',
        lastSaved: expect.any(Date)
      });
    });

    test('should return 400 when missing required fields', async () => {
      mockRequest.body = { projectId: 'project-123' }; // Missing notebookContent

      await autoSaveSubmission(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation Error',
        message: 'Project ID and notebook content are required'
      });
    });
  });
});
