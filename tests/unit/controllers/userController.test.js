import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/models/associations.js', () => ({
  User: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    findAndCountAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    count: jest.fn(),
    findByPk: jest.fn()
  },
  Role: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    findByPk: jest.fn()
  },
  Permission: {
    findAll: jest.fn()
  }
}));

jest.mock('../../../src/config/logger.js', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../../src/middlewares/auth.js', () => ({
  generateToken: jest.fn()
}));

jest.mock('../../../src/middlewares/errorHandler.js', () => ({
  asyncHandler: jest.fn((fn) => fn)
}));

// Import the controller functions that actually exist
import {
  getUsers,
  getUserById,
  updateUser,
  updateUserStatus,
  assignUserRole,
  removeUserRole,
  getUserStatistics
} from '../../../src/controllers/userController.js';

import { User, Role, Permission } from '../../../src/models/associations.js';
import logger from '../../../src/config/logger.js';
import { generateToken } from '../../../src/middlewares/auth.js';

describe('User Controller', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      params: {},
      query: {},
      body: {},
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        roles: [{ name: 'admin' }]
      }
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getUsers', () => {
    test('should get all users with pagination', async () => {
      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>', name: 'User 1' },
        { id: 'user-2', email: '<EMAIL>', name: 'User 2' }
      ];

      mockRequest.query = { page: 1, limit: 10 };
      User.findAndCountAll.mockResolvedValue({
        count: 2,
        rows: mockUsers
      });

      await getUsers(mockRequest, mockResponse, mockNext);

      expect(User.findAndCountAll).toHaveBeenCalledWith({
        where: {},
        include: [{
          model: Role,
          as: 'roles',
          include: [{
            model: Permission,
            as: 'permissions'
          }]
        }],
        limit: 10,
        offset: 0,
        order: [['created_at', 'DESC']],
        distinct: true
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          users: expect.any(Array),
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 2,
            itemsPerPage: 10
          }
        }
      });
    });

    test('should filter users by role', async () => {
      mockRequest.query = { role: 'student' };
      User.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getUsers(mockRequest, mockResponse, mockNext);

      expect(User.findAndCountAll).toHaveBeenCalledWith({
        where: {},
        include: [{
          model: Role,
          as: 'roles',
          where: { name: 'student' },
          required: true,
          include: [{
            model: Permission,
            as: 'permissions'
          }]
        }],
        limit: 10,
        offset: 0,
        order: [['created_at', 'DESC']],
        distinct: true
      });
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      User.findAndCountAll.mockRejectedValue(error);

      try {
        await getUsers(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });

  describe('getUserById', () => {
    test('should get user by ID', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      mockRequest.params = { id: 'user-123' };
      User.findByPk.mockResolvedValue(mockUser);

      await getUserById(mockRequest, mockResponse, mockNext);

      expect(User.findByPk).toHaveBeenCalledWith('user-123', {
        include: [{
          model: Role,
          as: 'roles',
          include: [{
            model: Permission,
            as: 'permissions'
          }]
        }, {
          model: undefined,
          as: 'enrollments',
          include: [{
            model: undefined,
            as: 'course'
          }]
        }]
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        user: expect.objectContaining({
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User'
        })
      });
    });

    test('should return 404 when user not found', async () => {
      mockRequest.params = { id: 'user-123' };
      User.findByPk.mockResolvedValue(null);

      await getUserById(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'User not found'
      });
    });
  });

  describe('updateUser', () => {
    test('should update user successfully', async () => {
      const mockUser = {
        id: 'user-123',
        name: 'Updated User',
        email: '<EMAIL>',
        update: jest.fn().mockResolvedValue(true)
      };

      mockRequest.params = { id: 'user-123' };
      mockRequest.body = { name: 'Updated User' };
      User.findByPk.mockResolvedValue(mockUser);

      await updateUser(mockRequest, mockResponse, mockNext);

      expect(User.findByPk).toHaveBeenCalledWith('user-123');
      expect(mockUser.update).toHaveBeenCalledWith({ name: 'Updated User' });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Profile updated successfully',
        user: expect.objectContaining({
          id: 'user-123',
          name: 'Updated User',
          email: '<EMAIL>'
        })
      });
    });

    test('should return 404 when user not found', async () => {
      mockRequest.params = { id: 'user-123' };
      User.findByPk.mockResolvedValue(null);

      await updateUser(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'User not found'
      });
    });
  });

  describe('updateUserStatus', () => {
    test('should update user status successfully', async () => {
      const mockUser = {
        id: 'user-123',
        status: 'active',
        update: jest.fn().mockResolvedValue(true)
      };

      mockRequest.params = { id: 'user-123' };
      mockRequest.body = { status: 'inactive' };
      User.findByPk.mockResolvedValue(mockUser);

      await updateUserStatus(mockRequest, mockResponse, mockNext);

      expect(User.findByPk).toHaveBeenCalledWith('user-123');
      expect(mockUser.update).toHaveBeenCalledWith({ status: 'inactive' });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'User status updated successfully',
        user: expect.objectContaining({
          id: 'user-123',
          status: 'active'
        })
      });
    });

    test('should return 404 when user not found', async () => {
      mockRequest.params = { id: 'user-123' };
      mockRequest.body = { status: 'invalid' }; // Invalid status to trigger validation error
      User.findByPk.mockResolvedValue(null);

      await updateUserStatus(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation Error',
        message: 'Invalid status. Must be: active, inactive, or suspended'
      });
    });
  });

  describe('assignUserRole', () => {
    test('should assign role to user successfully', async () => {
      const mockUser = {
        id: 'user-123',
        addRole: jest.fn().mockResolvedValue(true),
        hasRole: jest.fn().mockResolvedValue(false)
      };
      const mockRole = {
        id: 'role-123',
        name: 'student'
      };

      mockRequest.params = { id: 'user-123' };
      mockRequest.body = { roleId: 'role-123' };
      User.findByPk.mockResolvedValue(mockUser);
      Role.findByPk.mockResolvedValue(mockRole);

      await assignUserRole(mockRequest, mockResponse, mockNext);

      expect(User.findByPk).toHaveBeenCalledWith('user-123');
      expect(Role.findByPk).toHaveBeenCalledWith('role-123');
      expect(mockUser.hasRole).toHaveBeenCalledWith(mockRole);
      expect(mockUser.addRole).toHaveBeenCalledWith(mockRole, {
        through: {
          assigned_by: 'user-123',
          is_primary: false
        }
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Role assigned successfully'
      });
    });

    test('should return 404 when user not found', async () => {
      mockRequest.params = { id: 'user-123' };
      mockRequest.body = {}; // Missing roleId to trigger validation error
      User.findByPk.mockResolvedValue(null);

      await assignUserRole(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation Error',
        message: 'Role ID is required'
      });
    });

    test('should return 404 when role not found', async () => {
      const mockUser = {
        id: 'user-123'
      };

      mockRequest.params = { id: 'user-123' };
      mockRequest.body = { roleId: 'role-123' };
      User.findByPk.mockResolvedValue(mockUser);
      Role.findByPk.mockResolvedValue(null);

      await assignUserRole(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Role not found'
      });
    });
  });

  describe('removeUserRole', () => {
    test('should remove role from user successfully', async () => {
      const mockUser = {
        id: 'user-123',
        removeRole: jest.fn().mockResolvedValue(true)
      };
      const mockRole = {
        id: 'role-123',
        name: 'student'
      };

      mockRequest.params = { id: 'user-123', roleId: 'role-123' };
      User.findByPk.mockResolvedValue(mockUser);
      Role.findByPk.mockResolvedValue(mockRole);

      await removeUserRole(mockRequest, mockResponse, mockNext);

      expect(User.findByPk).toHaveBeenCalledWith('user-123');
      expect(Role.findByPk).toHaveBeenCalledWith('role-123');
      expect(mockUser.removeRole).toHaveBeenCalledWith(mockRole);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Role removed successfully'
      });
    });

    test('should return 404 when user not found', async () => {
      mockRequest.params = { id: 'user-123', roleId: 'role-123' };
      User.findByPk.mockResolvedValue(null);

      await removeUserRole(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'User not found'
      });
    });

    test('should return 404 when role not found', async () => {
      const mockUser = {
        id: 'user-123'
      };

      mockRequest.params = { id: 'user-123', roleId: 'role-123' };
      User.findByPk.mockResolvedValue(mockUser);
      Role.findByPk.mockResolvedValue(null);

      await removeUserRole(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Role not found'
      });
    });
  });

  describe('getUserStatistics', () => {
    test('should get user statistics successfully', async () => {
      const mockRoleStats = [
        { name: 'student', getDataValue: jest.fn().mockReturnValue('50') },
        { name: 'instructor', getDataValue: jest.fn().mockReturnValue('10') }
      ];

      User.count = jest.fn().mockResolvedValue(100);
      User.findAll = jest.fn().mockResolvedValue([
        { status: 'active' },
        { status: 'inactive' }
      ]);
      Role.findAll = jest.fn().mockResolvedValue(mockRoleStats);

      await getUserStatistics(mockRequest, mockResponse, mockNext);

      expect(User.count).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        statistics: expect.objectContaining({
          total: expect.any(Number),
          active: expect.any(Number),
          inactive: expect.any(Number),
          roleDistribution: expect.any(Array)
        })
      });
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      User.count.mockRejectedValue(error);

      try {
        await getUserStatistics(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });
});
