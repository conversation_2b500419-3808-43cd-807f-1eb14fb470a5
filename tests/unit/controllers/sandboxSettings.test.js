import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/models/Project.js');
jest.mock('../../../src/models/ProjectSandboxSettings.js');
jest.mock('../../../src/models/User.js');
jest.mock('../../../src/config/logger.js');

import Project from '../../../src/models/Project.js';
import ProjectSandboxSettings from '../../../src/models/ProjectSandboxSettings.js';
import User from '../../../src/models/User.js';

describe('Sandbox Settings Controller (Mocked)', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      params: {},
      body: {},
      query: {},
      headers: {}
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/sandbox/projects/:projectId/sandbox-settings', () => {
    test('should get sandbox settings for project', async () => {
      const mockProject = {
        id: '550e8400-e29b-41d4-a716-************',
        title: 'Test Project',
        description: 'A test project'
      };

      const mockSettings = {
        id: '550e8400-e29b-41d4-a716-************',
        projectId: mockProject.id,
        mode: 'docker',
        cpuLimit: '1.0',
        memLimit: '2g',
        timeout: 3600,
        isActive: true
      };

      // Mock Project.findByPk
      Project.findByPk = jest.fn().mockResolvedValue(mockProject);
      
      // Mock ProjectSandboxSettings.findOne
      ProjectSandboxSettings.findOne = jest.fn().mockResolvedValue(mockSettings);

      const getSandboxSettings = async (req, res) => {
        const { projectId } = req.params;

        // Validate UUID format
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(projectId)) {
          return res.status(400).json({
            error: 'Invalid project ID format'
          });
        }

        // Check if project exists
        const project = await Project.findByPk(projectId);
        if (!project) {
          return res.status(404).json({
            error: 'Project not found'
          });
        }

        // Get sandbox settings
        const settings = await ProjectSandboxSettings.findOne({
          where: { projectId, isActive: true }
        });

        if (!settings) {
          // Return default settings
          return res.status(200).json({
            settings: {
              projectId,
              mode: 'uv',
              cpuLimit: '0.5',
              memLimit: '1g',
              timeout: 1800,
              isActive: true
            }
          });
        }

        res.status(200).json({
          settings
        });
      };

      mockRequest.params = { projectId: mockProject.id };
      await getSandboxSettings(mockRequest, mockResponse);

      expect(Project.findByPk).toHaveBeenCalledWith(mockProject.id);
      expect(ProjectSandboxSettings.findOne).toHaveBeenCalledWith({
        where: { projectId: mockProject.id, isActive: true }
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        settings: mockSettings
      });
    });

    test('should return 404 for non-existent project', async () => {
      // Mock Project.findByPk to return null
      Project.findByPk = jest.fn().mockResolvedValue(null);

      const getSandboxSettings = async (req, res) => {
        const { projectId } = req.params;

        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(projectId)) {
          return res.status(400).json({
            error: 'Invalid project ID format'
          });
        }

        const project = await Project.findByPk(projectId);
        if (!project) {
          return res.status(404).json({
            error: 'Project not found'
          });
        }

        res.status(200).json({ settings: {} });
      };

      mockRequest.params = { projectId: '550e8400-e29b-41d4-a716-************' };
      await getSandboxSettings(mockRequest, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Project not found'
      });
    });

    test('should return default settings when no settings exist', async () => {
      const mockProject = {
        id: '550e8400-e29b-41d4-a716-************',
        title: 'Test Project'
      };

      // Mock Project.findByPk
      Project.findByPk = jest.fn().mockResolvedValue(mockProject);
      
      // Mock ProjectSandboxSettings.findOne to return null
      ProjectSandboxSettings.findOne = jest.fn().mockResolvedValue(null);

      const getSandboxSettings = async (req, res) => {
        const { projectId } = req.params;

        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(projectId)) {
          return res.status(400).json({
            error: 'Invalid project ID format'
          });
        }

        const project = await Project.findByPk(projectId);
        if (!project) {
          return res.status(404).json({
            error: 'Project not found'
          });
        }

        const settings = await ProjectSandboxSettings.findOne({
          where: { projectId, isActive: true }
        });

        if (!settings) {
          return res.status(200).json({
            settings: {
              projectId,
              mode: 'uv',
              cpuLimit: '0.5',
              memLimit: '1g',
              timeout: 1800,
              isActive: true
            }
          });
        }

        res.status(200).json({
          settings
        });
      };

      mockRequest.params = { projectId: mockProject.id };
      await getSandboxSettings(mockRequest, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        settings: {
          projectId: mockProject.id,
          mode: 'uv',
          cpuLimit: '0.5',
          memLimit: '1g',
          timeout: 1800,
          isActive: true
        }
      });
    });
  });

  describe('POST /api/sandbox/projects/:projectId/sandbox-settings', () => {
    test('should create sandbox settings', async () => {
      const mockProject = {
        id: '550e8400-e29b-41d4-a716-************',
        title: 'Test Project'
      };

      const settingsData = {
        mode: 'fargate',
        cpuLimit: '2.0',
        memLimit: '4g',
        timeout: 7200,
        environment: {
          PYTHON_VERSION: '3.9',
          PACKAGES: ['pandas', 'numpy']
        }
      };

      const mockCreatedSettings = {
        id: '550e8400-e29b-41d4-a716-************',
        projectId: mockProject.id,
        ...settingsData,
        isActive: true
      };

      // Mock Project.findByPk
      Project.findByPk = jest.fn().mockResolvedValue(mockProject);
      
      // Mock ProjectSandboxSettings.create
      ProjectSandboxSettings.create = jest.fn().mockResolvedValue(mockCreatedSettings);

      const createSandboxSettings = async (req, res) => {
        const { projectId } = req.params;
        const settingsData = req.body;

        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(projectId)) {
          return res.status(400).json({
            error: 'Invalid project ID format'
          });
        }

        // Validate required fields
        if (!settingsData.mode || !settingsData.cpuLimit || !settingsData.memLimit) {
          return res.status(400).json({
            error: 'Validation error',
            details: 'Mode, CPU limit, and memory limit are required'
          });
        }

        // Validate mode
        const validModes = ['uv', 'docker', 'fargate'];
        if (!validModes.includes(settingsData.mode)) {
          return res.status(400).json({
            error: 'Validation error',
            details: 'Invalid mode. Must be one of: uv, docker, fargate'
          });
        }

        // Check if project exists
        const project = await Project.findByPk(projectId);
        if (!project) {
          return res.status(404).json({
            error: 'Project not found'
          });
        }

        // Create settings
        const settings = await ProjectSandboxSettings.create({
          projectId,
          ...settingsData,
          isActive: true
        });

        res.status(201).json({
          settings
        });
      };

      mockRequest.params = { projectId: mockProject.id };
      mockRequest.body = settingsData;
      await createSandboxSettings(mockRequest, mockResponse);

      expect(Project.findByPk).toHaveBeenCalledWith(mockProject.id);
      expect(ProjectSandboxSettings.create).toHaveBeenCalledWith({
        projectId: mockProject.id,
        ...settingsData,
        isActive: true
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        settings: mockCreatedSettings
      });
    });

    test('should return 400 for invalid mode', async () => {
      const settingsData = {
        mode: 'invalid-mode',
        cpuLimit: '1.0',
        memLimit: '2g'
      };

      const createSandboxSettings = async (req, res) => {
        const { projectId } = req.params;
        const settingsData = req.body;

        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(projectId)) {
          return res.status(400).json({
            error: 'Invalid project ID format'
          });
        }

        if (!settingsData.mode || !settingsData.cpuLimit || !settingsData.memLimit) {
          return res.status(400).json({
            error: 'Validation error',
            details: 'Mode, CPU limit, and memory limit are required'
          });
        }

        const validModes = ['uv', 'docker', 'fargate'];
        if (!validModes.includes(settingsData.mode)) {
          return res.status(400).json({
            error: 'Validation error',
            details: 'Invalid mode. Must be one of: uv, docker, fargate'
          });
        }

        res.status(201).json({ settings: {} });
      };

      mockRequest.params = { projectId: '550e8400-e29b-41d4-a716-************' };
      mockRequest.body = settingsData;
      await createSandboxSettings(mockRequest, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation error',
        details: 'Invalid mode. Must be one of: uv, docker, fargate'
      });
    });
  });

  describe('PUT /api/sandbox/projects/:projectId/sandbox-settings', () => {
    test('should update sandbox settings', async () => {
      const mockProject = {
        id: '550e8400-e29b-41d4-a716-************',
        title: 'Test Project'
      };

      const mockExistingSettings = {
        id: '550e8400-e29b-41d4-a716-************',
        projectId: mockProject.id,
        mode: 'docker',
        cpuLimit: '1.0',
        memLimit: '2g',
        timeout: 3600,
        isActive: true,
        update: jest.fn().mockReturnThis()
      };

      const updateData = {
        mode: 'fargate',
        cpuLimit: '2.0',
        memLimit: '4g',
        timeout: 7200
      };

      // Mock Project.findByPk
      Project.findByPk = jest.fn().mockResolvedValue(mockProject);
      
      // Mock ProjectSandboxSettings.findOne
      ProjectSandboxSettings.findOne = jest.fn().mockResolvedValue(mockExistingSettings);

      const updateSandboxSettings = async (req, res) => {
        const { projectId } = req.params;
        const updateData = req.body;

        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(projectId)) {
          return res.status(400).json({
            error: 'Invalid project ID format'
          });
        }

        // Check if project exists
        const project = await Project.findByPk(projectId);
        if (!project) {
          return res.status(404).json({
            error: 'Project not found'
          });
        }

        // Get existing settings
        const settings = await ProjectSandboxSettings.findOne({
          where: { projectId, isActive: true }
        });

        if (!settings) {
          return res.status(404).json({
            error: 'Sandbox settings not found'
          });
        }

        // Update settings
        const updatedSettings = await settings.update(updateData);

        res.status(200).json({
          settings: updatedSettings
        });
      };

      mockRequest.params = { projectId: mockProject.id };
      mockRequest.body = updateData;
      await updateSandboxSettings(mockRequest, mockResponse);

      expect(Project.findByPk).toHaveBeenCalledWith(mockProject.id);
      expect(ProjectSandboxSettings.findOne).toHaveBeenCalledWith({
        where: { projectId: mockProject.id, isActive: true }
      });
      expect(mockExistingSettings.update).toHaveBeenCalledWith(updateData);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        settings: mockExistingSettings
      });
    });
  });

  describe('DELETE /api/sandbox/projects/:projectId/sandbox-settings', () => {
    test('should delete sandbox settings', async () => {
      const mockProject = {
        id: '550e8400-e29b-41d4-a716-************',
        title: 'Test Project'
      };

      const mockSettings = {
        id: '550e8400-e29b-41d4-a716-************',
        projectId: mockProject.id,
        mode: 'docker',
        cpuLimit: '1.0',
        memLimit: '2g',
        timeout: 3600,
        isActive: true,
        destroy: jest.fn().mockResolvedValue(true)
      };

      // Mock Project.findByPk
      Project.findByPk = jest.fn().mockResolvedValue(mockProject);
      
      // Mock ProjectSandboxSettings.findOne
      ProjectSandboxSettings.findOne = jest.fn().mockResolvedValue(mockSettings);

      const deleteSandboxSettings = async (req, res) => {
        const { projectId } = req.params;

        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(projectId)) {
          return res.status(400).json({
            error: 'Invalid project ID format'
          });
        }

        // Check if project exists
        const project = await Project.findByPk(projectId);
        if (!project) {
          return res.status(404).json({
            error: 'Project not found'
          });
        }

        // Get settings to delete
        const settings = await ProjectSandboxSettings.findOne({
          where: { projectId, isActive: true }
        });

        if (!settings) {
          return res.status(404).json({
            error: 'Sandbox settings not found'
          });
        }

        // Delete settings
        await settings.destroy();

        res.status(200).json({
          message: 'Sandbox settings deleted successfully'
        });
      };

      mockRequest.params = { projectId: mockProject.id };
      await deleteSandboxSettings(mockRequest, mockResponse);

      expect(Project.findByPk).toHaveBeenCalledWith(mockProject.id);
      expect(ProjectSandboxSettings.findOne).toHaveBeenCalledWith({
        where: { projectId: mockProject.id, isActive: true }
      });
      expect(mockSettings.destroy).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'Sandbox settings deleted successfully'
      });
    });
  });
});
