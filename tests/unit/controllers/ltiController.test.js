import {
  oidcInit,
  oidcCallback,
  getJWKS,
  handleDeepLinking,
  registerPlatform,
  getLTIConfiguration,
  getLTISession,
  cleanupLTISessions
} from '../../../src/controllers/ltiController.js';

import { LtiPlatform, LtiContext, LtiDeployment, LtiLaunchSession, LtiLineItem, LtiResourceLink, User, Course, Grade } from '../../../src/models/associations.js';
import ltiService from '../../../src/services/ltiService.js';
import logger from '../../../src/config/logger.js';

// Mock the models
jest.mock('../../../src/models/associations.js');
jest.mock('../../../src/services/ltiService.js');
jest.mock('../../../src/config/logger.js');

describe('LTI Controller', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      params: {},
      query: {},
      body: {},
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        roles: [{ name: 'instructor' }]
      },
      userRoles: ['instructor'],
      session: {}
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      redirect: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getLTIConfiguration', () => {
    test('should return LTI configuration', async () => {
      await getLTIConfiguration(mockRequest, mockResponse, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        title: 'BITS-DataScience Projects Platform',
        description: 'Interactive data science projects and assignments platform for BITS Pilani',
        target_link_uri: expect.stringContaining('/lti/launch'),
        oidc_initiation_url: expect.stringContaining('/api/lti/oidc/init'),
        public_jwk_url: expect.stringContaining('/.well-known/jwks.json'),
        scopes: expect.arrayContaining([
          'openid',
          'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
          'https://purl.imsglobal.org/spec/lti-ags/scope/score',
          'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
        ]),
        extensions: expect.any(Array),
        custom_fields: expect.any(Object),
        claims: expect.any(Array)
      });
    });
  });

  describe('oidcInit', () => {
    test('should initiate OIDC login successfully', async () => {
      const loginData = {
        iss: 'https://bitspilani.brightspacedemo.com',
        login_hint: 'user-123',
        target_link_uri: 'https://example.com/launch',
        client_id: 'client-123',
        lti_message_hint: 'hint'
      };

      mockRequest.method = 'GET';
      mockRequest.query = loginData;
      
      ltiService.generateOIDCLogin.mockResolvedValue({
        authUrl: 'https://bitspilani.brightspacedemo.com/d2l/auth/api/oauth2/auth?state=abc&nonce=def'
      });

      await oidcInit(mockRequest, mockResponse, mockNext);

      expect(ltiService.generateOIDCLogin).toHaveBeenCalledWith(
        loginData.iss,
        loginData.login_hint,
        loginData.target_link_uri,
        loginData.lti_message_hint,
        loginData.client_id
      );
      expect(mockResponse.redirect).toHaveBeenCalledWith('https://bitspilani.brightspacedemo.com/d2l/auth/api/oauth2/auth?state=abc&nonce=def');
    });

    test('should handle missing parameters', async () => {
      mockRequest.method = 'GET';
      mockRequest.query = {
        iss: 'https://bitspilani.brightspacedemo.com'
        // Missing required parameters
      };

      await oidcInit(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing Required Parameters',
        message: 'iss, login_hint, target_link_uri, and client_id are required',
        required: ['iss', 'login_hint', 'target_link_uri', 'client_id'],
        received: expect.any(Object)
      });
    });
  });

  describe('oidcCallback', () => {
    test('should handle OIDC callback successfully', async () => {
      const callbackData = {
        code: 'auth-code-123',
        state: 'state-123'
      };

      const mockLaunchResult = {
        user: { id: 'user-123', email: '<EMAIL>', name: 'Test User' },
        context: { id: 'context-123', contextId: 'CS101', title: 'Computer Science 101' },
        resourceLink: { id: 'resource-123', resourceLinkId: 'link-123', title: 'Assignment 1' },
        launchData: {
          'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiResourceLinkRequest'
        },
        sessionId: 'session-123'
      };

      mockRequest.body = callbackData;
      ltiService.handleOIDCCallback.mockResolvedValue(mockLaunchResult);

      await oidcCallback(mockRequest, mockResponse, mockNext);

      expect(ltiService.handleOIDCCallback).toHaveBeenCalledWith(callbackData.code, callbackData.state);
      expect(mockRequest.session.user).toEqual(mockLaunchResult.user);
      expect(mockRequest.session.ltiContext).toEqual(mockLaunchResult.context);
      expect(mockRequest.session.ltiResourceLink).toEqual(mockLaunchResult.resourceLink);
      expect(mockRequest.session.ltiLaunchData).toEqual(mockLaunchResult.launchData);
      expect(mockRequest.session.ltiSessionId).toEqual(mockLaunchResult.sessionId);
    });

    test('should handle missing code and state', async () => {
      mockRequest.body = {};

      await oidcCallback(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing Parameters',
        message: 'code and state are required'
      });
    });
  });

  describe('getJWKS', () => {
    test('should return JWKS', async () => {
      const mockJWKS = {
        keys: [
          {
            kty: 'RSA',
            use: 'sig',
            kid: 'bits-datascience-key-1',
            alg: 'RS256',
            n: 'mock-modulus',
            e: 'AQAB'
          }
        ]
      };

      ltiService.generateJWKS.mockReturnValue(mockJWKS);

      await getJWKS(mockRequest, mockResponse, mockNext);

      expect(ltiService.generateJWKS).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockJWKS);
    });
  });

  describe('handleDeepLinking', () => {
    test('should handle deep linking request', async () => {
      const deepLinkData = {
        resourceLinkId: 'link-123',
        contextId: 'context-123',
        title: 'Assignment 1',
        text: 'Complete this assignment'
      };

      mockRequest.body = deepLinkData;
      mockRequest.session = {
        ltiLaunchData: {
          'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiDeepLinkingRequest'
        }
      };

      await handleDeepLinking(mockRequest, mockResponse, mockNext);

      expect(mockResponse.send).toHaveBeenCalledWith(expect.stringContaining('Deep Linking Content Selection'));
    });
  });

  describe('registerPlatform', () => {
    test('should register platform successfully', async () => {
      const platformData = {
        platformName: 'Brightspace D2L',
        platformId: 'https://bitspilani.brightspacedemo.com',
        clientId: 'client-123',
        authLoginUrl: 'https://bitspilani.brightspacedemo.com/d2l/auth/api/oauth2/auth',
        authTokenUrl: 'https://bitspilani.brightspacedemo.com/d2l/auth/token',
        keySetUrl: 'https://bitspilani.brightspacedemo.com/.well-known/jwks.json'
      };

      mockRequest.body = platformData;
      LtiPlatform.create.mockResolvedValue({ id: 'platform-123', ...platformData });

      await registerPlatform(mockRequest, mockResponse, mockNext);

      expect(LtiPlatform.create).toHaveBeenCalledWith(platformData);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Platform registered successfully',
        platform: expect.objectContaining(platformData)
      });
    });
  });

  describe('getLTISession', () => {
    test('should return LTI session data', async () => {
      mockRequest.session = {
        user: { id: 'user-123', email: '<EMAIL>', name: 'Test User', roles: ['student'] },
        ltiContext: { id: 'context-123', contextId: 'CS101', title: 'Computer Science 101', label: 'CS101' },
        ltiResourceLink: { id: 'resource-123', resourceLinkId: 'link-123', title: 'Assignment 1' },
        ltiLaunchData: {
          'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiResourceLinkRequest',
          'https://purl.imsglobal.org/spec/lti/claim/version': '1.3.0',
          'https://purl.imsglobal.org/spec/lti/claim/deployment_id': 'deployment-123'
        }
      };

      await getLTISession(mockRequest, mockResponse, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        session: {
          user: {
            id: 'user-123',
            email: '<EMAIL>',
            name: 'Test User',
            roles: ['student']
          },
          context: {
            id: 'context-123',
            contextId: 'CS101',
            title: 'Computer Science 101',
            label: 'CS101'
          },
          resourceLink: {
            id: 'resource-123',
            resourceLinkId: 'link-123',
            title: 'Assignment 1'
          },
          launchData: {
            messageType: 'LtiResourceLinkRequest',
            version: '1.3.0',
            deploymentId: 'deployment-123'
          }
        }
      });
    });

    test('should return 401 when no LTI session', async () => {
      mockRequest.session = {};

      await getLTISession(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'No LTI Session',
        message: 'No active LTI session found'
      });
    });
  });

  describe('cleanupLTISessions', () => {
    test('should cleanup expired sessions successfully', async () => {
      ltiService.cleanupExpiredSessions.mockResolvedValue(5);

      await cleanupLTISessions(mockRequest, mockResponse, mockNext);

      expect(ltiService.cleanupExpiredSessions).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Cleaned up 5 expired sessions',
        deletedCount: 5
      });
    });

    test('should handle cleanup errors', async () => {
      const error = new Error('Cleanup failed');
      ltiService.cleanupExpiredSessions.mockRejectedValue(error);

      await cleanupLTISessions(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Cleanup Failed',
        message: 'Could not clean up expired sessions'
      });
    });
  });
});
