import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/models/associations.js', () => ({
  Course: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    findByPk: jest.fn(),
    count: jest.fn(),
    findAndCountAll: jest.fn()
  },
  User: {
    findOne: jest.fn(),
    findByPk: jest.fn(),
    count: jest.fn()
  },
  CourseEnrollment: {
    findOne: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn(),
    findAll: jest.fn(),
    count: jest.fn(),
    findAndCountAll: jest.fn()
  },
  Project: {
    findAll: jest.fn(),
    count: jest.fn()
  },
  Submission: {
    findAll: jest.fn(),
    count: jest.fn()
  }
}));

jest.mock('../../../src/config/logger.js', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../../src/middlewares/errorHandler.js', () => ({
  asyncHandler: jest.fn((fn) => fn)
}));

// Mock sequelize
jest.mock('sequelize', () => ({
  Op: {
    or: Symbol('or'),
    iLike: Symbol('iLike')
  },
  fn: jest.fn(),
  col: jest.fn()
}));

// Import the controller
import {
  getCourses,
  getCourseById,
  getCourseEnrollments,
  enrollUserInCourse,
  updateCourseEnrollment,
  getCourseStatistics
} from '../../../src/controllers/courseController.js';

import { Course, User, CourseEnrollment, Project, Submission } from '../../../src/models/associations.js';
import logger from '../../../src/config/logger.js';

describe('Course Controller', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      params: {},
      query: {},
      body: {},
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        roles: [{ name: 'instructor' }]
      },
      userRoles: ['instructor'] // Add userRoles to match controller expectations
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getCourses', () => {
    test('should get all courses with pagination for admin', async () => {
      const mockCourses = [
        { 
          id: 'course-1', 
          name: 'Data Science 101', 
          code: 'DS101',
          instructor: { id: 'instructor-1', name: 'Dr. Smith', email: '<EMAIL>' },
          projects: [],
          enrollments: []
        }
      ];

      mockRequest.query = { page: 1, limit: 10 };
      mockRequest.userRoles = ['admin'];
      
      Course.findAndCountAll.mockResolvedValue({
        count: 1,
        rows: mockCourses
      });
      CourseEnrollment.count.mockResolvedValue(25);

      await getCourses(mockRequest, mockResponse, mockNext);

      expect(Course.findAndCountAll).toHaveBeenCalledWith({
        where: {},
        include: expect.arrayContaining([
          expect.objectContaining({
            model: User,
            as: 'instructor',
            attributes: ['id', 'name', 'email']
          }),
          expect.objectContaining({
            model: Project,
            as: 'projects',
            attributes: ['id', 'title', 'status'],
            separate: true,
            limit: 5,
            order: [['created_at', 'DESC']]
          })
        ]),
        limit: 10,
        offset: 0,
        order: [['created_at', 'DESC']]
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          courses: expect.any(Array),
          pagination: expect.objectContaining({
            currentPage: 1,
            totalPages: 1,
            totalItems: 1,
            itemsPerPage: 10
          })
        })
      });
    });

    test('should get courses with search filter', async () => {
      mockRequest.query = { search: 'data science' };
      mockRequest.userRoles = ['admin'];
      
      Course.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getCourses(mockRequest, mockResponse, mockNext);

      expect(Course.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.any(Object)
        })
      );
    });

    test('should get courses with term filter', async () => {
      mockRequest.query = { term: 'Fall 2024' };
      mockRequest.userRoles = ['admin'];
      
      Course.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getCourses(mockRequest, mockResponse, mockNext);

      expect(Course.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { term: 'Fall 2024' }
        })
      );
    });

    test('should get courses with status filter', async () => {
      mockRequest.query = { status: 'active' };
      mockRequest.userRoles = ['admin'];
      
      Course.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getCourses(mockRequest, mockResponse, mockNext);

      expect(Course.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { status: 'active' }
        })
      );
    });

    test('should filter courses for non-admin users', async () => {
      mockRequest.query = { page: 1, limit: 10 };
      mockRequest.userRoles = ['student'];
      
      Course.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getCourses(mockRequest, mockResponse, mockNext);

      expect(Course.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          include: expect.arrayContaining([
            expect.objectContaining({
              model: CourseEnrollment,
              as: 'enrollments',
              where: { user_id: 'user-123' },
              required: true
            })
          ])
        })
      );
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      Course.findAndCountAll.mockRejectedValue(error);

      try {
        await getCourses(mockRequest, mockResponse, mockNext);
      } catch (err) {
        expect(err).toBe(error);
      }
    });
  });

  describe('getCourseById', () => {
    test('should get course by ID with full details', async () => {
      const mockCourse = {
        id: 'course-123',
        name: 'Data Science 101',
        code: 'DS101',
        description: 'Introduction to Data Science',
        instructor_id: 'instructor-123',
        instructor: {
          id: 'instructor-123',
          name: 'Dr. Smith',
          email: '<EMAIL>',
          profile_picture: 'profile.jpg'
        },
        projects: [],
        enrollments: []
      };

      mockRequest.params = { id: 'course-123' };
      mockRequest.userRoles = ['admin'];
      Course.findByPk.mockResolvedValue(mockCourse);

      await getCourseById(mockRequest, mockResponse, mockNext);

      expect(Course.findByPk).toHaveBeenCalledWith('course-123', {
        include: [
          {
            model: User,
            as: 'instructor',
            attributes: ['id', 'name', 'email', 'profile_picture']
          },
          {
            model: Project,
            as: 'projects',
            include: [{
              model: User,
              as: 'creator',
              attributes: ['id', 'name', 'email']
            }]
          },
          {
            model: CourseEnrollment,
            as: 'enrollments',
            include: [{
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'email', 'profile_picture']
            }]
          }
        ]
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        course: expect.objectContaining({
          id: 'course-123',
          name: 'Data Science 101',
          code: 'DS101'
        })
      });
    });

    test('should return 404 when course not found', async () => {
      mockRequest.params = { id: 'course-123' };
      Course.findByPk.mockResolvedValue(null);

      await getCourseById(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Course not found'
      });
    });

    test('should return 403 when user has no access', async () => {
      const mockCourse = {
        id: 'course-123',
        name: 'Data Science 101',
        instructor_id: 'other-instructor-123',
        enrollments: []
      };

      mockRequest.params = { id: 'course-123' };
      mockRequest.userRoles = ['student'];
      Course.findByPk.mockResolvedValue(mockCourse);

      await getCourseById(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access Denied',
        message: 'You do not have permission to view this course'
      });
    });

    test('should allow access for instructor of the course', async () => {
      const mockCourse = {
        id: 'course-123',
        name: 'Data Science 101',
        instructor_id: 'user-123',
        instructor: { id: 'user-123', name: 'Dr. Smith' },
        projects: [],
        enrollments: []
      };

      mockRequest.params = { id: 'course-123' };
      mockRequest.userRoles = ['instructor'];
      Course.findByPk.mockResolvedValue(mockCourse);

      await getCourseById(mockRequest, mockResponse, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        course: expect.any(Object)
      });
    });
  });

  describe('getCourseEnrollments', () => {
    test('should get course enrollments successfully', async () => {
      const enrollments = [
        {
          id: 'enrollment-1',
          role_in_course: 'student',
          enrollment_status: 'active',
          enrolled_at: new Date(),
          user: { id: 'user-1', name: 'John Doe', email: '<EMAIL>' }
        }
      ];

      const course = { id: 'course-123', instructor_id: 'user-123' };

      mockRequest.params = { id: 'course-123' };
      mockRequest.query = { page: 1, limit: 10 };
      mockRequest.userRoles = ['instructor'];
      
      Course.findByPk.mockResolvedValue(course);
      CourseEnrollment.findAndCountAll.mockResolvedValue({
        count: 1,
        rows: enrollments
      });

      await getCourseEnrollments(mockRequest, mockResponse, mockNext);

      expect(Course.findByPk).toHaveBeenCalledWith('course-123');
      expect(CourseEnrollment.findAndCountAll).toHaveBeenCalledWith({
        where: { course_id: 'course-123' },
        include: [{
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'profile_picture', 'last_login']
        }],
        limit: 10,
        offset: 0,
        order: [['enrolled_at', 'DESC']]
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          enrollments: expect.any(Array),
          pagination: expect.any(Object)
        })
      });
    });

    test('should return 404 when course not found', async () => {
      mockRequest.params = { id: 'course-123' };
      Course.findByPk.mockResolvedValue(null);

      await getCourseEnrollments(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Course not found'
      });
    });

    test('should return 403 when user has no permission', async () => {
      const course = { id: 'course-123', instructor_id: 'other-instructor-123' };
      mockRequest.params = { id: 'course-123' };
      mockRequest.userRoles = ['student'];
      
      Course.findByPk.mockResolvedValue(course);

      await getCourseEnrollments(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access Denied',
        message: 'You do not have permission to view course enrollments'
      });
    });

    test('should filter enrollments by role', async () => {
      const course = { id: 'course-123', instructor_id: 'user-123' };
      mockRequest.params = { id: 'course-123' };
      mockRequest.query = { role: 'student' };
      mockRequest.userRoles = ['instructor'];
      
      Course.findByPk.mockResolvedValue(course);
      CourseEnrollment.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getCourseEnrollments(mockRequest, mockResponse, mockNext);

      expect(CourseEnrollment.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { course_id: 'course-123', role_in_course: 'student' }
        })
      );
    });

    test('should filter enrollments by status', async () => {
      const course = { id: 'course-123', instructor_id: 'user-123' };
      mockRequest.params = { id: 'course-123' };
      mockRequest.query = { status: 'active' };
      mockRequest.userRoles = ['instructor'];
      
      Course.findByPk.mockResolvedValue(course);
      CourseEnrollment.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getCourseEnrollments(mockRequest, mockResponse, mockNext);

      expect(CourseEnrollment.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { course_id: 'course-123', enrollment_status: 'active' }
        })
      );
    });
  });

  describe('enrollUserInCourse', () => {
    test('should enroll user in course successfully', async () => {
      const course = {
        id: 'course-123',
        name: 'Data Science 101'
      };

      const user = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'John Doe'
      };

      const enrollment = {
        id: 'enrollment-123',
        course_id: 'course-123',
        user_id: 'user-123',
        role_in_course: 'student',
        enrollment_status: 'active',
        enrolled_at: new Date()
      };

      mockRequest.params = { id: 'course-123' };
      mockRequest.body = { userId: 'user-123', roleInCourse: 'student' };
      Course.findByPk.mockResolvedValue(course);
      User.findByPk.mockResolvedValue(user);
      CourseEnrollment.findOne.mockResolvedValue(null); // Not already enrolled
      CourseEnrollment.create.mockResolvedValue(enrollment);

      await enrollUserInCourse(mockRequest, mockResponse, mockNext);

      expect(Course.findByPk).toHaveBeenCalledWith('course-123');
      expect(User.findByPk).toHaveBeenCalledWith('user-123');
      expect(CourseEnrollment.findOne).toHaveBeenCalledWith({
        where: { course_id: 'course-123', user_id: 'user-123' }
      });
      expect(CourseEnrollment.create).toHaveBeenCalledWith({
        course_id: 'course-123',
        user_id: 'user-123',
        role_in_course: 'student',
        enrollment_status: 'active'
      });
      expect(logger.info).toHaveBeenCalledWith(
        `User enrolled: ${user.email} in course ${course.name} by ${mockRequest.user.email}`
      );
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'User enrolled successfully',
        enrollment: expect.objectContaining({
          id: 'enrollment-123',
          roleInCourse: 'student',
          enrollmentStatus: 'active'
        })
      });
    });

    test('should return error if course not found', async () => {
      mockRequest.params = { id: 'course-123' };
      mockRequest.body = { userId: 'user-123' };
      Course.findByPk.mockResolvedValue(null);

      await enrollUserInCourse(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Course not found'
      });
    });

    test('should return error if user not found', async () => {
      const course = { id: 'course-123', title: 'Data Science 101' };
      mockRequest.params = { id: 'course-123' };
      mockRequest.body = { userId: 'user-123' };
      Course.findByPk.mockResolvedValue(course);
      User.findByPk.mockResolvedValue(null);

      await enrollUserInCourse(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'User not found'
      });
    });

    test('should return error if user already enrolled', async () => {
      const course = { id: 'course-123', title: 'Data Science 101' };
      const user = { id: 'user-123', name: 'John Doe' };
      const existingEnrollment = { id: 'enrollment-123' };

      mockRequest.params = { id: 'course-123' };
      mockRequest.body = { userId: 'user-123' };
      Course.findByPk.mockResolvedValue(course);
      User.findByPk.mockResolvedValue(user);
      CourseEnrollment.findOne.mockResolvedValue(existingEnrollment);

      await enrollUserInCourse(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(409);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Conflict',
        message: 'User is already enrolled in this course'
      });
    });

    test('should return error if userId is missing', async () => {
      mockRequest.params = { id: 'course-123' };
      mockRequest.body = {};

      await enrollUserInCourse(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation Error',
        message: 'User ID is required'
      });
    });
  });

  describe('updateCourseEnrollment', () => {
    test('should update course enrollment successfully', async () => {
      const enrollment = {
        id: 'enrollment-123',
        course_id: 'course-123',
        user_id: 'user-123',
        role_in_course: 'ta',
        enrollment_status: 'active',
        enrolled_at: new Date(),
        update: jest.fn().mockImplementation(function(updateData) {
          Object.assign(this, updateData);
          return Promise.resolve(this);
        }),
        user: { id: 'user-123', name: 'John Doe', email: '<EMAIL>' },
        course: { id: 'course-123', name: 'Data Science 101', instructor_id: 'user-123' }
      };

      mockRequest.params = { id: 'course-123', enrollmentId: 'enrollment-123' };
      mockRequest.body = { roleInCourse: 'ta' };
      mockRequest.userRoles = ['instructor'];
      
      CourseEnrollment.findOne.mockResolvedValue(enrollment);

      await updateCourseEnrollment(mockRequest, mockResponse, mockNext);

      expect(CourseEnrollment.findOne).toHaveBeenCalledWith({
        where: { id: 'enrollment-123', course_id: 'course-123' },
        include: [
          { model: User, as: 'user', attributes: ['id', 'name', 'email'] },
          { model: Course, as: 'course', attributes: ['id', 'name', 'instructor_id'] }
        ]
      });
      expect(enrollment.update).toHaveBeenCalledWith({
        role_in_course: 'ta'
      });
      expect(logger.info).toHaveBeenCalledWith(
        `Enrollment updated: ${enrollment.user.email} in course ${enrollment.course.name} by ${mockRequest.user.email}`
      );
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Enrollment updated successfully',
        enrollment: expect.objectContaining({
          id: 'enrollment-123',
          roleInCourse: 'ta',
          enrollmentStatus: 'active',
          enrolledAt: expect.any(Date)
        })
      });
    });

    test('should return 404 when enrollment not found', async () => {
      mockRequest.params = { id: 'course-123', enrollmentId: 'enrollment-123' };
      CourseEnrollment.findOne.mockResolvedValue(null);

      await updateCourseEnrollment(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Enrollment not found'
      });
    });

    test('should return 403 when user has no permission', async () => {
      const enrollment = {
        id: 'enrollment-123',
        user: { id: 'user-123', name: 'John Doe', email: '<EMAIL>' },
        course: { id: 'course-123', name: 'Data Science 101', instructor_id: 'other-instructor-123' }
      };

      mockRequest.params = { id: 'course-123', enrollmentId: 'enrollment-123' };
      mockRequest.userRoles = ['instructor'];
      
      CourseEnrollment.findOne.mockResolvedValue(enrollment);

      await updateCourseEnrollment(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access Denied',
        message: 'You do not have permission to update this enrollment'
      });
    });

    test('should update multiple fields', async () => {
      const enrollment = {
        id: 'enrollment-123',
        update: jest.fn().mockResolvedValue({
          id: 'enrollment-123',
          role_in_course: 'ta',
          enrollment_status: 'completed',
          final_grade: 85,
          enrolled_at: new Date()
        }),
        user: { id: 'user-123', name: 'John Doe', email: '<EMAIL>' },
        course: { id: 'course-123', name: 'Data Science 101', instructor_id: 'user-123' }
      };

      mockRequest.params = { id: 'course-123', enrollmentId: 'enrollment-123' };
      mockRequest.body = { 
        roleInCourse: 'ta', 
        enrollmentStatus: 'completed', 
        finalGrade: 85 
      };
      mockRequest.userRoles = ['instructor'];
      
      CourseEnrollment.findOne.mockResolvedValue(enrollment);

      await updateCourseEnrollment(mockRequest, mockResponse, mockNext);

      expect(enrollment.update).toHaveBeenCalledWith({
        role_in_course: 'ta',
        enrollment_status: 'completed',
        final_grade: 85
      });
    });
  });

  describe('getCourseStatistics', () => {
    test('should get course statistics successfully', async () => {
      const course = {
        id: 'course-123',
        name: 'Data Science 101',
        instructor_id: 'user-123'
      };

      mockRequest.params = { id: 'course-123' };
      mockRequest.userRoles = ['instructor'];
      
      Course.findByPk.mockResolvedValue(course);
      CourseEnrollment.count
        .mockResolvedValueOnce(25) // totalEnrollments
        .mockResolvedValueOnce(20); // activeEnrollments
      CourseEnrollment.findAll.mockResolvedValue([
        { role_in_course: 'student', getDataValue: () => 20 },
        { role_in_course: 'ta', getDataValue: () => 5 }
      ]);
      Project.count
        .mockResolvedValueOnce(10) // totalProjects
        .mockResolvedValueOnce(8); // publishedProjects

      await getCourseStatistics(mockRequest, mockResponse, mockNext);

      expect(Course.findByPk).toHaveBeenCalledWith('course-123');
      expect(CourseEnrollment.count).toHaveBeenCalledWith({
        where: { course_id: 'course-123' }
      });
      expect(CourseEnrollment.count).toHaveBeenCalledWith({
        where: { course_id: 'course-123', enrollment_status: 'active' }
      });
      expect(Project.count).toHaveBeenCalledWith({
        where: { course_id: 'course-123' }
      });
      expect(Project.count).toHaveBeenCalledWith({
        where: { course_id: 'course-123', status: 'published' }
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        statistics: expect.objectContaining({
          enrollments: expect.objectContaining({
            total: 25,
            active: 20,
            byRole: expect.any(Array)
          }),
          projects: expect.objectContaining({
            total: 10,
            published: 8
          })
        })
      });
    });

    test('should return 404 when course not found', async () => {
      mockRequest.params = { id: 'course-123' };
      Course.findByPk.mockResolvedValue(null);

      await getCourseStatistics(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Course not found'
      });
    });

    test('should return 403 when user has no permission', async () => {
      const course = {
        id: 'course-123',
        name: 'Data Science 101',
        instructor_id: 'other-instructor-123'
      };

      mockRequest.params = { id: 'course-123' };
      mockRequest.userRoles = ['student'];
      
      Course.findByPk.mockResolvedValue(course);

      await getCourseStatistics(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access Denied',
        message: 'You do not have permission to view course statistics'
      });
    });
  });
});
