import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/models/associations.js', () => ({
  Project: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    findAndCountAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    findByPk: jest.fn(),
    count: jest.fn()
  },
  User: {
    findOne: jest.fn(),
    findByPk: jest.fn()
  },
  Course: {
    findByPk: jest.fn()
  },
  CourseEnrollment: {
    findOne: jest.fn(),
    findAll: jest.fn()
  },
  Submission: {
    findAll: jest.fn(),
    count: jest.fn(),
    findOne: jest.fn()
  },
  Grade: {
    findAll: jest.fn()
  },
  Rubric: {
    create: jest.fn()
  },
  ProjectSandboxSettings: {
    findOne: jest.fn(),
    create: jest.fn()
  }
}));

jest.mock('../../../src/config/logger.js', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../../src/middlewares/errorHandler.js', () => ({
  asyncHandler: jest.fn((fn) => fn)
}));

// Import the controller functions that actually exist
import {
  getProjects,
  getProjectById,
  createProject,
  updateProject,
  duplicateProject,
  deleteProject,
  publishProject,
  getProjectStatistics
} from '../../../src/controllers/projectController.js';

import { Project, User, Course, CourseEnrollment, Submission, ProjectSandboxSettings, Grade, Rubric } from '../../../src/models/associations.js';
import logger from '../../../src/config/logger.js';

describe('Project Controller', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      params: {},
      query: {},
      body: {},
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        roles: [{ name: 'instructor' }]
      },
      userRoles: ['instructor'] // Add userRoles to match controller expectations
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getProjects', () => {
    test('should get all projects with pagination', async () => {
      const mockProjects = [
        { 
          id: 'project-1', 
          title: 'Data Analysis Project', 
          description: 'Analyze dataset',
          course: {
            id: 'course-1',
            name: 'Data Science 101',
            code: 'DS101',
            term: 'Fall 2024',
            instructor: {
              id: 'instructor-1',
              name: 'Dr. Smith',
              email: '<EMAIL>'
            }
          },
          creator: {
            id: 'creator-1',
            name: 'Dr. Smith',
            email: '<EMAIL>'
          }
        },
        { 
          id: 'project-2', 
          title: 'ML Model Project', 
          description: 'Build ML model',
          course: {
            id: 'course-2',
            name: 'Machine Learning',
            code: 'ML101',
            term: 'Fall 2024',
            instructor: {
              id: 'instructor-2',
              name: 'Dr. Johnson',
              email: '<EMAIL>'
            }
          },
          creator: {
            id: 'creator-2',
            name: 'Dr. Johnson',
            email: '<EMAIL>'
          }
        }
      ];

      mockRequest.query = { page: 1, limit: 10 };
      Project.findAndCountAll.mockResolvedValue({
        count: 2,
        rows: mockProjects
      });

      await getProjects(mockRequest, mockResponse, mockNext);

      expect(Project.findAndCountAll).toHaveBeenCalledWith(expect.objectContaining({
        where: {},
        limit: 10,
        offset: 0,
        order: [['created_at', 'DESC']]
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          projects: expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(String),
              title: expect.any(String),
              description: expect.any(String)
            })
          ]),
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 2,
            itemsPerPage: 10
          }
        }
      });
    });

    test('should filter projects by course ID', async () => {
      mockRequest.query = { courseId: 'course-123' };
      Project.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getProjects(mockRequest, mockResponse, mockNext);

      expect(Project.findAndCountAll).toHaveBeenCalledWith(expect.objectContaining({
        where: { course_id: 'course-123' },
        limit: 10,
        offset: 0,
        order: [['created_at', 'DESC']]
      }));
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      Project.findAndCountAll.mockRejectedValue(error);

      try {
        await getProjects(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });

  describe('getProjectById', () => {
    test('should get project by ID', async () => {
      const mockProject = {
        id: 'project-123',
        title: 'Test Project',
        description: 'Test Description',
        course: {
          id: 'course-123',
          instructor_id: 'user-123',
          name: 'Test Course',
          code: 'TC101',
          term: 'Fall 2024'
        },
        creator: {
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>'
        },
        creator_id: 'user-123'
      };

      mockRequest.params = { id: 'project-123' };
      Project.findByPk.mockResolvedValue(mockProject);
      Submission.findOne.mockResolvedValue(null);

      await getProjectById(mockRequest, mockResponse, mockNext);

      expect(Project.findByPk).toHaveBeenCalledWith('project-123', expect.any(Object));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        project: expect.any(Object)
      });
    });

    test('should return 404 when project not found', async () => {
      mockRequest.params = { id: 'project-123' };
      Project.findByPk.mockResolvedValue(null);

      await getProjectById(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Project not found'
      });
    });
  });

  describe('createProject', () => {
    test('should create a new project', async () => {
      const projectData = {
        title: 'New Project',
        description: 'New Description',
        courseId: 'course-123'
      };

      const createdProject = {
        id: 'project-123',
        ...projectData
      };

      mockRequest.body = projectData;
      Course.findByPk.mockResolvedValue({ 
        id: 'course-123',
        instructor_id: 'user-123'
      });
      Project.create.mockResolvedValue(createdProject);

      await createProject(mockRequest, mockResponse, mockNext);

      expect(Course.findByPk).toHaveBeenCalledWith('course-123');
      expect(Project.create).toHaveBeenCalledWith(expect.objectContaining({
        title: projectData.title,
        description: projectData.description,
        course_id: projectData.courseId,
        creator_id: 'user-123'
      }));
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Project created successfully',
        project: expect.any(Object)
      });
    });

    test('should return 400 when course not found', async () => {
      mockRequest.body = { courseId: 'course-123' };
      Course.findByPk.mockResolvedValue(null);

      await createProject(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation Error',
        message: 'Title, description, and course ID are required'
      });
    });
  });

  describe('updateProject', () => {
    test('should update project successfully', async () => {
      const mockProject = {
        id: 'project-123',
        title: 'Updated Project',
        course: {
          id: 'course-123',
          instructor_id: 'user-123'
        },
        creator: {
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>'
        },
        creator_id: 'user-123',
        update: jest.fn().mockResolvedValue(true)
      };

      mockRequest.params = { id: 'project-123' };
      mockRequest.body = { title: 'Updated Project' };
      Project.findByPk.mockResolvedValue(mockProject);

      await updateProject(mockRequest, mockResponse, mockNext);

      expect(Project.findByPk).toHaveBeenCalledWith('project-123', expect.any(Object));
      expect(mockProject.update).toHaveBeenCalledWith({ title: 'Updated Project' });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Project updated successfully',
        project: expect.any(Object)
      });
    });

    test('should return 404 when project not found', async () => {
      mockRequest.params = { id: 'project-123' };
      Project.findByPk.mockResolvedValue(null);

      await updateProject(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Project not found'
      });
    });
  });

  describe('duplicateProject', () => {
    test('should duplicate project successfully', async () => {
      const originalProject = {
        id: 'project-123',
        title: 'Original Project',
        description: 'Original Description',
        course_id: 'course-123',
        course: {
          id: 'course-123',
          instructor_id: 'user-123'
        },
        creator: {
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>'
        },
        creator_id: 'user-123',
        rubrics: []
      };

      const duplicatedProject = {
        id: 'project-456',
        title: 'Original Project (Copy)',
        description: 'Original Description',
        course_id: 'course-456'
      };

      mockRequest.params = { id: 'project-123' };
      mockRequest.body = { 
        courseId: 'course-456',
        title: 'Original Project (Copy)'
      };
      Project.findByPk.mockResolvedValue(originalProject);
      Course.findByPk.mockResolvedValue({ 
        id: 'course-456',
        instructor_id: 'user-123'
      });
      Project.create.mockResolvedValue(duplicatedProject);

      await duplicateProject(mockRequest, mockResponse, mockNext);

      expect(Project.findByPk).toHaveBeenCalledWith('project-123', expect.any(Object));
      expect(Course.findByPk).toHaveBeenCalledWith('course-456');
      expect(Project.create).toHaveBeenCalledWith(expect.objectContaining({
        title: 'Original Project (Copy)',
        description: originalProject.description,
        course_id: 'course-456',
        creator_id: 'user-123'
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Project duplicated successfully',
        project: expect.any(Object)
      });
    });

    test('should return 404 when project not found', async () => {
      mockRequest.params = { id: 'project-123' };
      Project.findByPk.mockResolvedValue(null);

      await duplicateProject(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Project not found'
      });
    });
  });

  describe('deleteProject', () => {
    test('should delete project successfully', async () => {
      const mockProject = {
        id: 'project-123',
        course: {
          id: 'course-123',
          instructor_id: 'user-123'
        },
        creator: {
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>'
        },
        creator_id: 'user-123',
        destroy: jest.fn().mockResolvedValue(true)
      };

      mockRequest.params = { id: 'project-123' };
      Project.findByPk.mockResolvedValue(mockProject);

      await deleteProject(mockRequest, mockResponse, mockNext);

      expect(Project.findByPk).toHaveBeenCalledWith('project-123', expect.any(Object));
      expect(mockProject.destroy).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Project deleted successfully'
      });
    });

    test('should return 404 when project not found', async () => {
      mockRequest.params = { id: 'project-123' };
      Project.findByPk.mockResolvedValue(null);

      await deleteProject(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Project not found'
      });
    });
  });

  describe('publishProject', () => {
    test('should publish project successfully', async () => {
      const mockProject = {
        id: 'project-123',
        status: 'draft',
        course: {
          id: 'course-123',
          instructor_id: 'user-123'
        },
        creator: {
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>'
        },
        creator_id: 'user-123',
        update: jest.fn().mockResolvedValue(true)
      };

      mockRequest.params = { id: 'project-123' };
      Project.findByPk.mockResolvedValue(mockProject);

      await publishProject(mockRequest, mockResponse, mockNext);

      expect(Project.findByPk).toHaveBeenCalledWith('project-123', expect.any(Object));
      expect(mockProject.update).toHaveBeenCalledWith({ status: 'published' });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Project published successfully',
        project: expect.any(Object)
      });
    });

    test('should return 404 when project not found', async () => {
      mockRequest.params = { id: 'project-123' };
      Project.findByPk.mockResolvedValue(null);

      await publishProject(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Project not found'
      });
    });
  });

  describe('getProjectStatistics', () => {
    test('should get project statistics successfully', async () => {
      const mockProject = {
        id: 'project-123',
        course: {
          id: 'course-123',
          instructor_id: 'user-123'
        },
        creator_id: 'user-123'
      };

      mockRequest.params = { id: 'project-123' };
      Project.findByPk.mockResolvedValue(mockProject);
      Submission.count = jest.fn().mockResolvedValue(10);
      Submission.findAll = jest.fn().mockResolvedValue([
        { status: 'submitted', getDataValue: () => '5' },
        { status: 'graded', getDataValue: () => '3' }
      ]);
      Grade.findAll = jest.fn().mockResolvedValue([
        { 
          getDataValue: (field) => {
            if (field === 'averagePercentage') return '85.5';
            if (field === 'minPercentage') return '70.0';
            if (field === 'maxPercentage') return '95.0';
            return '0';
          }
        }
      ]);

      await getProjectStatistics(mockRequest, mockResponse, mockNext);

      expect(Project.findByPk).toHaveBeenCalledWith('project-123', expect.any(Object));
      expect(Submission.count).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        statistics: expect.objectContaining({
          submissions: expect.any(Object),
          grades: expect.any(Object)
        })
      });
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      Project.findByPk.mockRejectedValue(error);

      try {
        await getProjectStatistics(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });
});
