import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('bcryptjs');
jest.mock('jsonwebtoken');
jest.mock('passport');

// Mock models and associations
jest.mock('../../../src/models/associations.js', () => ({
  User: {
    findOne: jest.fn(),
    findByPk: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    prototype: {
      comparePassword: jest.fn().mockResolvedValue(true),
      updateLastLogin: jest.fn().mockResolvedValue(),
      toJSON: jest.fn().mockReturnValue({})
    }
  },
  Role: {
    findOne: jest.fn(),
    findByPk: jest.fn(),
    create: jest.fn()
  },
  Permission: {
    findAll: jest.fn()
  }
}));

jest.mock('../../../src/config/logger.js', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../../src/middlewares/auth.js', () => ({
  generateToken: jest.fn().mockReturnValue('mock-jwt-token')
}));

jest.mock('../../../src/middlewares/errorHandler.js', () => ({
  asyncHandler: jest.fn((fn) => fn)
}));

import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import passport from 'passport';
import { User, Role } from '../../../src/models/associations.js';
import { generateToken } from '../../../src/middlewares/auth.js';
import logger from '../../../src/config/logger.js';

// Mock token blacklist service
jest.mock('../../../src/services/tokenBlacklistService.js', () => ({
  blacklistToken: jest.fn().mockResolvedValue(),
  isTokenBlacklisted: jest.fn().mockResolvedValue(false),
  getBlacklistStats: jest.fn().mockResolvedValue({
    totalBlacklistedTokens: 10,
    memoryUsage: 1024000
  })
}));

import tokenBlacklistService from '../../../src/services/tokenBlacklistService.js';

// Import the controller functions
import {
  googleAuth,
  googleCallback,
  login,
  getCurrentUser,
  refreshToken,
  logout,
  logoutAllDevices,
  getLogoutStats,
  updatePreferences
} from '../../../src/controllers/authController.js';

describe('Auth Controller', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      body: {},
      query: {},
      params: {},
      user: null,
      session: {},
      headers: {}
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      redirect: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('googleCallback', () => {
    test('should handle successful Google authentication', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        updateLastLogin: jest.fn().mockResolvedValue()
      };

      // Mock passport.authenticate to return a function that calls the callback with success
      passport.authenticate = jest.fn().mockReturnValue((req, res, next) => {
        // Simulate the callback being called with success
        const callback = passport.authenticate.mock.calls[0][2];
        callback(null, mockUser);
      });

      await googleCallback(mockRequest, mockResponse, mockNext);

      expect(passport.authenticate).toHaveBeenCalledWith('google', { session: false }, expect.any(Function));
      expect(generateToken).toHaveBeenCalledWith(mockUser);
      expect(mockUser.updateLastLogin).toHaveBeenCalled();
      expect(mockResponse.redirect).toHaveBeenCalledWith(
        expect.stringContaining('auth/callback?token=mock-jwt-token')
      );
    });

    test('should handle authentication failure', async () => {
      const mockError = new Error('Authentication failed');

      // Mock passport.authenticate to return a function that calls the callback with error
      passport.authenticate = jest.fn().mockReturnValue((req, res, next) => {
        // Simulate the callback being called with error
        const callback = passport.authenticate.mock.calls[0][2];
        callback(mockError, null);
      });

      await googleCallback(mockRequest, mockResponse, mockNext);

      expect(passport.authenticate).toHaveBeenCalledWith('google', { session: false }, expect.any(Function));
      expect(logger.error).toHaveBeenCalledWith('Google OAuth error:', mockError);
      expect(mockResponse.redirect).toHaveBeenCalledWith(
        expect.stringContaining('login?error=oauth_error')
      );
    });

    test('should handle missing user', async () => {
      // Mock passport.authenticate to return a function that calls the callback without user
      passport.authenticate = jest.fn().mockReturnValue((req, res, next) => {
        // Simulate the callback being called without user
        const callback = passport.authenticate.mock.calls[0][2];
        callback(null, null);
      });

      await googleCallback(mockRequest, mockResponse, mockNext);

      expect(passport.authenticate).toHaveBeenCalledWith('google', { session: false }, expect.any(Function));
      expect(mockResponse.redirect).toHaveBeenCalledWith(
        expect.stringContaining('login?error=authentication_failed')
      );
    });
  });

  describe('getCurrentUser', () => {
    test('should get current user profile', async () => {
      const mockUser = {
        id: 'user-123',
        name: 'Test User',
        email: '<EMAIL>',
        profile_picture: 'avatar.jpg',
        last_login: new Date(),
        status: 'active',
        preferences: { theme: 'dark' },
        roles: [
          {
            id: 'role-1',
            name: 'student',
            permissions: [
              { key: 'read:projects' },
              { key: 'write:submissions' }
            ]
          }
        ]
      };

      mockRequest.user = mockUser;

      await getCurrentUser(mockRequest, mockResponse, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        user: {
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>',
          profilePicture: 'avatar.jpg',
          lastLogin: expect.any(Date),
          status: 'active',
          preferences: { theme: 'dark' },
          roles: [
            {
              id: 'role-1',
              name: 'student',
              permissions: ['read:projects', 'write:submissions']
            }
          ]
        }
      });
    });
  });

  describe('refreshToken', () => {
    test('should refresh token successfully', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.user = mockUser;

      await refreshToken(mockRequest, mockResponse, mockNext);

      expect(generateToken).toHaveBeenCalledWith(mockUser);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        token: 'mock-jwt-token'
      });
    });
  });

  describe('logout', () => {
    test('should logout user successfully with token blacklisting', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      const mockToken = 'mock-jwt-token';

      mockRequest.user = mockUser;
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      await logout(mockRequest, mockResponse, mockNext);

      expect(tokenBlacklistService.blacklistToken).toHaveBeenCalledWith(mockToken);
      expect(logger.info).toHaveBeenCalledWith(`Token blacklisted for user: ${mockUser.email}`);
      expect(logger.info).toHaveBeenCalledWith(`User logged out: ${mockUser.email}`);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Logged out successfully',
        timestamp: expect.any(String)
      });
    });

    test('should logout user successfully without token', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.user = mockUser;
      mockRequest.headers = {};

      await logout(mockRequest, mockResponse, mockNext);

      expect(tokenBlacklistService.blacklistToken).not.toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(`User logged out: ${mockUser.email}`);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Logged out successfully',
        timestamp: expect.any(String)
      });
    });

    test('should handle blacklisting error gracefully', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      const mockToken = 'mock-jwt-token';

      mockRequest.user = mockUser;
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      // Mock blacklisting to fail
      tokenBlacklistService.blacklistToken.mockRejectedValue(new Error('Redis error'));

      await logout(mockRequest, mockResponse, mockNext);

      expect(tokenBlacklistService.blacklistToken).toHaveBeenCalledWith(mockToken);
      expect(logger.error).toHaveBeenCalledWith('Logout error:', expect.any(Error));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Logged out successfully',
        timestamp: expect.any(String)
      });
    });
  });

  describe('logoutAllDevices', () => {
    test('should logout from all devices successfully', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.user = mockUser;

      await logoutAllDevices(mockRequest, mockResponse, mockNext);

      expect(logger.info).toHaveBeenCalledWith(`User logged out from all devices: ${mockUser.email}`);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Logged out from all devices successfully',
        timestamp: expect.any(String)
      });
    });
  });

  describe('getLogoutStats', () => {
    test('should get logout stats for admin user', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.user = mockUser;
      mockRequest.userPermissions = ['admin:read'];
      mockRequest.userRoles = ['admin'];

      await getLogoutStats(mockRequest, mockResponse, mockNext);

      expect(tokenBlacklistService.getBlacklistStats).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        stats: {
          totalBlacklistedTokens: 10,
          memoryUsage: 1024000
        },
        timestamp: expect.any(String)
      });
    });

    test('should reject non-admin users', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.user = mockUser;
      mockRequest.userPermissions = ['student:read'];
      mockRequest.userRoles = ['student'];

      await getLogoutStats(mockRequest, mockResponse, mockNext);

      expect(tokenBlacklistService.getBlacklistStats).not.toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Access denied',
        message: 'Admin permission required'
      });
    });

    test('should handle stats retrieval error', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.user = mockUser;
      mockRequest.userPermissions = ['admin:read'];
      mockRequest.userRoles = ['admin'];

      // Mock stats retrieval to fail
      tokenBlacklistService.getBlacklistStats.mockRejectedValue(new Error('Redis error'));

      await getLogoutStats(mockRequest, mockResponse, mockNext);

      expect(logger.error).toHaveBeenCalledWith('Get logout stats error:', expect.any(Error));
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error',
        message: 'Failed to get logout statistics'
      });
    });
  });

  describe('updatePreferences', () => {
    test('should update user preferences successfully', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        preferences: { theme: 'light' },
        update: jest.fn().mockResolvedValue()
      };

      mockRequest.user = mockUser;
      mockRequest.body = {
        preferences: { theme: 'dark', language: 'en' }
      };

      await updatePreferences(mockRequest, mockResponse, mockNext);

      expect(mockUser.update).toHaveBeenCalledWith({
        preferences: {
          theme: 'light',
          theme: 'dark',
          language: 'en'
        }
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Preferences updated successfully',
        preferences: { theme: 'light' }
      });
    });

    test('should reject invalid preferences', async () => {
      mockRequest.body = {
        preferences: 'invalid'
      };

      await updatePreferences(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation Error',
        message: 'Valid preferences object is required'
      });
    });

    test('should reject missing preferences', async () => {
      mockRequest.body = {};

      await updatePreferences(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation Error',
        message: 'Valid preferences object is required'
      });
    });
  });

  describe('login', () => {
    test('should login successfully with valid credentials', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        status: 'active',
        comparePassword: jest.fn().mockResolvedValue(true),
        roles: [
          {
            id: 'role-1',
            name: 'student',
            permissions: []
          }
        ],
        updateLastLogin: jest.fn().mockResolvedValue()
      };

      mockRequest.body = {
        email: '<EMAIL>',
        password: 'password123'
      };

      User.findOne = jest.fn().mockResolvedValue(mockUser);

      await login(mockRequest, mockResponse, mockNext);

      expect(User.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
        include: expect.any(Array)
      });
      expect(mockUser.comparePassword).toHaveBeenCalledWith('password123');
      expect(mockUser.updateLastLogin).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Login successful',
        user: expect.objectContaining({
          id: 'user-123',
          email: '<EMAIL>'
        }),
        token: 'mock-jwt-token'
      });
    });

    test('should reject login with missing credentials', async () => {
      mockRequest.body = {};

      await login(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation Error',
        message: 'Email and password are required'
      });
    });

    test('should reject login with invalid user', async () => {
      mockRequest.body = {
        email: '<EMAIL>',
        password: 'password123'
      };

      User.findOne = jest.fn().mockResolvedValue(null);

      await login(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid Credentials',
        message: 'No user found with this email address'
      });
    });

    test('should reject login with inactive user', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        status: 'inactive'
      };

      mockRequest.body = {
        email: '<EMAIL>',
        password: 'password123'
      };

      User.findOne = jest.fn().mockResolvedValue(mockUser);

      await login(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Account Inactive',
        message: 'Your account is not active. Please contact administrator.'
      });
    });

    test('should reject login with invalid password', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        status: 'active',
        comparePassword: jest.fn().mockResolvedValue(false)
      };

      mockRequest.body = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      User.findOne = jest.fn().mockResolvedValue(mockUser);

      await login(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid Credentials',
        message: 'Incorrect password'
      });
    });
  });
});
