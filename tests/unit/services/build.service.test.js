import { jest } from '@jest/globals';

// Mock AWS SDK
jest.mock('@aws-sdk/client-codebuild');
jest.mock('../../../src/config/logger.js');

import {
  CodeBuildClient,
  StartBuildCommand,
  BatchGetBuildsCommand
} from '@aws-sdk/client-codebuild';
import logger from '../../../src/config/logger.js';

// Import the service
import BuildService from '../../../src/services/build.service.js';

describe('Build Service', () => {
  let buildService;
  let mockCodeBuildClient;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Mock the CodeBuildClient constructor
    mockCodeBuildClient = {
      send: jest.fn()
    };
    CodeBuildClient.mockImplementation(() => mockCodeBuildClient);

    // Create service instance
    buildService = new BuildService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    test('should initialize with default values', () => {
      expect(buildService.region).toBe('ap-south-1');
      expect(buildService.projectName).toBe('bits-ds-image-build');
      expect(CodeBuildClient).toHaveBeenCalledWith({
        region: 'ap-south-1'
      });
    });

    test('should initialize with environment variables', () => {
      const originalRegion = process.env.AWS_REGION;
      const originalProjectName = process.env.CODEBUILD_PROJECT_NAME;

      process.env.AWS_REGION = 'us-east-1';
      process.env.CODEBUILD_PROJECT_NAME = 'custom-project';

      const customBuildService = new BuildService();

      expect(customBuildService.region).toBe('us-east-1');
      expect(customBuildService.projectName).toBe('custom-project');
      expect(CodeBuildClient).toHaveBeenCalledWith({
        region: 'us-east-1'
      });

      // Restore environment variables
      process.env.AWS_REGION = originalRegion;
      process.env.CODEBUILD_PROJECT_NAME = originalProjectName;
    });
  });

  describe('startImageBuild', () => {
    test('should start image build successfully', async () => {
      const projectId = 'project-123';
      const templateUrl = 's3://bucket/template/';
      const buildOptions = {
        dockerfile: 'CustomDockerfile',
        imageTag: 'custom-image:latest',
        ecrRepo: 'custom.ecr.aws/repo/image'
      };

      const mockResponse = {
        build: {
          id: 'build-123',
          arn: 'arn:aws:codebuild:region:account:build/build-123',
          buildStatus: 'IN_PROGRESS'
        }
      };

      mockCodeBuildClient.send = jest.fn().mockResolvedValue(mockResponse);

      const result = await buildService.startImageBuild(projectId, templateUrl, buildOptions);

      expect(logger.info).toHaveBeenCalledWith(`Starting image build for project ${projectId}`);
      expect(mockCodeBuildClient.send).toHaveBeenCalledWith(expect.any(StartBuildCommand));
      expect(logger.info).toHaveBeenCalledWith(
        `Image build started for project ${projectId}, build ID: ${mockResponse.build.id}`
      );

      expect(result).toEqual({
        success: true,
        buildId: 'build-123',
        buildArn: 'arn:aws:codebuild:region:account:build/build-123',
        status: 'IN_PROGRESS'
      });
    });

    test('should start image build with default options', async () => {
      const projectId = 'project-123';
      const templateUrl = 's3://bucket/template/';

      const mockResponse = {
        build: {
          id: 'build-123',
          arn: 'arn:aws:codebuild:region:account:build/build-123',
          buildStatus: 'IN_PROGRESS'
        }
      };

      mockCodeBuildClient.send = jest.fn().mockResolvedValue(mockResponse);

      const result = await buildService.startImageBuild(projectId, templateUrl);

      expect(mockCodeBuildClient.send).toHaveBeenCalledWith(expect.any(StartBuildCommand));
      
      // Check that default values were used
      const commandCall = mockCodeBuildClient.send.mock.calls[0][0];
      expect(commandCall.projectName).toBe('bits-ds-image-build');
      expect(commandCall.sourceVersion).toBe('main');
      
      // Check environment variables
      const envVars = commandCall.environmentVariablesOverride;
      expect(envVars.find(v => v.name === 'DOCKERFILE').value).toBe('Dockerfile');
      expect(envVars.find(v => v.name === 'IMAGE_TAG').value).toBe('bits-ds-project-123:latest');
      expect(envVars.find(v => v.name === 'ECR_REPO').value).toBe('registry.io/custom-notebook');

      expect(result.success).toBe(true);
    });

    test('should handle build start failure', async () => {
      const projectId = 'project-123';
      const templateUrl = 's3://bucket/template/';
      const mockError = new Error('Build start failed');

      mockCodeBuildClient.send = jest.fn().mockRejectedValue(mockError);

      await expect(buildService.startImageBuild(projectId, templateUrl)).rejects.toThrow('Build start failed');

      expect(logger.error).toHaveBeenCalledWith(
        `Failed to start image build for project ${projectId}:`,
        mockError
      );
    });

    test('should use custom ECR repo from environment', () => {
      const originalEcrRepo = process.env.ECR_REPO;
      process.env.ECR_REPO = 'custom.ecr.aws/repo/image';

      const buildServiceWithCustomEcr = new BuildService();
      const buildOptions = {};
      
      // This would be called in startImageBuild
      const buildSpec = buildServiceWithCustomEcr.generateBuildSpec({
        templateUrl: 's3://bucket/template/',
        dockerfile: 'Dockerfile',
        imageTag: 'image:latest',
        ecrRepo: 'custom.ecr.aws/repo/image'
      });

      expect(buildSpec).toContain('custom.ecr.aws/repo/image');

      process.env.ECR_REPO = originalEcrRepo;
    });
  });

  describe('getBuildStatus', () => {
    test('should get build status successfully', async () => {
      const buildId = 'build-123';
      const mockResponse = {
        builds: [
          {
            id: 'build-123',
            buildStatus: 'SUCCEEDED',
            startTime: new Date('2023-01-01T00:00:00Z'),
            endTime: new Date('2023-01-01T01:00:00Z'),
            logs: {
              deepLink: 'https://console.aws.amazon.com/logs'
            },
            artifacts: {
              location: 's3://bucket/artifacts/'
            }
          }
        ]
      };

      mockCodeBuildClient.send = jest.fn().mockResolvedValue(mockResponse);

      const result = await buildService.getBuildStatus(buildId);

      expect(mockCodeBuildClient.send).toHaveBeenCalledWith(expect.any(BatchGetBuildsCommand));
      expect(logger.error).not.toHaveBeenCalled();

      expect(result).toEqual({
        success: true,
        buildId: 'build-123',
        status: 'SUCCEEDED',
        startTime: new Date('2023-01-01T00:00:00Z'),
        endTime: new Date('2023-01-01T01:00:00Z'),
        logs: 'https://console.aws.amazon.com/logs',
        artifacts: 's3://bucket/artifacts/'
      });
    });

    test('should handle build not found', async () => {
      const buildId = 'build-123';
      const mockResponse = {
        builds: []
      };

      mockCodeBuildClient.send = jest.fn().mockResolvedValue(mockResponse);

      await expect(buildService.getBuildStatus(buildId)).rejects.toThrow('Build not found');

      expect(logger.error).toHaveBeenCalledWith(`Failed to get build status for ${buildId}:`, expect.any(Error));
    });

    test('should handle build status failure', async () => {
      const buildId = 'build-123';
      const mockError = new Error('Status check failed');

      mockCodeBuildClient.send = jest.fn().mockRejectedValue(mockError);

      await expect(buildService.getBuildStatus(buildId)).rejects.toThrow('Status check failed');

      expect(logger.error).toHaveBeenCalledWith(`Failed to get build status for ${buildId}:`, mockError);
    });

    test('should handle build with missing optional fields', async () => {
      const buildId = 'build-123';
      const mockResponse = {
        builds: [
          {
            id: 'build-123',
            buildStatus: 'IN_PROGRESS',
            startTime: new Date('2023-01-01T00:00:00Z')
            // Missing endTime, logs, artifacts
          }
        ]
      };

      mockCodeBuildClient.send = jest.fn().mockResolvedValue(mockResponse);

      const result = await buildService.getBuildStatus(buildId);

      expect(result).toEqual({
        success: true,
        buildId: 'build-123',
        status: 'IN_PROGRESS',
        startTime: new Date('2023-01-01T00:00:00Z'),
        endTime: undefined,
        logs: undefined,
        artifacts: undefined
      });
    });
  });

  describe('generateBuildSpec', () => {
    test('should generate valid buildspec', () => {
      const options = {
        templateUrl: 's3://bucket/template/',
        dockerfile: 'Dockerfile',
        imageTag: 'image:latest',
        ecrRepo: 'custom.ecr.aws/repo/image'
      };

      const buildSpec = buildService.generateBuildSpec(options);

      expect(buildSpec).toContain('version: 0.2');
      expect(buildSpec).toContain('phases:');
      expect(buildSpec).toContain('pre_build:');
      expect(buildSpec).toContain('build:');
      expect(buildSpec).toContain('post_build:');
      expect(buildSpec).toContain('artifacts:');
      expect(buildSpec).toContain('cache:');
      expect(buildSpec).toContain('aws ecr get-login-password');
      expect(buildSpec).toContain('aws s3 sync');
      expect(buildSpec).toContain('docker build');
      expect(buildSpec).toContain('docker push');
    });

    test('should include all required sections', () => {
      const buildSpec = buildService.generateBuildSpec({});

      // Check for all required sections
      expect(buildSpec).toMatch(/version: 0\.2/);
      expect(buildSpec).toMatch(/phases:/);
      expect(buildSpec).toMatch(/pre_build:/);
      expect(buildSpec).toMatch(/build:/);
      expect(buildSpec).toMatch(/post_build:/);
      expect(buildSpec).toMatch(/artifacts:/);
      expect(buildSpec).toMatch(/cache:/);
    });
  });

  describe('createBuildWebhook', () => {
    test('should create webhook successfully', async () => {
      const projectId = 'project-123';
      const webhookUrl = 'https://api.example.com/webhook';

      const result = await buildService.createBuildWebhook(projectId, webhookUrl);

      expect(logger.info).toHaveBeenCalledWith(`Creating webhook for project ${projectId} at ${webhookUrl}`);
      expect(result).toEqual({
        success: true,
        webhookId: 'webhook-project-123',
        url: webhookUrl
      });
    });

    test('should handle webhook creation failure', async () => {
      const projectId = 'project-123';
      const webhookUrl = 'https://api.example.com/webhook';
      const mockError = new Error('Webhook creation failed');

      // Mock the method to throw an error
      jest.spyOn(buildService, 'createBuildWebhook').mockRejectedValueOnce(mockError);

      await expect(buildService.createBuildWebhook(projectId, webhookUrl)).rejects.toThrow('Webhook creation failed');

      expect(logger.error).toHaveBeenCalledWith(`Failed to create webhook for project ${projectId}:`, mockError);
    });
  });

  describe('updateProjectImageUrl', () => {
    test('should update project image URL successfully', async () => {
      const projectId = 'project-123';
      const imageUrl = 'custom.ecr.aws/repo/image:latest';

      const result = await buildService.updateProjectImageUrl(projectId, imageUrl);

      expect(logger.info).toHaveBeenCalledWith(`Updating project ${projectId} with image URL: ${imageUrl}`);
      expect(result).toEqual({
        success: true,
        projectId,
        imageUrl
      });
    });

    test('should handle project update failure', async () => {
      const projectId = 'project-123';
      const imageUrl = 'custom.ecr.aws/repo/image:latest';
      const mockError = new Error('Project update failed');

      // Mock the method to throw an error
      jest.spyOn(buildService, 'updateProjectImageUrl').mockRejectedValueOnce(mockError);

      await expect(buildService.updateProjectImageUrl(projectId, imageUrl)).rejects.toThrow('Project update failed');

      expect(logger.error).toHaveBeenCalledWith(
        `Failed to update project ${projectId} with image URL:`,
        mockError
      );
    });
  });

  describe('validateBuildRequirements', () => {
    test('should validate requirements successfully', async () => {
      const templateUrl = 's3://bucket/template/';

      const result = await buildService.validateBuildRequirements(templateUrl);

      expect(logger.info).toHaveBeenCalledWith(`Validating build requirements for template: ${templateUrl}`);
      expect(result).toBe(true);
    });

    test('should handle validation failure', async () => {
      const templateUrl = 's3://bucket/template/';
      const mockError = new Error('Validation failed');

      // Mock the method to throw an error
      jest.spyOn(buildService, 'validateBuildRequirements').mockImplementationOnce(async () => {
        throw mockError;
      });

      const result = await buildService.validateBuildRequirements(templateUrl);

      expect(logger.error).toHaveBeenCalledWith('Failed to validate build requirements:', mockError);
      expect(result).toBe(false);
    });
  });

  describe('getBuildHistory', () => {
    test('should get build history successfully', async () => {
      const projectId = 'project-123';

      const result = await buildService.getBuildHistory(projectId);

      expect(logger.info).toHaveBeenCalledWith(`Getting build history for project ${projectId}`);
      expect(result).toEqual([]);
    });

    test('should handle build history failure', async () => {
      const projectId = 'project-123';
      const mockError = new Error('History retrieval failed');

      // Mock the method to throw an error
      jest.spyOn(buildService, 'getBuildHistory').mockRejectedValueOnce(mockError);

      await expect(buildService.getBuildHistory(projectId)).rejects.toThrow('History retrieval failed');

      expect(logger.error).toHaveBeenCalledWith(
        `Failed to get build history for project ${projectId}:`,
        mockError
      );
    });
  });

  describe('StartBuildCommand validation', () => {
    test('should create StartBuildCommand with correct parameters', async () => {
      const projectId = 'project-123';
      const templateUrl = 's3://bucket/template/';
      const buildOptions = {
        dockerfile: 'CustomDockerfile',
        imageTag: 'custom-image:latest',
        ecrRepo: 'custom.ecr.aws/repo/image'
      };

      const mockResponse = {
        build: {
          id: 'build-123',
          arn: 'arn:aws:codebuild:region:account:build/build-123',
          buildStatus: 'IN_PROGRESS'
        }
      };

      mockCodeBuildClient.send = jest.fn().mockResolvedValue(mockResponse);

      await buildService.startImageBuild(projectId, templateUrl, buildOptions);

      const commandCall = mockCodeBuildClient.send.mock.calls[0][0];
      expect(commandCall).toBeInstanceOf(StartBuildCommand);
      expect(commandCall.projectName).toBe('bits-ds-image-build');
      expect(commandCall.sourceVersion).toBe('main');
      expect(commandCall.environmentVariablesOverride).toHaveLength(5);
      
      // Check environment variables
      const envVars = commandCall.environmentVariablesOverride;
      expect(envVars.find(v => v.name === 'PROJECT_ID').value).toBe(projectId);
      expect(envVars.find(v => v.name === 'TEMPLATE_URL').value).toBe(templateUrl);
      expect(envVars.find(v => v.name === 'DOCKERFILE').value).toBe('CustomDockerfile');
      expect(envVars.find(v => v.name === 'IMAGE_TAG').value).toBe('custom-image:latest');
      expect(envVars.find(v => v.name === 'ECR_REPO').value).toBe('custom.ecr.aws/repo/image');
    });
  });

  describe('BatchGetBuildsCommand validation', () => {
    test('should create BatchGetBuildsCommand with correct parameters', async () => {
      const buildId = 'build-123';
      const mockResponse = {
        builds: [
          {
            id: 'build-123',
            buildStatus: 'SUCCEEDED'
          }
        ]
      };

      mockCodeBuildClient.send = jest.fn().mockResolvedValue(mockResponse);

      await buildService.getBuildStatus(buildId);

      const commandCall = mockCodeBuildClient.send.mock.calls[0][0];
      expect(commandCall).toBeInstanceOf(BatchGetBuildsCommand);
      expect(commandCall.ids).toEqual([buildId]);
    });
  });
});
