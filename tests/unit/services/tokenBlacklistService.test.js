import { jest } from '@jest/globals';

// Mock Redis
const mockRedis = {
  setex: jest.fn(),
  get: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  memory: jest.fn(),
  ttl: jest.fn(),
  quit: jest.fn()
};

jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => mockRedis);
});

jest.mock('../../../src/config/logger.js', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn()
}));

import logger from '../../../src/config/logger.js';

describe('TokenBlacklistService', () => {
  let service;

  beforeEach(() => {
    jest.clearAllMocks();
    // Import and create a new instance for each test
    const TokenBlacklistService = require('../../../src/services/tokenBlacklistService.js').default.constructor;
    service = new TokenBlacklistService();
  });

  describe('blacklistToken', () => {
    test('should blacklist token successfully', async () => {
      const token = 'test-jwt-token';
      const expiresIn = 3600;

      mockRedis.setex.mockResolvedValue('OK');

      await service.blacklistToken(token, expiresIn);

      expect(mockRedis.setex).toHaveBeenCalledWith(
        'blacklisted_token:test-jwt-token',
        expiresIn,
        '1'
      );
      expect(logger.info).toHaveBeenCalledWith('Token blacklisted successfully');
    });

    test('should use default expiry when not provided', async () => {
      const token = 'test-jwt-token';

      mockRedis.setex.mockResolvedValue('OK');

      await service.blacklistToken(token);

      expect(mockRedis.setex).toHaveBeenCalledWith(
        'blacklisted_token:test-jwt-token',
        24 * 60 * 60, // 24 hours
        '1'
      );
    });

    test('should handle Redis error', async () => {
      const token = 'test-jwt-token';
      const redisError = new Error('Redis connection failed');

      mockRedis.setex.mockRejectedValue(redisError);

      await expect(service.blacklistToken(token)).rejects.toThrow('Redis connection failed');
      expect(logger.error).toHaveBeenCalledWith('Error blacklisting token:', redisError);
    });
  });

  describe('isTokenBlacklisted', () => {
    test('should return true for blacklisted token', async () => {
      const token = 'test-jwt-token';

      mockRedis.get.mockResolvedValue('1');

      const result = await service.isTokenBlacklisted(token);

      expect(mockRedis.get).toHaveBeenCalledWith('blacklisted_token:test-jwt-token');
      expect(result).toBe(true);
    });

    test('should return false for non-blacklisted token', async () => {
      const token = 'test-jwt-token';

      mockRedis.get.mockResolvedValue(null);

      const result = await service.isTokenBlacklisted(token);

      expect(mockRedis.get).toHaveBeenCalledWith('blacklisted_token:test-jwt-token');
      expect(result).toBe(false);
    });

    test('should handle Redis error gracefully', async () => {
      const token = 'test-jwt-token';
      const redisError = new Error('Redis connection failed');

      mockRedis.get.mockRejectedValue(redisError);

      const result = await service.isTokenBlacklisted(token);

      expect(logger.error).toHaveBeenCalledWith('Error checking token blacklist:', redisError);
      expect(result).toBe(false); // Fail open
    });
  });

  describe('removeFromBlacklist', () => {
    test('should remove token from blacklist successfully', async () => {
      const token = 'test-jwt-token';

      mockRedis.del.mockResolvedValue(1);

      await service.removeFromBlacklist(token);

      expect(mockRedis.del).toHaveBeenCalledWith('blacklisted_token:test-jwt-token');
      expect(logger.info).toHaveBeenCalledWith('Token removed from blacklist');
    });

    test('should handle Redis error', async () => {
      const token = 'test-jwt-token';
      const redisError = new Error('Redis connection failed');

      mockRedis.del.mockRejectedValue(redisError);

      await expect(service.removeFromBlacklist(token)).rejects.toThrow('Redis connection failed');
      expect(logger.error).toHaveBeenCalledWith('Error removing token from blacklist:', redisError);
    });
  });

  describe('getBlacklistStats', () => {
    test('should return blacklist statistics', async () => {
      const mockKeys = [
        'blacklisted_token:token1',
        'blacklisted_token:token2',
        'blacklisted_token:token3'
      ];

      mockRedis.keys.mockResolvedValue(mockKeys);
      mockRedis.memory.mockResolvedValue(1024000);

      const stats = await service.getBlacklistStats();

      expect(mockRedis.keys).toHaveBeenCalledWith('blacklisted_token:*');
      expect(mockRedis.memory).toHaveBeenCalledWith('USAGE');
      expect(stats).toEqual({
        totalBlacklistedTokens: 3,
        memoryUsage: 1024000
      });
    });

    test('should handle Redis error gracefully', async () => {
      const redisError = new Error('Redis connection failed');

      mockRedis.keys.mockRejectedValue(redisError);

      const stats = await service.getBlacklistStats();

      expect(logger.error).toHaveBeenCalledWith('Error getting blacklist stats:', redisError);
      expect(stats).toEqual({
        totalBlacklistedTokens: 0,
        memoryUsage: 0
      });
    });

    test('should handle memory command error', async () => {
      const mockKeys = ['blacklisted_token:token1'];
      const memoryError = new Error('Memory command not supported');

      mockRedis.keys.mockResolvedValue(mockKeys);
      mockRedis.memory.mockRejectedValue(memoryError);

      const stats = await service.getBlacklistStats();

      expect(logger.warn).toHaveBeenCalledWith('Memory command not supported, using 0 for memory usage');
      expect(stats).toEqual({
        totalBlacklistedTokens: 1,
        memoryUsage: 0
      });
    });
  });

  describe('cleanupExpiredEntries', () => {
    test('should cleanup expired entries successfully', async () => {
      const mockKeys = [
        'blacklisted_token:expired1',
        'blacklisted_token:expired2',
        'blacklisted_token:valid1'
      ];

      mockRedis.keys.mockResolvedValue(mockKeys);
      mockRedis.ttl
        .mockResolvedValueOnce(-1) // expired
        .mockResolvedValueOnce(-2) // expired
        .mockResolvedValueOnce(3600); // valid
      mockRedis.del.mockResolvedValue(1);

      await service.cleanupExpiredEntries();

      expect(mockRedis.keys).toHaveBeenCalledWith('blacklisted_token:*');
      expect(mockRedis.ttl).toHaveBeenCalledTimes(3);
      expect(mockRedis.del).toHaveBeenCalledTimes(2);
      expect(logger.info).toHaveBeenCalledWith('Cleaned up 2 expired blacklist entries');
    });

    test('should handle no expired entries', async () => {
      const mockKeys = [
        'blacklisted_token:valid1',
        'blacklisted_token:valid2'
      ];

      mockRedis.keys.mockResolvedValue(mockKeys);
      mockRedis.ttl
        .mockResolvedValueOnce(3600) // valid
        .mockResolvedValueOnce(7200); // valid

      await service.cleanupExpiredEntries();

      expect(mockRedis.del).not.toHaveBeenCalled();
      expect(logger.info).not.toHaveBeenCalledWith(expect.stringContaining('Cleaned up'));
    });

    test('should handle Redis error gracefully', async () => {
      const redisError = new Error('Redis connection failed');

      mockRedis.keys.mockRejectedValue(redisError);

      await service.cleanupExpiredEntries();

      expect(logger.error).toHaveBeenCalledWith('Error cleaning up expired blacklist entries:', redisError);
    });
  });

  describe('close', () => {
    test('should close Redis connection successfully', async () => {
      mockRedis.quit.mockResolvedValue('OK');

      await service.close();

      expect(mockRedis.quit).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Token blacklist service connection closed');
    });

    test('should handle Redis close error', async () => {
      const redisError = new Error('Redis close failed');

      mockRedis.quit.mockRejectedValue(redisError);

      await service.close();

      expect(logger.error).toHaveBeenCalledWith('Error closing token blacklist service:', redisError);
    });
  });

  describe('configuration', () => {
    test('should use correct Redis prefix', () => {
      expect(service.blacklistPrefix).toBe('blacklisted_token:');
    });

    test('should use correct default expiry', () => {
      expect(service.blacklistExpiry).toBe(24 * 60 * 60); // 24 hours
    });
  });
});
