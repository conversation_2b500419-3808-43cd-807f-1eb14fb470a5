import { jest } from '@jest/globals';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3');
jest.mock('@aws-sdk/s3-request-presigner');
jest.mock('uuid');
jest.mock('../../../src/config/logger.js');

import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  HeadObjectCommand
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import logger from '../../../src/config/logger.js';

// Import the service directly
import s3Service from '../../../src/services/s3Service.js';

describe('S3 Service', () => {
  let mockS3Client;
  let mockGetSignedUrl;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Mock the S3Client constructor
    mockS3Client = {
      send: jest.fn()
    };
    S3Client.mockImplementation(() => mockS3Client);

    // Mock getSignedUrl
    mockGetSignedUrl = jest.fn();
    getSignedUrl.mockImplementation(mockGetSignedUrl);

    // Mock uuid - return the full UUID string
    uuidv4.mockReturnValue('test-uuid-1234-5678-9012');

    // Set up the mocked client.send for the singleton instance
    s3Service.client.send = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    test('should initialize with default values', () => {
      // Since it's a singleton, we can't test the constructor directly
      // Instead, we test the instance properties
      expect(s3Service.bucket).toBe('bits-dataScience-platform');
      expect(s3Service.baseUrl).toBe('https://bits-dataScience-platform.s3.us-east-1.amazonaws.com');
    });

    test('should initialize with environment variables', () => {
      const originalRegion = process.env.AWS_REGION;
      const originalBucket = process.env.AWS_S3_BUCKET;

      process.env.AWS_REGION = 'ap-south-1';
      process.env.AWS_S3_BUCKET = 'custom-bucket';

      // Re-import to get the updated instance
      jest.resetModules();
      const updatedS3Service = require('../../../src/services/s3Service.js').default;

      expect(updatedS3Service.bucket).toBe('custom-bucket');
      expect(updatedS3Service.baseUrl).toBe('https://custom-bucket.s3.ap-south-1.amazonaws.com');

      // Restore environment variables
      process.env.AWS_REGION = originalRegion;
      process.env.AWS_S3_BUCKET = originalBucket;
    });
  });

  describe('generateFilePath', () => {
    test('should generate project template path', () => {
      const path = s3Service.generateFilePath('project-template', 'user-123', 'course-456', 'project-789', 'template.zip');

      expect(path).toMatch(/^projects\/course-456\/project-789\/templates\/template\.zip$/);
    });

    test('should generate project dataset path', () => {
      const path = s3Service.generateFilePath('project-dataset', 'user-123', 'course-456', 'project-789', 'data.csv');

      expect(path).toMatch(/^projects\/course-456\/project-789\/datasets\/data\.csv$/);
    });

    test('should generate submission path', () => {
      const path = s3Service.generateFilePath('submission', 'user-123', 'course-456', 'project-789', 'submission.py');

      expect(path).toMatch(/^submissions\/course-456\/project-789\/user-123\/\d{4}-\d{2}-\d{2}\/submission\.py$/);
    });

    test('should generate user notebook path', () => {
      const path = s3Service.generateFilePath('user-notebook', 'user-123', null, null, 'notebook.ipynb');

      expect(path).toMatch(/^notebooks\/user-123\/\d{4}-\d{2}-\d{2}\/notebook\.ipynb$/);
    });

    test('should generate profile picture path', () => {
      const path = s3Service.generateFilePath('profile-picture', 'user-123', null, null, 'avatar.jpg');

      expect(path).toMatch(/^profiles\/user-123\/avatar\.jpg$/);
    });

    test('should generate course material path', () => {
      const path = s3Service.generateFilePath('course-material', 'user-123', 'course-456', null, 'lecture.pdf');

      expect(path).toMatch(/^courses\/course-456\/materials\/lecture\.pdf$/);
    });

    test('should generate misc path for unknown type', () => {
      const path = s3Service.generateFilePath('unknown-type', 'user-123', null, null, 'file.txt');

      expect(path).toMatch(/^misc\/\d{4}-\d{2}-\d{2}\/file\.txt$/);
    });

    test('should generate path with UUID when filename not provided', () => {
      const path = s3Service.generateFilePath('project-template', 'user-123', 'course-456', 'project-789');

      expect(path).toMatch(/^projects\/course-456\/project-789\/templates\/test$/);
    });
  });

  describe('uploadFile', () => {
    test('should upload file successfully', async () => {
      const mockFile = {
        buffer: Buffer.from('test content'),
        mimetype: 'text/plain',
        originalname: 'test.txt',
        size: 1024
      };

      const mockResult = {
        ETag: '"test-etag"'
      };

      s3Service.client.send = jest.fn().mockResolvedValue(mockResult);

      const result = await s3Service.uploadFile(mockFile, 'project-template', 'user-123', {
        courseId: 'course-456',
        projectId: 'project-789'
      });

      expect(s3Service.client.send).toHaveBeenCalledWith(expect.any(PutObjectCommand));
      expect(logger.info).toHaveBeenCalledWith('File uploaded successfully: projects/course-456/project-789/templates/test.txt');
      expect(result).toEqual({
        url: expect.stringContaining('projects/course-456/project-789/templates/test.txt'),
        key: expect.stringContaining('projects/course-456/project-789/templates/test.txt'),
        bucket: 'bits-dataScience-platform',
        size: 1024,
        contentType: 'text/plain',
        etag: '"test-etag"'
      });
    });

    test('should handle upload error', async () => {
      const mockFile = {
        buffer: Buffer.from('test content'),
        mimetype: 'text/plain',
        originalname: 'test.txt',
        size: 1024
      };

      const mockError = new Error('Upload failed');
      s3Service.client.send = jest.fn().mockRejectedValue(mockError);

      await expect(s3Service.uploadFile(mockFile, 'project-template', 'user-123')).rejects.toThrow('Failed to upload file: Upload failed');
      expect(logger.error).toHaveBeenCalledWith('S3 upload error:', mockError);
    });
  });

  describe('generatePresignedUploadUrl', () => {
    test('should generate presigned upload URL successfully', async () => {
      const mockPresignedUrl = 'https://presigned-url.com';

      mockGetSignedUrl.mockResolvedValue(mockPresignedUrl);

      const result = await s3Service.generatePresignedUploadUrl('project-template', 'user-123', {
        courseId: 'course-456',
        projectId: 'project-789',
        filename: 'test.txt',
        contentType: 'text/plain',
        expiresIn: 7200
      });

      expect(mockGetSignedUrl).toHaveBeenCalledWith(expect.any(S3Client), expect.any(PutObjectCommand), {
        expiresIn: 7200
      });
      expect(result).toEqual({
        uploadUrl: mockPresignedUrl,
        key: expect.stringContaining('test.txt'),
        finalUrl: expect.stringContaining('test.txt')
      });
    });

    test('should handle presigned URL generation error', async () => {
      const mockError = new Error('URL generation failed');

      mockGetSignedUrl.mockRejectedValue(mockError);

      await expect(s3Service.generatePresignedUploadUrl('project-template', 'user-123')).rejects.toThrow('Failed to generate upload URL: URL generation failed');
      expect(logger.error).toHaveBeenCalledWith('S3 presigned URL generation error:', mockError);
    });
  });

  describe('generatePresignedDownloadUrl', () => {
    test('should generate presigned download URL successfully', async () => {
      const mockPresignedUrl = 'https://download-url.com';

      mockGetSignedUrl.mockResolvedValue(mockPresignedUrl);

      const result = await s3Service.generatePresignedDownloadUrl('test-key', 1800);

      expect(mockGetSignedUrl).toHaveBeenCalledWith(expect.any(S3Client), expect.any(GetObjectCommand), {
        expiresIn: 1800
      });
      expect(result).toBe(mockPresignedUrl);
    });

    test('should handle download URL generation error', async () => {
      const mockError = new Error('Download URL generation failed');

      mockGetSignedUrl.mockRejectedValue(mockError);

      await expect(s3Service.generatePresignedDownloadUrl('test-key')).rejects.toThrow('Failed to generate download URL: Download URL generation failed');
      expect(logger.error).toHaveBeenCalledWith('S3 download URL generation error:', mockError);
    });
  });

  describe('deleteFile', () => {
    test('should delete file successfully', async () => {
      s3Service.client.send = jest.fn().mockResolvedValue({});

      const result = await s3Service.deleteFile('test-key');

      expect(s3Service.client.send).toHaveBeenCalledWith(expect.any(DeleteObjectCommand));
      expect(logger.info).toHaveBeenCalledWith('File deleted successfully: test-key');
      expect(result).toEqual({
        success: true,
        key: 'test-key'
      });
    });

    test('should handle delete error', async () => {
      const mockError = new Error('Delete failed');
      s3Service.client.send = jest.fn().mockRejectedValue(mockError);

      await expect(s3Service.deleteFile('test-key')).rejects.toThrow('Failed to delete file: Delete failed');
      expect(logger.error).toHaveBeenCalledWith('S3 delete error:', mockError);
    });
  });

  describe('fileExists', () => {
    test('should return true when file exists', async () => {
      s3Service.client.send = jest.fn().mockResolvedValue({});

      const result = await s3Service.fileExists('test-key');

      expect(s3Service.client.send).toHaveBeenCalledWith(expect.any(HeadObjectCommand));
      expect(result).toBe(true);
    });

    test('should return false when file does not exist', async () => {
      const mockError = new Error('Not found');
      mockError.name = 'NotFound';
      s3Service.client.send = jest.fn().mockRejectedValue(mockError);

      const result = await s3Service.fileExists('test-key');

      expect(result).toBe(false);
    });

    test('should throw error for other errors', async () => {
      const mockError = new Error('Other error');
      s3Service.client.send = jest.fn().mockRejectedValue(mockError);

      await expect(s3Service.fileExists('test-key')).rejects.toThrow('Other error');
    });
  });

  describe('getFileMetadata', () => {
    test('should get file metadata successfully', async () => {
      const mockResult = {
        ContentLength: 1024,
        ContentType: 'text/plain',
        LastModified: new Date(),
        ETag: '"test-etag"',
        Metadata: { key: 'value' }
      };

      s3Service.client.send = jest.fn().mockResolvedValue(mockResult);

      const result = await s3Service.getFileMetadata('test-key');

      expect(s3Service.client.send).toHaveBeenCalledWith(expect.any(HeadObjectCommand));
      expect(result).toEqual({
        size: 1024,
        contentType: 'text/plain',
        lastModified: mockResult.LastModified,
        etag: '"test-etag"',
        metadata: { key: 'value' }
      });
    });

    test('should handle metadata retrieval error', async () => {
      const mockError = new Error('Metadata retrieval failed');
      s3Service.client.send = jest.fn().mockRejectedValue(mockError);

      await expect(s3Service.getFileMetadata('test-key')).rejects.toThrow('Failed to get file metadata: Metadata retrieval failed');
      expect(logger.error).toHaveBeenCalledWith('S3 metadata retrieval error:', mockError);
    });
  });

  describe('copyFile', () => {
    test('should copy file successfully', async () => {
      s3Service.client.send = jest.fn().mockResolvedValue({});

      const result = await s3Service.copyFile('source-key', 'destination-key');

      expect(s3Service.client.send).toHaveBeenCalledWith(expect.any(PutObjectCommand));
      expect(logger.info).toHaveBeenCalledWith('File copied successfully: source-key -> destination-key');
      expect(result).toEqual({
        url: expect.stringContaining('destination-key'),
        key: 'destination-key'
      });
    });

    test('should handle copy error', async () => {
      const mockError = new Error('Copy failed');
      s3Service.client.send = jest.fn().mockRejectedValue(mockError);

      await expect(s3Service.copyFile('source-key', 'destination-key')).rejects.toThrow('Failed to copy file: Copy failed');
      expect(logger.error).toHaveBeenCalledWith('S3 copy error:', mockError);
    });
  });

  describe('extractKeyFromUrl', () => {
    test('should extract key from base URL', () => {
      const url = 'https://bits-dataScience-platform.s3.us-east-1.amazonaws.com/test/path/file.txt';

      const result = s3Service.extractKeyFromUrl(url);

      expect(result).toBe('test/path/file.txt');
    });

    test('should extract key from different S3 URL format', () => {
      const url = 'https://s3.us-east-1.amazonaws.com/bits-dataScience-platform/test/path/file.txt';

      const result = s3Service.extractKeyFromUrl(url);

      expect(result).toBe('test/path/file.txt');
    });

    test('should return null for invalid URL', () => {
      const url = 'https://invalid-url.com/file.txt';

      const result = s3Service.extractKeyFromUrl(url);

      expect(result).toBeNull();
    });

    test('should return null for non-string URL', () => {
      expect(s3Service.extractKeyFromUrl(null)).toBeNull();
      expect(s3Service.extractKeyFromUrl(undefined)).toBeNull();
      expect(s3Service.extractKeyFromUrl(123)).toBeNull();
    });
  });

  describe('validateFile', () => {
    test('should validate file type successfully', () => {
      const file = {
        originalname: 'test.txt',
        mimetype: 'text/plain',
        size: 1024
      };

      const errors = s3Service.validateFile(file, ['txt', 'pdf'], 2048);

      expect(errors).toEqual([]);
    });

    test('should reject invalid file type', () => {
      const file = {
        originalname: 'test.exe',
        mimetype: 'application/octet-stream',
        size: 1024
      };

      const errors = s3Service.validateFile(file, ['txt', 'pdf'], 2048);

      expect(errors).toContain('File type not allowed. Allowed types: txt, pdf');
    });

    test('should reject file that is too large', () => {
      const file = {
        originalname: 'test.txt',
        mimetype: 'text/plain',
        size: 3 * 1024 * 1024 // 3MB
      };

      const errors = s3Service.validateFile(file, ['txt'], 2 * 1024 * 1024); // 2MB limit

      expect(errors).toContain('File size too large. Maximum size: 2MB');
    });

    test('should handle multiple validation errors', () => {
      const file = {
        originalname: 'test.exe',
        mimetype: 'application/octet-stream',
        size: 3 * 1024 * 1024 // 3MB
      };

      const errors = s3Service.validateFile(file, ['txt'], 2 * 1024 * 1024); // 2MB limit

      expect(errors).toHaveLength(2);
      expect(errors).toContain('File type not allowed. Allowed types: txt');
      expect(errors).toContain('File size too large. Maximum size: 2MB');
    });

    test('should skip type validation when no allowed types specified', () => {
      const file = {
        originalname: 'test.exe',
        mimetype: 'application/octet-stream',
        size: 1024
      };

      const errors = s3Service.validateFile(file, [], 2048);

      expect(errors).toEqual([]);
    });

    test('should skip size validation when no max size specified', () => {
      const file = {
        originalname: 'test.txt',
        mimetype: 'text/plain',
        size: 3 * 1024 * 1024 // 3MB
      };

      const errors = s3Service.validateFile(file, ['txt']);

      expect(errors).toEqual([]);
    });
  });
});
