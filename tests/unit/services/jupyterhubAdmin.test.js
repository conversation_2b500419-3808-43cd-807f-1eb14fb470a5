import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('axios');
jest.mock('../../../src/config/logger.js');

import axios from 'axios';

// Import the service functions
import {
  createUser,
  deleteUser,
  getUser,
  listUsers,
  startServer,
  stopServer,
  getServerStatus,
  getUserServers,
  addUserToGroup,
  removeUserFromGroup,
  createGroup,
  deleteGroup,
  listGroups
} from '../../../src/services/jupyterhubAdmin.js';

describe('JupyterHub Admin Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createUser', () => {
    test('should create user successfully', async () => {
      const userData = {
        name: 'testuser',
        admin: false,
        groups: ['students']
      };

      const mockResponse = {
        data: {
          name: 'testuser',
          admin: false,
          groups: ['students'],
          created: '2023-01-01T00:00:00Z'
        }
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      const result = await createUser(userData);

      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/users'),
        userData,
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token'),
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle user creation errors', async () => {
      const userData = {
        name: 'testuser'
      };

      const mockError = new Error('User creation failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(createUser(userData)).rejects.toThrow('User creation failed');
    });

    test('should handle duplicate user creation', async () => {
      const userData = {
        name: 'existinguser'
      };

      const mockResponse = {
        status: 409,
        data: {
          message: 'User already exists'
        }
      };

      axios.post = jest.fn().mockRejectedValue({
        response: mockResponse
      });

      await expect(createUser(userData)).rejects.toThrow();
    });
  });

  describe('deleteUser', () => {
    test('should delete user successfully', async () => {
      const username = 'testuser';
      const mockResponse = {
        data: {
          success: true,
          message: 'User deleted successfully'
        }
      };

      axios.delete = jest.fn().mockResolvedValue(mockResponse);

      const result = await deleteUser(username);

      expect(axios.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/users/${username}`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token')
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle user deletion errors', async () => {
      const username = 'testuser';
      const mockError = new Error('User deletion failed');
      axios.delete = jest.fn().mockRejectedValue(mockError);

      await expect(deleteUser(username)).rejects.toThrow('User deletion failed');
    });

    test('should handle non-existent user deletion', async () => {
      const username = 'nonexistent';
      const mockResponse = {
        status: 404,
        data: {
          message: 'User not found'
        }
      };

      axios.delete = jest.fn().mockRejectedValue({
        response: mockResponse
      });

      await expect(deleteUser(username)).rejects.toThrow();
    });
  });

  describe('getUser', () => {
    test('should get user successfully', async () => {
      const username = 'testuser';
      const mockResponse = {
        data: {
          name: 'testuser',
          admin: false,
          groups: ['students'],
          servers: {
            '': {
              name: '',
              ready: true,
              url: 'http://localhost:8888/user/testuser/',
              user_options: {},
              progress_url: '/hub/api/users/testuser/server/progress'
            }
          }
        }
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await getUser(username);

      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/users/${username}`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token')
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle user retrieval errors', async () => {
      const username = 'testuser';
      const mockError = new Error('User retrieval failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(getUser(username)).rejects.toThrow('User retrieval failed');
    });
  });

  describe('listUsers', () => {
    test('should list users successfully', async () => {
      const mockResponse = {
        data: [
          {
            name: 'user1',
            admin: false,
            groups: ['students']
          },
          {
            name: 'user2',
            admin: true,
            groups: ['instructors']
          }
        ]
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await listUsers();

      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/users'),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token')
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle user listing errors', async () => {
      const mockError = new Error('User listing failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(listUsers()).rejects.toThrow('User listing failed');
    });
  });

  describe('startServer', () => {
    test('should start server successfully', async () => {
      const username = 'testuser';
      const serverName = '';
      const serverOptions = {
        image: 'jupyter/datascience-notebook:latest',
        cpu_limit: 1,
        mem_limit: '2G'
      };

      const mockResponse = {
        data: {
          name: '',
          ready: false,
          url: 'http://localhost:8888/user/testuser/',
          progress_url: '/hub/api/users/testuser/server/progress'
        }
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      const result = await startServer(username, serverName, serverOptions);

      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining(`/users/${username}/servers/${serverName}`),
        serverOptions,
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token'),
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle server start errors', async () => {
      const username = 'testuser';
      const serverName = '';
      const serverOptions = {};

      const mockError = new Error('Server start failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(startServer(username, serverName, serverOptions)).rejects.toThrow('Server start failed');
    });
  });

  describe('stopServer', () => {
    test('should stop server successfully', async () => {
      const username = 'testuser';
      const serverName = '';
      const mockResponse = {
        data: {
          success: true,
          message: 'Server stopped successfully'
        }
      };

      axios.delete = jest.fn().mockResolvedValue(mockResponse);

      const result = await stopServer(username, serverName);

      expect(axios.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/users/${username}/servers/${serverName}`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token')
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle server stop errors', async () => {
      const username = 'testuser';
      const serverName = '';
      const mockError = new Error('Server stop failed');
      axios.delete = jest.fn().mockRejectedValue(mockError);

      await expect(stopServer(username, serverName)).rejects.toThrow('Server stop failed');
    });
  });

  describe('getServerStatus', () => {
    test('should get server status successfully', async () => {
      const username = 'testuser';
      const serverName = '';
      const mockResponse = {
        data: {
          name: '',
          ready: true,
          url: 'http://localhost:8888/user/testuser/',
          user_options: {},
          progress_url: '/hub/api/users/testuser/server/progress'
        }
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await getServerStatus(username, serverName);

      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/users/${username}/servers/${serverName}`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token')
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle server status retrieval errors', async () => {
      const username = 'testuser';
      const serverName = '';
      const mockError = new Error('Server status retrieval failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(getServerStatus(username, serverName)).rejects.toThrow('Server status retrieval failed');
    });
  });

  describe('getUserServers', () => {
    test('should get user servers successfully', async () => {
      const username = 'testuser';
      const mockResponse = {
        data: {
          '': {
            name: '',
            ready: true,
            url: 'http://localhost:8888/user/testuser/'
          },
          'server1': {
            name: 'server1',
            ready: false,
            url: 'http://localhost:8888/user/testuser/server1/'
          }
        }
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await getUserServers(username);

      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/users/${username}/servers`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token')
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle user servers retrieval errors', async () => {
      const username = 'testuser';
      const mockError = new Error('User servers retrieval failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(getUserServers(username)).rejects.toThrow('User servers retrieval failed');
    });
  });

  describe('addUserToGroup', () => {
    test('should add user to group successfully', async () => {
      const username = 'testuser';
      const groupName = 'students';
      const mockResponse = {
        data: {
          success: true,
          message: 'User added to group successfully'
        }
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      const result = await addUserToGroup(username, groupName);

      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining(`/groups/${groupName}/users`),
        { users: [username] },
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token'),
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle add user to group errors', async () => {
      const username = 'testuser';
      const groupName = 'students';
      const mockError = new Error('Add user to group failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(addUserToGroup(username, groupName)).rejects.toThrow('Add user to group failed');
    });
  });

  describe('removeUserFromGroup', () => {
    test('should remove user from group successfully', async () => {
      const username = 'testuser';
      const groupName = 'students';
      const mockResponse = {
        data: {
          success: true,
          message: 'User removed from group successfully'
        }
      };

      axios.delete = jest.fn().mockResolvedValue(mockResponse);

      const result = await removeUserFromGroup(username, groupName);

      expect(axios.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/groups/${groupName}/users/${username}`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token')
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle remove user from group errors', async () => {
      const username = 'testuser';
      const groupName = 'students';
      const mockError = new Error('Remove user from group failed');
      axios.delete = jest.fn().mockRejectedValue(mockError);

      await expect(removeUserFromGroup(username, groupName)).rejects.toThrow('Remove user from group failed');
    });
  });

  describe('createGroup', () => {
    test('should create group successfully', async () => {
      const groupData = {
        name: 'newgroup',
        users: ['user1', 'user2']
      };

      const mockResponse = {
        data: {
          name: 'newgroup',
          users: ['user1', 'user2']
        }
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      const result = await createGroup(groupData);

      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/groups'),
        groupData,
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token'),
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle group creation errors', async () => {
      const groupData = {
        name: 'newgroup'
      };

      const mockError = new Error('Group creation failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(createGroup(groupData)).rejects.toThrow('Group creation failed');
    });
  });

  describe('deleteGroup', () => {
    test('should delete group successfully', async () => {
      const groupName = 'testgroup';
      const mockResponse = {
        data: {
          success: true,
          message: 'Group deleted successfully'
        }
      };

      axios.delete = jest.fn().mockResolvedValue(mockResponse);

      const result = await deleteGroup(groupName);

      expect(axios.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/groups/${groupName}`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token')
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle group deletion errors', async () => {
      const groupName = 'testgroup';
      const mockError = new Error('Group deletion failed');
      axios.delete = jest.fn().mockRejectedValue(mockError);

      await expect(deleteGroup(groupName)).rejects.toThrow('Group deletion failed');
    });
  });

  describe('listGroups', () => {
    test('should list groups successfully', async () => {
      const mockResponse = {
        data: [
          {
            name: 'students',
            users: ['user1', 'user2']
          },
          {
            name: 'instructors',
            users: ['instructor1']
          }
        ]
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await listGroups();

      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/groups'),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': expect.stringContaining('token')
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle group listing errors', async () => {
      const mockError = new Error('Group listing failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(listGroups()).rejects.toThrow('Group listing failed');
    });
  });
});
