import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('axios');
jest.mock('../../../src/config/logger.js');

import axios from 'axios';

// Import the service functions
import {
  createSandbox,
  deleteSandbox,
  getSandboxStatus,
  updateSandboxResources,
  listSandboxes,
  getSandboxLogs,
  restartSandbox,
  scaleSandbox
} from '../../../src/services/sandboxOrchestrator.js';

describe('Sandbox Orchestrator Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createSandbox', () => {
    test('should create sandbox successfully', async () => {
      const mockSandboxConfig = {
        userId: 'user-123',
        projectId: 'project-456',
        resources: {
          cpu: '1',
          memory: '2Gi',
          storage: '10Gi'
        },
        environment: 'python:3.9'
      };

      const mockResponse = {
        data: {
          id: 'sandbox-789',
          status: 'creating',
          url: 'https://sandbox-789.example.com',
          ...mockSandboxConfig
        }
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      const result = await createSandbox(mockSandboxConfig);

      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/sandboxes'),
        mockSandboxConfig,
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle sandbox creation errors', async () => {
      const mockSandboxConfig = {
        userId: 'user-123',
        projectId: 'project-456'
      };

      const mockError = new Error('Sandbox creation failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(createSandbox(mockSandboxConfig)).rejects.toThrow('Sandbox creation failed');
    });

    test('should validate required configuration', async () => {
      const invalidConfig = {
        // Missing required fields
      };

      await expect(createSandbox(invalidConfig)).rejects.toThrow();
    });
  });

  describe('deleteSandbox', () => {
    test('should delete sandbox successfully', async () => {
      const sandboxId = 'sandbox-789';
      const mockResponse = {
        data: {
          success: true,
          message: 'Sandbox deleted successfully'
        }
      };

      axios.delete = jest.fn().mockResolvedValue(mockResponse);

      const result = await deleteSandbox(sandboxId);

      expect(axios.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/sandboxes/${sandboxId}`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle sandbox deletion errors', async () => {
      const sandboxId = 'sandbox-789';
      const mockError = new Error('Sandbox deletion failed');
      axios.delete = jest.fn().mockRejectedValue(mockError);

      await expect(deleteSandbox(sandboxId)).rejects.toThrow('Sandbox deletion failed');
    });

    test('should handle non-existent sandbox', async () => {
      const sandboxId = 'non-existent';
      const mockResponse = {
        status: 404,
        data: {
          success: false,
          message: 'Sandbox not found'
        }
      };

      axios.delete = jest.fn().mockRejectedValue({
        response: mockResponse
      });

      await expect(deleteSandbox(sandboxId)).rejects.toThrow();
    });
  });

  describe('getSandboxStatus', () => {
    test('should get sandbox status successfully', async () => {
      const sandboxId = 'sandbox-789';
      const mockResponse = {
        data: {
          id: sandboxId,
          status: 'running',
          url: 'https://sandbox-789.example.com',
          resources: {
            cpu: '1',
            memory: '2Gi',
            storage: '10Gi'
          },
          createdAt: '2023-01-01T00:00:00Z',
          lastActivity: '2023-01-01T12:00:00Z'
        }
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await getSandboxStatus(sandboxId);

      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/sandboxes/${sandboxId}/status`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle status retrieval errors', async () => {
      const sandboxId = 'sandbox-789';
      const mockError = new Error('Status retrieval failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(getSandboxStatus(sandboxId)).rejects.toThrow('Status retrieval failed');
    });
  });

  describe('updateSandboxResources', () => {
    test('should update sandbox resources successfully', async () => {
      const sandboxId = 'sandbox-789';
      const resourceUpdate = {
        cpu: '2',
        memory: '4Gi',
        storage: '20Gi'
      };

      const mockResponse = {
        data: {
          id: sandboxId,
          status: 'updating',
          resources: resourceUpdate
        }
      };

      axios.patch = jest.fn().mockResolvedValue(mockResponse);

      const result = await updateSandboxResources(sandboxId, resourceUpdate);

      expect(axios.patch).toHaveBeenCalledWith(
        expect.stringContaining(`/sandboxes/${sandboxId}/resources`),
        resourceUpdate,
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle resource update errors', async () => {
      const sandboxId = 'sandbox-789';
      const resourceUpdate = {
        cpu: '2',
        memory: '4Gi'
      };

      const mockError = new Error('Resource update failed');
      axios.patch = jest.fn().mockRejectedValue(mockError);

      await expect(updateSandboxResources(sandboxId, resourceUpdate)).rejects.toThrow('Resource update failed');
    });

    test('should validate resource limits', async () => {
      const sandboxId = 'sandbox-789';
      const invalidResources = {
        cpu: '100', // Too high
        memory: '1000Gi' // Too high
      };

      await expect(updateSandboxResources(sandboxId, invalidResources)).rejects.toThrow();
    });
  });

  describe('listSandboxes', () => {
    test('should list sandboxes successfully', async () => {
      const mockResponse = {
        data: {
          sandboxes: [
            {
              id: 'sandbox-1',
              userId: 'user-123',
              status: 'running',
              createdAt: '2023-01-01T00:00:00Z'
            },
            {
              id: 'sandbox-2',
              userId: 'user-456',
              status: 'stopped',
              createdAt: '2023-01-02T00:00:00Z'
            }
          ],
          total: 2,
          page: 1,
          limit: 10
        }
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await listSandboxes({
        page: 1,
        limit: 10,
        status: 'running'
      });

      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/sandboxes'),
        expect.objectContaining({
          params: {
            page: 1,
            limit: 10,
            status: 'running'
          },
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle listing errors', async () => {
      const mockError = new Error('Listing failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(listSandboxes({})).rejects.toThrow('Listing failed');
    });

    test('should handle pagination parameters', async () => {
      const mockResponse = {
        data: {
          sandboxes: [],
          total: 0,
          page: 2,
          limit: 5
        }
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      await listSandboxes({
        page: 2,
        limit: 5
      });

      expect(axios.get).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          params: {
            page: 2,
            limit: 5
          }
        })
      );
    });
  });

  describe('getSandboxLogs', () => {
    test('should get sandbox logs successfully', async () => {
      const sandboxId = 'sandbox-789';
      const mockResponse = {
        data: {
          logs: [
            {
              timestamp: '2023-01-01T12:00:00Z',
              level: 'INFO',
              message: 'Sandbox started successfully'
            },
            {
              timestamp: '2023-01-01T12:01:00Z',
              level: 'ERROR',
              message: 'Failed to load some modules'
            }
          ],
          total: 2
        }
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await getSandboxLogs(sandboxId, {
        level: 'ERROR',
        limit: 50
      });

      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/sandboxes/${sandboxId}/logs`),
        expect.objectContaining({
          params: {
            level: 'ERROR',
            limit: 50
          },
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle log retrieval errors', async () => {
      const sandboxId = 'sandbox-789';
      const mockError = new Error('Log retrieval failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(getSandboxLogs(sandboxId)).rejects.toThrow('Log retrieval failed');
    });

    test('should handle log level filtering', async () => {
      const sandboxId = 'sandbox-789';
      const mockResponse = {
        data: {
          logs: [],
          total: 0
        }
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      await getSandboxLogs(sandboxId, {
        level: 'WARN'
      });

      expect(axios.get).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          params: {
            level: 'WARN'
          }
        })
      );
    });
  });

  describe('restartSandbox', () => {
    test('should restart sandbox successfully', async () => {
      const sandboxId = 'sandbox-789';
      const mockResponse = {
        data: {
          id: sandboxId,
          status: 'restarting',
          message: 'Sandbox restart initiated'
        }
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      const result = await restartSandbox(sandboxId);

      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining(`/sandboxes/${sandboxId}/restart`),
        {},
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle restart errors', async () => {
      const sandboxId = 'sandbox-789';
      const mockError = new Error('Restart failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(restartSandbox(sandboxId)).rejects.toThrow('Restart failed');
    });

    test('should handle restart with force option', async () => {
      const sandboxId = 'sandbox-789';
      const mockResponse = {
        data: {
          id: sandboxId,
          status: 'restarting',
          force: true
        }
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      await restartSandbox(sandboxId, { force: true });

      expect(axios.post).toHaveBeenCalledWith(
        expect.any(String),
        { force: true },
        expect.any(Object)
      );
    });
  });

  describe('scaleSandbox', () => {
    test('should scale sandbox successfully', async () => {
      const sandboxId = 'sandbox-789';
      const scaleConfig = {
        replicas: 3,
        resources: {
          cpu: '2',
          memory: '4Gi'
        }
      };

      const mockResponse = {
        data: {
          id: sandboxId,
          status: 'scaling',
          replicas: 3,
          resources: scaleConfig.resources
        }
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      const result = await scaleSandbox(sandboxId, scaleConfig);

      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining(`/sandboxes/${sandboxId}/scale`),
        scaleConfig,
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle scaling errors', async () => {
      const sandboxId = 'sandbox-789';
      const scaleConfig = {
        replicas: 5
      };

      const mockError = new Error('Scaling failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(scaleSandbox(sandboxId, scaleConfig)).rejects.toThrow('Scaling failed');
    });

    test('should validate scaling limits', async () => {
      const sandboxId = 'sandbox-789';
      const invalidScaleConfig = {
        replicas: 100 // Too high
      };

      await expect(scaleSandbox(sandboxId, invalidScaleConfig)).rejects.toThrow();
    });
  });
});
