import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/models/User.js');
jest.mock('../../../src/models/Role.js');
jest.mock('../../../src/models/Permission.js');
jest.mock('../../../src/config/logger.js');

import User from '../../../src/models/User.js';
import Role from '../../../src/models/Role.js';
import Permission from '../../../src/models/Permission.js';

// Import the middleware functions
import {
  requirePermissions,
  requireRoles,
  requireResourceAccess,
  hasPermission,
  hasRole
} from '../../../src/middlewares/rbac.js';

describe('RBAC Middleware', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      user: null,
      userPermissions: [],
      userRoles: [],
      params: {},
      body: {},
      headers: {}
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('requirePermissions', () => {
    test('should allow user with required permission', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userPermissions = ['course:create'];

      const middleware = requirePermissions('course:create');
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    test('should reject user without required permission', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userPermissions = ['course:read'];

      const middleware = requirePermissions('course:create');
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Insufficient permissions',
          message: expect.stringContaining('You do not have the required permissions')
        })
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('should allow super admin to bypass permission checks', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userRoles = ['super_admin'];
      mockRequest.userPermissions = [];

      const middleware = requirePermissions('course:create');
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    test('should reject unauthenticated user', () => {
      const middleware = requirePermissions('course:create');
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Authentication required',
          message: expect.stringContaining('User must be authenticated')
        })
      );
    });

    test('should handle multiple permissions with requireAll=true (default)', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userPermissions = ['course:create', 'course:update'];

      const middleware = requirePermissions(['course:create', 'course:update']);
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should reject when user missing any permission with requireAll=true', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userPermissions = ['course:create']; // missing course:update

      const middleware = requirePermissions(['course:create', 'course:update']);
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
    });

    test('should allow when user has any permission with requireAll=false', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userPermissions = ['course:create']; // has one of two

      const middleware = requirePermissions(['course:create', 'course:update'], { requireAll: false });
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('requireRoles', () => {
    test('should allow user with required role', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userRoles = ['instructor'];

      const middleware = requireRoles('instructor');
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    test('should reject user without required role', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userRoles = ['student'];

      const middleware = requireRoles('instructor');
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Insufficient role privileges',
          message: expect.stringContaining('You do not have the required role')
        })
      );
    });

    test('should allow super admin to bypass role checks', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userRoles = ['super_admin'];

      const middleware = requireRoles('instructor');
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should handle multiple roles with requireAll=true (default)', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userRoles = ['instructor', 'admin'];

      const middleware = requireRoles(['instructor', 'admin']);
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should allow when user has any role with requireAll=false', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userRoles = ['instructor']; // has one of two

      const middleware = requireRoles(['instructor', 'admin'], { requireAll: false });
      middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('requireResourceAccess', () => {
    test('should allow super admin access to any resource', async () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userRoles = ['super_admin'];
      mockRequest.params = { id: 'resource-1' };

      const middleware = requireResourceAccess('submission');
      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should allow admin access to any resource', async () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.userRoles = ['admin'];
      mockRequest.params = { id: 'resource-1' };

      const middleware = requireResourceAccess('submission');
      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should reject unauthenticated user', async () => {
      mockRequest.params = { id: 'resource-1' };

      const middleware = requireResourceAccess('submission');
      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Authentication required',
          message: expect.stringContaining('User must be authenticated')
        })
      );
    });

    test('should reject invalid resource type', async () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };
      mockRequest.params = { id: 'resource-1' };

      const middleware = requireResourceAccess('invalid_type');
      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Authorization error',
          message: 'Internal server error during resource access check'
        })
      );
    });
  });

  describe('hasPermission utility', () => {
    test('should return true for user with permission', () => {
      const userPermissions = ['course:create', 'course:read'];
      expect(hasPermission(userPermissions, 'course:create')).toBe(true);
    });

    test('should return true for super admin', () => {
      const userPermissions = ['super_admin'];
      expect(hasPermission(userPermissions, 'course:create')).toBe(true);
    });

    test('should return false for user without permission', () => {
      const userPermissions = ['course:read'];
      expect(hasPermission(userPermissions, 'course:create')).toBe(false);
    });
  });

  describe('hasRole utility', () => {
    test('should return true for user with role', () => {
      const userRoles = ['instructor', 'student'];
      expect(hasRole(userRoles, 'instructor')).toBe(true);
    });

    test('should return true for super admin', () => {
      const userRoles = ['super_admin'];
      expect(hasRole(userRoles, 'instructor')).toBe(true);
    });

    test('should return false for user without role', () => {
      const userRoles = ['student'];
      expect(hasRole(userRoles, 'instructor')).toBe(false);
    });
  });
});
