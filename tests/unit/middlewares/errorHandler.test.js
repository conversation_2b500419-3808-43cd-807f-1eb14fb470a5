import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/config/logger.js');

import logger from '../../../src/config/logger.js';

// Import the middleware functions
import {
  errorHandler,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ConflictError,
  BadRequestError,
  asyncHandler
} from '../../../src/middlewares/errorHandler.js';

describe('Error Handler Middleware', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      url: '/api/test',
      method: 'GET',
      ip: '127.0.0.1',
      get: jest.fn().mockReturnValue('Mozilla/5.0'),
      user: { email: '<EMAIL>' }
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('errorHandler', () => {
    test('should handle generic errors', () => {
      const error = new Error('Something went wrong');
      error.stack = 'Error stack trace';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(logger.error).toHaveBeenCalledWith('API Error:', {
        message: 'Something went wrong',
        stack: 'Error stack trace',
        url: '/api/test',
        method: 'GET',
        ip: '127.0.0.1',
        userAgent: 'Mozilla/5.0',
        user: '<EMAIL>'
      });

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Something went wrong',
        status: 500,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should handle ValidationError', () => {
      const error = new Error('Validation failed');
      error.name = 'ValidationError';
      error.errors = [
        { path: 'email', message: 'Email is required', value: null },
        { path: 'name', message: 'Name is required', value: '' }
      ];

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation error',
        status: 400,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET',
        details: [
          { field: 'email', message: 'Email is required', value: null },
          { field: 'name', message: 'Name is required', value: '' }
        ]
      });
    });

    test('should handle SequelizeUniqueConstraintError', () => {
      const error = new Error('Unique constraint violation');
      error.name = 'SequelizeUniqueConstraintError';
      error.errors = [
        { path: 'email', message: 'email must be unique', value: '<EMAIL>' }
      ];

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(409);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Resource already exists',
        status: 409,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET',
        details: [
          { field: 'email', message: 'email must be unique', value: '<EMAIL>' }
        ]
      });
    });

    test('should handle SequelizeForeignKeyConstraintError', () => {
      const error = new Error('Foreign key constraint violation');
      error.name = 'SequelizeForeignKeyConstraintError';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid reference to related resource',
        status: 400,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should handle SequelizeConnectionError', () => {
      const error = new Error('Database connection failed');
      error.name = 'SequelizeConnectionError';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(503);
      const responseCall = mockResponse.json.mock.calls[0][0];
      expect(responseCall.error).toBe('Service temporarily unavailable');
      expect(responseCall.status).toBe(503);
      expect(responseCall.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(responseCall.path).toBe('/api/test');
      expect(responseCall.method).toBe('GET');
      expect(responseCall.details).toBe('Database connection error');
    });

    test('should handle JsonWebTokenError', () => {
      const error = new Error('Invalid JWT token');
      error.name = 'JsonWebTokenError';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid authentication token',
        status: 401,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should handle TokenExpiredError', () => {
      const error = new Error('JWT token expired');
      error.name = 'TokenExpiredError';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Authentication token expired',
        status: 401,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should handle MulterError with LIMIT_FILE_SIZE', () => {
      const error = new Error('File too large');
      error.name = 'MulterError';
      error.code = 'LIMIT_FILE_SIZE';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'File size too large',
        status: 400,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should handle MulterError with LIMIT_FILE_COUNT', () => {
      const error = new Error('Too many files');
      error.name = 'MulterError';
      error.code = 'LIMIT_FILE_COUNT';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Too many files',
        status: 400,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should handle MulterError with unknown code', () => {
      const error = new Error('File upload error');
      error.name = 'MulterError';
      error.code = 'UNKNOWN_ERROR';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'File upload error',
        status: 400,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should handle custom errors with statusCode', () => {
      const error = new Error('Custom error');
      error.statusCode = 422;

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(422);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Custom error',
        status: 422,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should handle custom errors with status', () => {
      const error = new Error('Custom error');
      error.status = 418;

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(418);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Custom error',
        status: 418,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should handle errors without message', () => {
      const error = {};

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Internal server error',
        status: 500,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });
    });

    test('should include stack trace in development mode', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const error = new Error('Development error');
      error.stack = 'Development stack trace';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Development error',
        status: 500,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET',
        stack: 'Development stack trace'
      });

      process.env.NODE_ENV = originalEnv;
    });

    test('should not include stack trace in production mode', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const error = new Error('Production error');
      error.stack = 'Production stack trace';

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Production error',
        status: 500,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET'
      });

      process.env.NODE_ENV = originalEnv;
    });

    test('should handle request without user', () => {
      const requestWithoutUser = { ...mockRequest };
      delete requestWithoutUser.user;

      const error = new Error('Test error');

      errorHandler(error, requestWithoutUser, mockResponse, mockNext);

      expect(logger.error).toHaveBeenCalledWith('API Error:', {
        message: 'Test error',
        stack: expect.any(String),
        url: '/api/test',
        method: 'GET',
        ip: '127.0.0.1',
        userAgent: 'Mozilla/5.0',
        user: 'anonymous'
      });
    });

    test('should include details for non-500 errors in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const error = new Error('Validation failed');
      error.name = 'ValidationError';
      error.errors = [
        { path: 'email', message: 'Email is required', value: null }
      ];

      errorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation error',
        status: 400,
        timestamp: expect.any(String),
        path: '/api/test',
        method: 'GET',
        details: [
          { field: 'email', message: 'Email is required', value: null }
        ]
      });

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Custom Error Classes', () => {
    test('ValidationError should have correct properties', () => {
      const error = new ValidationError('Invalid data', [{ field: 'email', message: 'Invalid email' }]);

      expect(error.name).toBe('ValidationError');
      expect(error.message).toBe('Invalid data');
      expect(error.statusCode).toBe(400);
      expect(error.details).toEqual([{ field: 'email', message: 'Invalid email' }]);
    });

    test('NotFoundError should have correct properties', () => {
      const error = new NotFoundError('User not found');

      expect(error.name).toBe('NotFoundError');
      expect(error.message).toBe('User not found');
      expect(error.statusCode).toBe(404);
    });

    test('NotFoundError should use default message', () => {
      const error = new NotFoundError();

      expect(error.message).toBe('Resource not found');
      expect(error.statusCode).toBe(404);
    });

    test('UnauthorizedError should have correct properties', () => {
      const error = new UnauthorizedError('Invalid credentials');

      expect(error.name).toBe('UnauthorizedError');
      expect(error.message).toBe('Invalid credentials');
      expect(error.statusCode).toBe(401);
    });

    test('UnauthorizedError should use default message', () => {
      const error = new UnauthorizedError();

      expect(error.message).toBe('Unauthorized access');
      expect(error.statusCode).toBe(401);
    });

    test('ForbiddenError should have correct properties', () => {
      const error = new ForbiddenError('Insufficient permissions');

      expect(error.name).toBe('ForbiddenError');
      expect(error.message).toBe('Insufficient permissions');
      expect(error.statusCode).toBe(403);
    });

    test('ForbiddenError should use default message', () => {
      const error = new ForbiddenError();

      expect(error.message).toBe('Access forbidden');
      expect(error.statusCode).toBe(403);
    });

    test('ConflictError should have correct properties', () => {
      const error = new ConflictError('Resource already exists');

      expect(error.name).toBe('ConflictError');
      expect(error.message).toBe('Resource already exists');
      expect(error.statusCode).toBe(409);
    });

    test('ConflictError should use default message', () => {
      const error = new ConflictError();

      expect(error.message).toBe('Resource conflict');
      expect(error.statusCode).toBe(409);
    });

    test('BadRequestError should have correct properties', () => {
      const error = new BadRequestError('Invalid request data');

      expect(error.name).toBe('BadRequestError');
      expect(error.message).toBe('Invalid request data');
      expect(error.statusCode).toBe(400);
    });

    test('BadRequestError should use default message', () => {
      const error = new BadRequestError();

      expect(error.message).toBe('Bad request');
      expect(error.statusCode).toBe(400);
    });
  });

  describe('asyncHandler', () => {
    test('should handle successful async operations', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');
      const handler = asyncHandler(mockFn);

      await handler(mockRequest, mockResponse, mockNext);

      expect(mockFn).toHaveBeenCalledWith(mockRequest, mockResponse, mockNext);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('should handle async operation errors', async () => {
      const mockError = new Error('Async operation failed');
      const mockFn = jest.fn().mockRejectedValue(mockError);
      const handler = asyncHandler(mockFn);

      await handler(mockRequest, mockResponse, mockNext);

      expect(mockFn).toHaveBeenCalledWith(mockRequest, mockResponse, mockNext);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    test('should handle synchronous errors', async () => {
      const mockError = new Error('Sync operation failed');
      const mockFn = jest.fn().mockImplementation(() => {
        throw mockError;
      });
      const handler = asyncHandler(mockFn);

      try {
        await handler(mockRequest, mockResponse, mockNext);
      } catch (error) {
        // The asyncHandler should catch this and pass it to next
      }

      expect(mockFn).toHaveBeenCalledWith(mockRequest, mockResponse, mockNext);
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });
  });
});
