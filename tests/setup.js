// Test setup file
// Set test environment
process.env.NODE_ENV = 'test';

// Make Jest globals available (Jest automatically provides these)
// No need to import jest as it's globally available in test environment

// Set test environment variables
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_NAME_TEST = 'bits_datascience_test';
process.env.DB_USERNAME = 'postgres';
process.env.DB_PASSWORD = 'postgres';

process.env.PORT = '5002';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.SESSION_SECRET = 'test-session-secret-key-for-testing-only';

process.env.AWS_REGION = 'us-east-1';
process.env.AWS_ACCESS_KEY_ID = 'test-access-key';
process.env.AWS_SECRET_ACCESS_KEY = 'test-secret-key';
process.env.S3_BUCKET = 'test-bucket';

process.env.JUPYTERHUB_URL = 'http://localhost:8000';
process.env.JUPYTERHUB_TOKEN = 'test-token';

process.env.REDIS_URL = 'redis://localhost:6379';
process.env.LOG_LEVEL = 'error';

// Global test utilities
global.testUtils = {
  // Helper to create test data
  createTestUser: async (User, userData = {}) => {
    const defaultData = {
      id: '550e8400-e29b-41d4-a716-446655440001',
      email: '<EMAIL>',
      name: 'Test User',
      password: 'hashedPassword123',
      isActive: true,
      ...userData
    };
    return await User.create(defaultData);
  },

  // Helper to create test project
  createTestProject: async (Project, User, projectData = {}) => {
    const user = await global.testUtils.createTestUser(User);
    const defaultData = {
      id: '550e8400-e29b-41d4-a716-446655440002',
      title: 'Test Project',
      description: 'A test project',
      createdBy: user.id,
      isActive: true,
      ...projectData
    };
    return await Project.create(defaultData);
  },

  // Helper to clean up test data
  cleanupTestData: async (models) => {
    for (const model of models) {
      await model.destroy({ where: {}, force: true });
    }
  }
};
