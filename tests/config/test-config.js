// Test configuration
export const testConfig = {
  database: {
    host: 'localhost',
    port: 5432,
    name: 'bits_datascience_test',
    user: 'postgres',
    password: 'postgres'
  },
  server: {
    port: 5002,
    jwtSecret: 'test-jwt-secret-key-for-testing-only',
    sessionSecret: 'test-session-secret-key-for-testing-only'
  },
  aws: {
    region: 'us-east-1',
    accessKeyId: 'test-access-key',
    secretAccessKey: 'test-secret-key',
    s3Bucket: 'test-bucket'
  },
  jupyterhub: {
    url: 'http://localhost:8000',
    token: 'test-token'
  },
  redis: {
    url: 'redis://localhost:6379'
  },
  logging: {
    level: 'error'
  }
};
