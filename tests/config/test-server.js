import express from 'express';
import session from 'express-session';
import SequelizeStoreInit from 'connect-session-sequelize';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { testSequelize } from './test-database.js';
import logger from '../../src/config/logger.js';
import setupSwagger from '../../src/config/swagger.js';
import { verifyToken } from '../../src/middlewares/auth.js';
import { errorHandler, NotFoundError } from '../../src/middlewares/errorHandler.js';

// Import routes
import authRoutes from '../../src/routes/auth.js';
import courseRoutes from '../../src/routes/courses.js';
import gradeRoutes from '../../src/routes/grades.js';
import lmsRoutes from '../../src/routes/lms.js';
import ltiRoutes from '../../src/routes/lti.js';
import projectRoutes from '../../src/routes/projects.js';
import roleRoutes from '../../src/routes/roles.js';
import s3Routes from '../../src/routes/s3.js';
import submissionRoutes from '../../src/routes/submissions.js';
import userRoutes from '../../src/routes/users.js';

// Import associations
import '../../src/models/associations.js';

let testApp = null;
let testServer = null;

export const createTestServer = async () => {
  if (testApp) {
    return testApp;
  }

  const app = express();
  const PORT = process.env.PORT || 5002;

  // Override the database connection for controllers
  // This ensures all controllers use the test database
  const originalRequire = require;
  const Module = require('module');
  
  // Store the original sequelize instance
  const { sequelize: originalSequelize } = await import('../../src/config/database.js');
  
  // Replace the sequelize export with our test sequelize
  Object.defineProperty(originalSequelize, 'authenticate', {
    value: testSequelize.authenticate.bind(testSequelize),
    writable: false
  });
  
  // Replace the sequelize instance in the database config
  Object.defineProperty(await import('../../src/config/database.js'), 'sequelize', {
    value: testSequelize,
    writable: false
  });

  // Session store setup with test database
  const SequelizeStore = SequelizeStoreInit(session.Store);
  const sessionStore = new SequelizeStore({
    db: testSequelize,
    tableName: 'sessions',
    checkExpirationInterval: 15 * 60 * 1000,
    expiration: 24 * 60 * 60 * 1000
  });

  // Middleware
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "https:"],
        frameSrc: ["'self'", "https:"],
        frameAncestors: ["'self'", "https:"]
      }
    }
  }));

  app.use(cors({
    origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  }));

  // HTTP logging (minimal for tests)
  app.use(morgan('combined', {
    stream: { write: message => logger.debug(message.trim()) }
  }));

  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ extended: true, limit: '50mb' }));

  // Session middleware
  app.use(session({
    secret: process.env.SESSION_SECRET || 'test-session-secret',
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false, // Always false for tests
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000
    },
    name: 'bits.session.id'
  }));

  // Health check
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'test'
    });
  });

  // Routes
  app.use('/api/auth', authRoutes);
  app.use('/api/users', verifyToken, userRoutes);
  app.use('/api/courses', verifyToken, courseRoutes);
  app.use('/api/projects', verifyToken, projectRoutes);
  app.use('/api/submissions', verifyToken, submissionRoutes);
  app.use('/api/grades', verifyToken, gradeRoutes);
  app.use('/api/roles', verifyToken, roleRoutes);
  app.use('/api/s3', verifyToken, s3Routes);
  app.use('/api/lms', verifyToken, lmsRoutes);

  // LTI routes (no authentication required for initial endpoints)
  app.use('/api/lti', ltiRoutes);

  // Swagger
  setupSwagger(app);

  // Error handling
  app.use((req, res, next) => {
    next(new NotFoundError());
  });
  app.use(errorHandler);

  // Start test server
  testServer = app.listen(PORT, () => {
    logger.info(`Test server running on port ${PORT}`);
  });

  testApp = app;
  return app;
};

export const closeTestServer = async () => {
  if (testServer) {
    return new Promise((resolve) => {
      testServer.close(() => {
        testServer = null;
        testApp = null;
        logger.info('Test server closed');
        resolve();
      });
    });
  }
};

export const getTestApp = () => testApp;
