import { Sequelize } from 'sequelize';
import logger from '../../src/config/logger.js';

// Test database configuration
const testConfig = {
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME_TEST || 'bits_datascience_test',
  host: process.env.DB_HOST || '127.0.0.1',
  port: Number(process.env.DB_PORT) || 5432,
  dialect: 'postgres',
  logging: false, // Disable logging for tests
  pool: { max: 5, min: 0, acquire: 30000, idle: 10000 },
  define: { 
    timestamps: true, 
    underscored: true, 
    paranoid: true, 
    freezeTableName: true 
  },
  ssl: false,
};

// Create a connection to the default database to create test database
const createTestDatabaseConnection = () => {
  return new Sequelize(
    'postgres', // Connect to default postgres database
    testConfig.username,
    testConfig.password,
    {
      host: testConfig.host,
      port: testConfig.port,
      dialect: testConfig.dialect,
      logging: false,
    }
  );
};

// Create test database connection
export const testSequelize = new Sequelize(
  testConfig.database,
  testConfig.username,
  testConfig.password,
  {
    host: testConfig.host,
    port: testConfig.port,
    dialect: testConfig.dialect,
    logging: testConfig.logging,
    pool: testConfig.pool,
    define: testConfig.define,
    dialectOptions: testConfig.ssl ? { ssl: testConfig.ssl } : {},
  }
);

// Database setup utilities
export const setupTestDatabase = async () => {
  try {
    // First, try to create the test database if it doesn't exist
    const defaultConnection = createTestDatabaseConnection();
    
    try {
      await defaultConnection.authenticate();
      logger.info('✅ Connected to PostgreSQL server');
      
      // Try to create the test database
      try {
        await defaultConnection.query(`CREATE DATABASE "${testConfig.database}";`);
        logger.info(`✅ Created test database: ${testConfig.database}`);
      } catch (createError) {
        if (createError.message.includes('already exists')) {
          logger.info(`✅ Test database already exists: ${testConfig.database}`);
        } else {
          throw createError;
        }
      }
    } finally {
      await defaultConnection.close();
    }
    
    // Now connect to the test database
    await testSequelize.authenticate();
    logger.info('✅ Test database connection established');
    
    // Sync all models (create tables)
    await testSequelize.sync({ force: true });
    logger.info('✅ Test database tables created');
    
    return true;
  } catch (error) {
    logger.error('❌ Test database setup failed:', error);
    throw error;
  }
};

// Database teardown utilities
export const teardownTestDatabase = async () => {
  try {
    // Close all connections
    await testSequelize.close();
    logger.info('✅ Test database connection closed');
    return true;
  } catch (error) {
    logger.error('❌ Test database teardown failed:', error);
    throw error;
  }
};

// Clean database between tests
export const cleanTestDatabase = async () => {
  try {
    // Get all model names
    const modelNames = Object.keys(testSequelize.models);
    
    // Delete all data from all tables (in reverse order to handle foreign keys)
    const reverseModelNames = modelNames.reverse();
    
    for (const modelName of reverseModelNames) {
      const model = testSequelize.models[modelName];
      await model.destroy({ 
        where: {}, 
        force: true, // Hard delete for tests
        truncate: { cascade: true }
      });
    }
    
    logger.debug('✅ Test database cleaned');
  } catch (error) {
    logger.error('❌ Test database cleanup failed:', error);
    throw error;
  }
};

// Import all models for testing
export const importTestModels = async () => {
  try {
    // Import all models
    const models = await import('../../src/models/associations.js');
    return models;
  } catch (error) {
    logger.error('❌ Failed to import test models:', error);
    throw error;
  }
};

// Create test data utilities
export const createTestData = async (models) => {
  const { User, Role, Permission, Course, Project } = models;
  
  try {
    // Create test roles
    const adminRole = await Role.create({
      name: 'admin',
      description: 'Administrator role'
    });
    
    const instructorRole = await Role.create({
      name: 'instructor',
      description: 'Instructor role'
    });
    
    const studentRole = await Role.create({
      name: 'student',
      description: 'Student role'
    });
    
    // Create test permissions
    const permissions = await Permission.bulkCreate([
      { name: 'user:create', description: 'Create users' },
      { name: 'user:read', description: 'Read users' },
      { name: 'user:update', description: 'Update users' },
      { name: 'user:delete', description: 'Delete users' },
      { name: 'course:create', description: 'Create courses' },
      { name: 'course:read', description: 'Read courses' },
      { name: 'course:update', description: 'Update courses' },
      { name: 'course:delete', description: 'Delete courses' },
      { name: 'project:create', description: 'Create projects' },
      { name: 'project:read', description: 'Read projects' },
      { name: 'project:update', description: 'Update projects' },
      { name: 'project:delete', description: 'Delete projects' }
    ]);
    
    // Create test users
    const adminUser = await User.create({
      email: '<EMAIL>',
      name: 'Admin User',
      password: 'hashedPassword123',
      isActive: true
    });
    
    const instructorUser = await User.create({
      email: '<EMAIL>',
      name: 'Instructor User',
      password: 'hashedPassword123',
      isActive: true
    });
    
    const studentUser = await User.create({
      email: '<EMAIL>',
      name: 'Student User',
      password: 'hashedPassword123',
      isActive: true
    });
    
    // Create test course
    const testCourse = await Course.create({
      title: 'Test Course',
      description: 'A test course for integration testing',
      instructorId: instructorUser.id,
      isActive: true
    });
    
    // Create test project
    const testProject = await Project.create({
      title: 'Test Project',
      description: 'A test project for integration testing',
      courseId: testCourse.id,
      createdBy: instructorUser.id,
      isActive: true
    });
    
    logger.info('✅ Test data created successfully');
    
    return {
      users: { adminUser, instructorUser, studentUser },
      roles: { adminRole, instructorRole, studentRole },
      permissions,
      course: testCourse,
      project: testProject
    };
  } catch (error) {
    logger.error('❌ Failed to create test data:', error);
    throw error;
  }
};

export default testConfig;
