import { jest } from '@jest/globals';
import request from 'supertest';
import app from '../../../src/app.js';
import { sequelize } from '../../../src/config/database.js';
import { User, Role } from '../../../src/models/associations.js';
import { generateToken } from '../../../src/middlewares/auth.js';

// Mock Redis for integration tests
jest.mock('ioredis', () => {
  const mockRedis = {
    setex: jest.fn().mockResolvedValue('OK'),
    get: jest.fn().mockResolvedValue(null),
    del: jest.fn().mockResolvedValue(1),
    keys: jest.fn().mockResolvedValue([]),
    memory: jest.fn().mockResolvedValue(1024000),
    ttl: jest.fn().mockResolvedValue(3600),
    quit: jest.fn().mockResolvedValue('OK')
  };
  
  return jest.fn().mockImplementation(() => mockRedis);
});

describe('Logout Integration Tests', () => {
  let testUser;
  let adminUser;
  let userToken;
  let adminToken;

  beforeAll(async () => {
    // Sync database
    await sequelize.sync({ force: true });

    // Create test roles
    const studentRole = await Role.create({
      name: 'student',
      description: 'Student role'
    });

    const adminRole = await Role.create({
      name: 'admin',
      description: 'Admin role'
    });

    // Create test users
    testUser = await User.create({
      email: '<EMAIL>',
      name: 'Test User',
      password: 'password123',
      status: 'active'
    });

    adminUser = await User.create({
      email: '<EMAIL>',
      name: 'Admin User',
      password: 'admin123',
      status: 'active'
    });

    // Assign roles
    await testUser.addRole(studentRole);
    await adminUser.addRole(adminRole);

    // Generate tokens
    userToken = generateToken(testUser);
    adminToken = generateToken(adminUser);
  });

  afterAll(async () => {
    await sequelize.close();
  });

  afterEach(async () => {
    // Clear all users except test users
    await User.destroy({
      where: {
        email: {
          [sequelize.Op.notIn]: ['<EMAIL>', '<EMAIL>']
        }
      }
    });
  });

  describe('POST /api/auth/logout', () => {
    test('should logout user successfully and blacklist token', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Logged out successfully',
        timestamp: expect.any(String)
      });

      // Verify token is blacklisted by trying to use it again
      const protectedResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(401);

      expect(protectedResponse.body).toEqual({
        error: 'Token invalidated',
        message: 'This token has been revoked. Please login again.'
      });
    });

    test('should handle logout without token gracefully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .expect(401);

      expect(response.body).toEqual({
        error: 'Access denied',
        message: 'No token provided or invalid format'
      });
    });

    test('should handle invalid token format', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', 'InvalidFormat token')
        .expect(401);

      expect(response.body).toEqual({
        error: 'Access denied',
        message: 'No token provided or invalid format'
      });
    });

    test('should handle expired token', async () => {
      // Create an expired token
      const expiredToken = generateToken(testUser, '1s'); // 1 second expiry
      
      // Wait for token to expire
      await new Promise(resolve => setTimeout(resolve, 1100));

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body).toEqual({
        error: 'Token expired',
        message: 'Please login again'
      });
    });
  });

  describe('POST /api/auth/logout-all', () => {
    test('should logout from all devices successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout-all')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Logged out from all devices successfully',
        timestamp: expect.any(String)
      });
    });

    test('should require authentication', async () => {
      const response = await request(app)
        .post('/api/auth/logout-all')
        .expect(401);

      expect(response.body).toEqual({
        error: 'Access denied',
        message: 'No token provided or invalid format'
      });
    });
  });

  describe('GET /api/auth/logout/stats', () => {
    test('should get logout stats for admin user', async () => {
      const response = await request(app)
        .get('/api/auth/logout/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        stats: {
          totalBlacklistedTokens: expect.any(Number),
          memoryUsage: expect.any(Number)
        },
        timestamp: expect.any(String)
      });
    });

    test('should reject non-admin users', async () => {
      const response = await request(app)
        .get('/api/auth/logout/stats')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body).toEqual({
        success: false,
        error: 'Access denied',
        message: 'Admin permission required'
      });
    });

    test('should require authentication', async () => {
      const response = await request(app)
        .get('/api/auth/logout/stats')
        .expect(401);

      expect(response.body).toEqual({
        error: 'Access denied',
        message: 'No token provided or invalid format'
      });
    });
  });

  describe('Token Blacklisting Flow', () => {
    test('should prevent access after logout', async () => {
      // First, access a protected endpoint
      const beforeLogout = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(beforeLogout.body.success).toBe(true);

      // Logout
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      // Try to access protected endpoint again
      const afterLogout = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(401);

      expect(afterLogout.body).toEqual({
        error: 'Token invalidated',
        message: 'This token has been revoked. Please login again.'
      });
    });

    test('should allow new token after logout', async () => {
      // Logout with current token
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      // Generate new token
      const newToken = generateToken(testUser);

      // New token should work
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${newToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Multiple Logout Scenarios', () => {
    test('should handle multiple logout requests', async () => {
      // First logout
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      // Second logout with same token (should still work)
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      // Token should still be invalidated
      await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(401);
    });

    test('should handle concurrent logout requests', async () => {
      const logoutPromises = [
        request(app)
          .post('/api/auth/logout')
          .set('Authorization', `Bearer ${userToken}`),
        request(app)
          .post('/api/auth/logout')
          .set('Authorization', `Bearer ${userToken}`),
        request(app)
          .post('/api/auth/logout')
          .set('Authorization', `Bearer ${userToken}`)
      ];

      const responses = await Promise.all(logoutPromises);

      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      // Token should be invalidated
      await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(401);
    });
  });

  describe('Error Handling', () => {
    test('should handle malformed JWT token', async () => {
      const malformedToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature';

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${malformedToken}`)
        .expect(401);

      expect(response.body).toEqual({
        error: 'Invalid token',
        message: 'Token is malformed'
      });
    });

    test('should handle missing user in token', async () => {
      // Create token with non-existent user ID
      const invalidUserToken = generateToken({ id: 'non-existent-user-id' });

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${invalidUserToken}`)
        .expect(401);

      expect(response.body).toEqual({
        error: 'Access denied',
        message: 'User not found'
      });
    });
  });
});
