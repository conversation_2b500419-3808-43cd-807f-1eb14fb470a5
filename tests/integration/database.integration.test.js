import { getTestModels, getTestData, isDatabaseAvailable } from './setup.js';
import logger from '../../src/config/logger.js';

describe('Database Integration Tests', () => {
  let testModels;
  let testData;
  let dbAvailable;

  beforeAll(async () => {
    try {
      // Get test models and data
      testModels = getTestModels();
      testData = getTestData();
      dbAvailable = isDatabaseAvailable();
      
      logger.info(`✅ Database integration test setup complete (Database: ${dbAvailable ? 'Available' : 'Mock Mode'})`);
    } catch (error) {
      logger.error('❌ Database integration test setup failed:', error);
      throw error;
    }
  });

  describe('Database Connection', () => {
    test('should have test models loaded', () => {
      expect(testModels).toBeDefined();
      expect(testModels.User).toBeDefined();
      expect(testModels.Role).toBeDefined();
      expect(testModels.Permission).toBeDefined();
      expect(testModels.Course).toBeDefined();
      expect(testModels.Project).toBeDefined();
    });

    test('should have test data created', () => {
      expect(testData).toBeDefined();
      expect(testData.users).toBeDefined();
      expect(testData.roles).toBeDefined();
      expect(testData.permissions).toBeDefined();
      expect(testData.course).toBeDefined();
      expect(testData.project).toBeDefined();
    });

    test('should indicate database availability', () => {
      expect(typeof dbAvailable).toBe('boolean');
      if (!dbAvailable) {
        logger.warn('⚠️ Running in mock mode - no real database connection');
      }
    });
  });

  describe('User Model Operations', () => {
    test('should create a new user', async () => {
      if (dbAvailable) {
        const newUser = await testModels.User.create({
          email: '<EMAIL>',
          name: 'Test User',
          password: 'hashedPassword123',
          isActive: true
        });

        expect(newUser).toBeDefined();
        expect(newUser.email).toBe('<EMAIL>');
        expect(newUser.name).toBe('Test User');
        expect(newUser.isActive).toBe(true);
      } else {
        // Mock test
        testModels.User.create.mockResolvedValue({
          id: 'mock-user-123',
          email: '<EMAIL>',
          name: 'Test User',
          isActive: true
        });

        const newUser = await testModels.User.create({
          email: '<EMAIL>',
          name: 'Test User',
          password: 'hashedPassword123',
          isActive: true
        });

        expect(testModels.User.create).toHaveBeenCalledWith({
          email: '<EMAIL>',
          name: 'Test User',
          password: 'hashedPassword123',
          isActive: true
        });
        expect(newUser.email).toBe('<EMAIL>');
      }
    });

    test('should find existing user', async () => {
      if (dbAvailable) {
        const user = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        expect(user).toBeDefined();
        expect(user.email).toBe('<EMAIL>');
        expect(user.name).toBe('Admin User');
      } else {
        // Mock test
        testModels.User.findOne.mockResolvedValue({
          id: 'admin-123',
          email: '<EMAIL>',
          name: 'Admin User'
        });

        const user = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        expect(testModels.User.findOne).toHaveBeenCalledWith({
          where: { email: '<EMAIL>' }
        });
        expect(user.email).toBe('<EMAIL>');
      }
    });

    test('should update user', async () => {
      if (dbAvailable) {
        const user = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        const updatedUser = await user.update({
          name: 'Updated Instructor'
        });

        expect(updatedUser.name).toBe('Updated Instructor');
      } else {
        // Mock test
        const mockUser = {
          update: jest.fn().mockResolvedValue({
            id: 'instructor-123',
            email: '<EMAIL>',
            name: 'Updated Instructor'
          })
        };
        testModels.User.findOne.mockResolvedValue(mockUser);

        const user = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        const updatedUser = await user.update({
          name: 'Updated Instructor'
        });

        expect(user.update).toHaveBeenCalledWith({
          name: 'Updated Instructor'
        });
        expect(updatedUser.name).toBe('Updated Instructor');
      }
    });

    test('should delete user', async () => {
      if (dbAvailable) {
        const user = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        await user.destroy();

        const deletedUser = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        expect(deletedUser).toBeNull();
      } else {
        // Mock test
        const mockUser = {
          destroy: jest.fn().mockResolvedValue(true)
        };
        testModels.User.findOne
          .mockResolvedValueOnce(mockUser) // First call returns user
          .mockResolvedValueOnce(null); // Second call returns null (deleted)

        const user = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        await user.destroy();

        const deletedUser = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        expect(user.destroy).toHaveBeenCalled();
        expect(deletedUser).toBeNull();
      }
    });
  });

  describe('Role Model Operations', () => {
    test('should create a new role', async () => {
      if (dbAvailable) {
        const newRole = await testModels.Role.create({
          name: 'test_role',
          description: 'A test role'
        });

        expect(newRole).toBeDefined();
        expect(newRole.name).toBe('test_role');
        expect(newRole.description).toBe('A test role');
      } else {
        // Mock test
        testModels.Role.create.mockResolvedValue({
          id: 'mock-role-123',
          name: 'test_role',
          description: 'A test role'
        });

        const newRole = await testModels.Role.create({
          name: 'test_role',
          description: 'A test role'
        });

        expect(testModels.Role.create).toHaveBeenCalledWith({
          name: 'test_role',
          description: 'A test role'
        });
        expect(newRole.name).toBe('test_role');
      }
    });

    test('should find existing role', async () => {
      if (dbAvailable) {
        const role = await testModels.Role.findOne({
          where: { name: 'admin' }
        });

        expect(role).toBeDefined();
        expect(role.name).toBe('admin');
        expect(role.description).toBe('Administrator role');
      } else {
        // Mock test
        testModels.Role.findOne.mockResolvedValue({
          id: 'role-1',
          name: 'admin',
          description: 'Administrator role'
        });

        const role = await testModels.Role.findOne({
          where: { name: 'admin' }
        });

        expect(testModels.Role.findOne).toHaveBeenCalledWith({
          where: { name: 'admin' }
        });
        expect(role.name).toBe('admin');
      }
    });
  });

  describe('Course Model Operations', () => {
    test('should create a new course', async () => {
      if (dbAvailable) {
        const instructor = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        const newCourse = await testModels.Course.create({
          title: 'Test Course 2',
          description: 'Another test course',
          instructorId: instructor.id,
          isActive: true
        });

        expect(newCourse).toBeDefined();
        expect(newCourse.title).toBe('Test Course 2');
        expect(newCourse.instructorId).toBe(instructor.id);
      } else {
        // Mock test
        testModels.User.findOne.mockResolvedValue({
          id: 'instructor-123',
          email: '<EMAIL>'
        });
        testModels.Course.create.mockResolvedValue({
          id: 'course-456',
          title: 'Test Course 2',
          instructorId: 'instructor-123',
          isActive: true
        });

        const instructor = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        const newCourse = await testModels.Course.create({
          title: 'Test Course 2',
          description: 'Another test course',
          instructorId: instructor.id,
          isActive: true
        });

        expect(newCourse.title).toBe('Test Course 2');
        expect(newCourse.instructorId).toBe('instructor-123');
      }
    });

    test('should find course with instructor', async () => {
      if (dbAvailable) {
        const course = await testModels.Course.findOne({
          where: { title: 'Test Course' },
          include: [{
            model: testModels.User,
            as: 'instructor'
          }]
        });

        expect(course).toBeDefined();
        expect(course.title).toBe('Test Course');
        expect(course.instructor).toBeDefined();
        expect(course.instructor.email).toBe('<EMAIL>');
      } else {
        // Mock test
        testModels.Course.findOne.mockResolvedValue({
          id: 'course-123',
          title: 'Test Course',
          instructor: {
            id: 'instructor-123',
            email: '<EMAIL>'
          }
        });

        const course = await testModels.Course.findOne({
          where: { title: 'Test Course' },
          include: [{
            model: testModels.User,
            as: 'instructor'
          }]
        });

        expect(course.title).toBe('Test Course');
        expect(course.instructor.email).toBe('<EMAIL>');
      }
    });
  });

  describe('Project Model Operations', () => {
    test('should create a new project', async () => {
      if (dbAvailable) {
        const course = await testModels.Course.findOne({
          where: { title: 'Test Course' }
        });

        const instructor = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        const newProject = await testModels.Project.create({
          title: 'Test Project 2',
          description: 'Another test project',
          courseId: course.id,
          createdBy: instructor.id,
          isActive: true
        });

        expect(newProject).toBeDefined();
        expect(newProject.title).toBe('Test Project 2');
        expect(newProject.courseId).toBe(course.id);
        expect(newProject.createdBy).toBe(instructor.id);
      } else {
        // Mock test
        testModels.Course.findOne.mockResolvedValue({
          id: 'course-123',
          title: 'Test Course'
        });
        testModels.User.findOne.mockResolvedValue({
          id: 'instructor-123',
          email: '<EMAIL>'
        });
        testModels.Project.create.mockResolvedValue({
          id: 'project-456',
          title: 'Test Project 2',
          courseId: 'course-123',
          createdBy: 'instructor-123',
          isActive: true
        });

        const course = await testModels.Course.findOne({
          where: { title: 'Test Course' }
        });

        const instructor = await testModels.User.findOne({
          where: { email: '<EMAIL>' }
        });

        const newProject = await testModels.Project.create({
          title: 'Test Project 2',
          description: 'Another test project',
          courseId: course.id,
          createdBy: instructor.id,
          isActive: true
        });

        expect(newProject.title).toBe('Test Project 2');
        expect(newProject.courseId).toBe('course-123');
        expect(newProject.createdBy).toBe('instructor-123');
      }
    });

    test('should find project with course', async () => {
      if (dbAvailable) {
        const project = await testModels.Project.findOne({
          where: { title: 'Test Project' },
          include: [{
            model: testModels.Course,
            as: 'course'
          }]
        });

        expect(project).toBeDefined();
        expect(project.title).toBe('Test Project');
        expect(project.course).toBeDefined();
        expect(project.course.title).toBe('Test Course');
      } else {
        // Mock test
        testModels.Project.findOne.mockResolvedValue({
          id: 'project-123',
          title: 'Test Project',
          course: {
            id: 'course-123',
            title: 'Test Course'
          }
        });

        const project = await testModels.Project.findOne({
          where: { title: 'Test Project' },
          include: [{
            model: testModels.Course,
            as: 'course'
          }]
        });

        expect(project.title).toBe('Test Project');
        expect(project.course.title).toBe('Test Course');
      }
    });
  });

  describe('Associations', () => {
    test('should have proper user-role associations', async () => {
      if (dbAvailable) {
        const user = await testModels.User.findOne({
          where: { email: '<EMAIL>' },
          include: [{
            model: testModels.Role,
            as: 'roles'
          }]
        });

        expect(user).toBeDefined();
        // Note: User-role associations would need to be set up in the test data
      } else {
        // Mock test
        testModels.User.findOne.mockResolvedValue({
          id: 'admin-123',
          email: '<EMAIL>',
          roles: [
            { id: 'role-1', name: 'admin' }
          ]
        });

        const user = await testModels.User.findOne({
          where: { email: '<EMAIL>' },
          include: [{
            model: testModels.Role,
            as: 'roles'
          }]
        });

        expect(user).toBeDefined();
        expect(user.roles).toBeDefined();
        expect(user.roles.length).toBeGreaterThan(0);
      }
    });

    test('should have proper course-project associations', async () => {
      if (dbAvailable) {
        const course = await testModels.Course.findOne({
          where: { title: 'Test Course' },
          include: [{
            model: testModels.Project,
            as: 'projects'
          }]
        });

        expect(course).toBeDefined();
        expect(course.projects).toBeDefined();
        expect(course.projects.length).toBeGreaterThan(0);
      } else {
        // Mock test
        testModels.Course.findOne.mockResolvedValue({
          id: 'course-123',
          title: 'Test Course',
          projects: [
            { id: 'project-123', title: 'Test Project' }
          ]
        });

        const course = await testModels.Course.findOne({
          where: { title: 'Test Course' },
          include: [{
            model: testModels.Project,
            as: 'projects'
          }]
        });

        expect(course).toBeDefined();
        expect(course.projects).toBeDefined();
        expect(course.projects.length).toBeGreaterThan(0);
      }
    });
  });
});
