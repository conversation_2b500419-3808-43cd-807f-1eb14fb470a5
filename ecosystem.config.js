module.exports = {
  apps: [
    {
      name: 'bits-datascience-backend',
      script: 'src/server.js',
      cwd: '/app',
      instances: process.env.NODE_ENV === 'production' ? 'max' : 1,
      exec_mode: process.env.NODE_ENV === 'production' ? 'cluster' : 'fork',
      
      // Environment variables
      env_production: {
        NODE_ENV: 'production',
        PORT: 5000,
        LOG_LEVEL: 'info'
      },
      
      env_development: {
        NODE_ENV: 'development',
        PORT: 5000,
        LOG_LEVEL: 'debug'
      },
      
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 5000,
        LOG_LEVEL: 'info'
      },
      
      // Process management
      autorestart: true,
      watch: process.env.NODE_ENV === 'development',
      watch_delay: 1000,
      ignore_watch: ['node_modules', 'logs', 'uploads', 'temp'],
      
      // Resource limits
      max_memory_restart: '1G',
      max_restarts: 10,
      min_uptime: '10s',
      
      // Logging
      log_file: 'logs/pm2.log',
      error_file: 'logs/pm2-error.log',
      out_file: 'logs/pm2-out.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Health monitoring
      health_check_delay: 30000,
      health_check_grace_period: 10000,
      
      // Kill timeout
      kill_timeout: 5000,
      
      // Source map support
      source_map_support: true,
      
      // Instance variables
      instance_var: 'INSTANCE_ID',
      
      // Advanced options
      node_args: '--max-old-space-size=1024',
      
      // Cron restart (daily at 2 AM)
      cron_restart: '0 2 * * *',
      
      // Startup delay
      wait_ready: true,
      listen_timeout: 10000,
      
      // Graceful shutdown
      shutdown_with_message: true,
      
      // Error handling
      min_uptime: '10s',
      max_restarts: 5,
      
      // Monitoring
      pmx: true,
      
      // Disable automatic restart on crash in development
      autorestart: process.env.NODE_ENV !== 'development'
    }
  ],
  
  deploy: {
    production: {
      user: 'deploy',
      host: ['production-server.bits.edu'],
      ref: 'origin/main',
      repo: '**************:bits-pilani/datascience-platform-backend.git',
      path: '/var/www/bits-datascience-backend',
      'post-deploy': 'npm install && npm run db:migrate && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt-get update && apt-get install -y git nodejs npm postgresql-client redis-tools'
    },
    
    staging: {
      user: 'deploy',
      host: ['staging-server.bits.edu'],
      ref: 'origin/develop',
      repo: '**************:bits-pilani/datascience-platform-backend.git',
      path: '/var/www/bits-datascience-backend-staging',
      'post-deploy': 'npm install && npm run db:migrate && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': 'apt-get update && apt-get install -y git nodejs npm postgresql-client redis-tools'
    }
  }
};