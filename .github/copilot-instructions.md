# Copilot Instructions for BITS-DataScience Projects Platform Backend

## Project Overview
- **Purpose:** Backend API for a Data Science project platform with LTI 1.3 (D2L-Brightspace), AWS S3, and robust RBAC.
- **Stack:** Node.js (Express), PostgreSQL (Sequelize), Redis, AWS S3, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>.
- **Key Integrations:**
  - LTI 1.3 (see `/src/controllers/ltiController.js`, `/src/services/ltiService.js`)
  - AWS S3 (see `/src/services/s3Service.js`)
  - Google OAuth (see `/src/controllers/authController.js`)

## Architecture & Patterns
- **API Structure:** RESTful, versioned under `/api/` (see `/src/routes/`).
- **RBAC:** Roles/permissions in `/src/models/Role.js`, `/src/models/Permission.js`, `/src/middlewares/rbac.js`.
- **Session Management:** Express-session with Sequelize store (`/src/config/database.js`).
- **Error Handling:** Centralized in `/src/middlewares/errorHandler.js`.
- **Swagger Docs:** Configured in `/src/config/swagger.js`.
- **Health Checks:** `/health` endpoint, see `/src/server.js`.
- **Logging:** Winston (`/src/config/logger.js`), HTTP logs via Morgan.
- **Testing:** Jest, with structure in `tests/` (unit, integration, fixtures).

## Developer Workflows
- **Dev Server:** `npm run dev` (nodemon, hot reload)
- **Build/Prod:** `docker-compose -f docker-compose.dev.yml up` (dev), `docker-compose -f docker-compose.prod.yml up -d` (prod)
- **Migrations:** `npm run db:migrate`, `npm run db:seed`, `npm run db:reset`
- **Lint/Format:** `npm run lint`, `npm run format`
- **Tests:** `npm run test`, `npm run test:unit`, `npm run test:integration`
- **Docs:** `npm run docs:generate`, `npm run docs:serve`
- **Deployment:** `./scripts/deploy.sh [staging|production]`, or `npm run pm2:start`

## Conventions & Tips
- **Env Vars:** Copy `.env.example` to `.env` and configure for local/dev.
- **Session Cookies:** Secure in prod, httpOnly always.
- **Swagger:** API docs at `/api-docs` (dev: http://localhost:5000/api-docs).
- **Postman:** Use `/docs/BITS_DataScience_Platform.postman_collection.json` for API testing.
- **Error Handling:** Always use `next(err)` for async route errors.
- **RBAC:** Use `verifyToken` and RBAC middleware for protected routes.
- **LTI:** Register platforms via `/api/lti/config` and manage keys in env.
- **S3:** All file ops via presigned URLs, never direct S3 access from client.

## Key Files & Directories
- `/src/server.js`: Entry point, server setup, graceful shutdown
- `/src/routes/`: API route definitions
- `/src/controllers/`: Business logic for each resource
- `/src/models/`: Sequelize models and associations
- `/src/services/`: LTI, S3, and other integrations
- `/src/middlewares/`: Auth, RBAC, error handling
- `/docs/`: API docs, integration guides, Postman collection
- `/scripts/`: DB, deployment, and health scripts

---

For more, see `README.md` and `/docs/`. If unsure about a pattern, check existing controllers/services for examples.
