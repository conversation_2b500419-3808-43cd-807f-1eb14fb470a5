version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: bits-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: bits_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - bits-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: bits-backend
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 5000
      DATABASE_URL: ********************************************/bits_platform
      JWT_SECRET: your-jwt-secret-change-in-production
      SESSION_SECRET: your-session-secret-change-in-production
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./temp:/app/temp
    ports:
      - "5000:5000"
    networks:
      - bits-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 20s
      timeout: 5s
      retries: 3

# Volumes
volumes:
  postgres_data:
    driver: local

# Networks
networks:
  bits-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
